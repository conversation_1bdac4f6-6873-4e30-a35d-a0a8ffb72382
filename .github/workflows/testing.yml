name: Testing

on:
  push:
    tags:
      - 'testing/*'

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set build variables
      run: |
        echo "GIT_VER=${GITHUB_REF##*/}" >> ${GITHUB_ENV}
        echo "GIT_TAG=${GITHUB_REF##refs/tags/}" >> ${GITHUB_ENV}
        cat ${GITHUB_ENV}

    - name: Upload Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: rotorflight-lua-ethos-suite-testing-${{ env.GIT_VER }}
        path: scripts

    - name: Delete tag
      run: git push origin :${GITHUB_REF}
