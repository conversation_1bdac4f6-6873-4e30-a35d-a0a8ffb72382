name: Release

on:
  push:
    tags:
      - 'release/*'

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
    - name: Check out repository
      uses: actions/checkout@v4

    - name: Set build variables
      run: |
        GIT_VER=${GITHUB_REF##*/}
        GIT_TAG=${GITHUB_REF##refs/tags/}
        if [[ ${GIT_VER} =~ ^[0-9]+[.][0-9]+[.][0-9]+[-][A-Za-z0-9]+ ]]
        then
          GH_TYPE='Release Candidate'
        else
          GH_TYPE='Release'
        fi
        echo "GIT_VER=${GIT_VER}" >> ${GITHUB_ENV}
        echo "GIT_TAG=${GIT_TAG}" >> ${GITHUB_ENV}
        echo "GH_TYPE=${GH_TYPE}" >> ${GITHUB_ENV}
        cat ${GITHUB_ENV}

    - name: Make release package
      run: zip -q -r -9 "rotorflight-lua-ethos-suite-${{ env.GIT_VER }}.zip" scripts

    - name: Package combined soundpack
      run: |
        SOUND_DIR="bin/sound-generator/soundpack"
        OUTPUT_DIR="${GITHUB_WORKSPACE}/tmp"
        mkdir -p "$OUTPUT_DIR"

        ZIP_NAME="rotorflight-lua-ethos-suite-soundpack-${GIT_VER}.zip"
        (cd "$SOUND_DIR" && zip -r "$OUTPUT_DIR/$ZIP_NAME" .)
        echo "Created $OUTPUT_DIR/$ZIP_NAME"

    - name: Move soundpack to root
      run: mv "${GITHUB_WORKSPACE}/tmp/"*.zip "${GITHUB_WORKSPACE}/"             

    - name: Create Release
      run: |
        .github/scripts/extract-release-notes.py "${{ env.GIT_VER }}" Releases.md > Notes.md
        gh release create ${{ env.GIT_TAG }} --notes-file Notes.md --title "Rotorflight Lua Suite for Ethos - ${{ env.GH_TYPE }} ${{ env.GIT_VER }}" *.zip
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}