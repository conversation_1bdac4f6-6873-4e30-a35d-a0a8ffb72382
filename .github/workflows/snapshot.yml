name: Snapshot

on:
  push:
    tags:
      - 'snapshot/*'

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
    - name: Check out repository
      uses: actions/checkout@v4

    - name: Set build variables
      run: |
        echo "GIT_VER=${GITHUB_REF##*/}" >> ${GITHUB_ENV}
        echo "GIT_TAG=${GITHUB_REF##refs/tags/}" >> ${GITHUB_ENV}
        cat ${GITHUB_ENV}

    - name: Make snapshot package
      run: zip -q -r -9 "rotorflight-lua-ethos-suite-${{ env.GIT_VER }}.zip" scripts

    - name: Package combined soundpack
      run: |
        SOUND_DIR="bin/sound-generator/soundpack"
        OUTPUT_DIR="${GITHUB_WORKSPACE}/tmp"
        mkdir -p "$OUTPUT_DIR"

        ZIP_NAME="rotorflight-lua-ethos-suite-soundpack-${GIT_VER}.zip"
        (cd "$SOUND_DIR" && zip -r "$OUTPUT_DIR/$ZIP_NAME" .)
        echo "Created $OUTPUT_DIR/$ZIP_NAME"

    - name: Move soundpack to root
      run: mv "${GITHUB_WORKSPACE}/tmp/"*.zip "${GITHUB_WORKSPACE}/"        

    - name: Create Snapshot
      run: |
        .github/scripts/extract-release-notes.py "${{ env.GIT_VER }}" Releases.md > Notes.md
        gh release create ${{ env.GIT_TAG }} --prerelease --notes-file Notes.md --title "Rotorflight Lua Suite for Ethos - Snapshot ${{ env.GIT_VER }}" *.zip 
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}