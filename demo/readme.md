**RFSUITE DEMO**

<PERSON><PERSON> have been making some fantastic new features.

This tool allows you to run a fully setup 'rfsuite' install within your browser.

To view the demo; click the link below


[Launch RFSuite Demo - English](https://ethos.studio1247.com/nightly16/X20PRO_FCC?backup=https:%2F%2Fgithub.com%2Frotorflight%2Frotorflight-lua-ethos-suite%2Fraw%2Frefs%2Fheads%2Fmaster%2Fdemo%2FDEMO.zip&reset=all&language=en&sidenav=false)

[Launch RFSuite Demo - German](https://ethos.studio1247.com/nightly16/X20PRO_FCC?backup=https:%2F%2Fgithub.com%2Frotorflight%2Frotorflight-lua-ethos-suite%2Fraw%2Frefs%2Fheads%2Fmaster%2Fdemo%2FDEMO.zip&reset=all&language=de&sidenav=false)

[Launch RFSuite Demo - Dutch](https://ethos.studio1247.com/nightly16/X20PRO_FCC?backup=https:%2F%2Fgithub.com%2Frotorflight%2Frotorflight-lua-ethos-suite%2Fraw%2Frefs%2Fheads%2Fmaster%2Fdemo%2FDEMO.zip&reset=all&language=nl&sidenav=false)

[Launch RFSuite Demo - Spanish](https://ethos.studio1247.com/nightly16/X20PRO_FCC?backup=https:%2F%2Fgithub.com%2Frotorflight%2Frotorflight-lua-ethos-suite%2Fraw%2Frefs%2Fheads%2Fmaster%2Fdemo%2FDEMO.zip&reset=all&language=es&sidenav=false)

Please be patient and allow the page to load - without clicking on any of the links on the page.