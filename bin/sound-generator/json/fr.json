[{"file": "app/timeout.wav", "english": "Timed out", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "app/powercycleesc.wav", "english": "Please power cycle the speed controller", "translation": "Veuillez redemarrée le controleur de vitesse", "needs_translation": false}, {"file": "app/soverideen.wav", "english": "Servo overide - enabled", "translation": "Commande servo - activé", "needs_translation": false}, {"file": "app/soveridedis.wav", "english": "<PERSON><PERSON> overide - disabled", "translation": "Commande servo - désactivé", "needs_translation": false}, {"file": "app/eraseflash.wav", "english": "Erasing Data Flash", "translation": "Effacement de la mémoire flash", "needs_translation": false}, {"file": "app/moverideen.wav", "english": "Mixer overide - enabled", "translation": "Commande mixeur - activé", "needs_translation": false}, {"file": "app/moveridedis.wav", "english": "Mixer overide - disabled", "translation": "Commande mixeur - désactiv<PERSON>", "needs_translation": false}, {"file": "events/alerts/lowvoltage.wav", "english": "Low Voltage", "translation": "Tension faible", "needs_translation": false}, {"file": "events/alerts/lowfuel.wav", "english": "Low Fuel", "translation": "Carburant faible", "needs_translation": false}, {"file": "events/alerts/armed.wav", "english": "Armed", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "events/alerts/disarmed.wav", "english": "<PERSON><PERSON><PERSON>", "translation": "Désarmé", "needs_translation": false}, {"file": "events/alerts/profile.wav", "english": "Profile", "translation": "Profil", "needs_translation": false}, {"file": "events/alerts/rates.wav", "english": "Rates", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "events/alerts/elapsed.wav", "english": "Timer Elapsed", "translation": "Minuteur ecoule", "needs_translation": false}, {"file": "events/alerts/becvolt.wav", "english": "BEC Voltage Warning", "translation": "Avertissement tension BEC", "needs_translation": false}, {"file": "events/alerts/esctemp.wav", "english": "ESC Temperature Warning", "translation": "Avertissement temperature ESC", "needs_translation": false}, {"file": "events/alerts/rxvolt.wav", "english": "RX Batt Voltage Warning", "translation": null, "needs_translation": true}, {"file": "events/gov/off.wav", "english": "Governor - Off", "translation": "Gouverneur <PERSON> <PERSON><PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "events/gov/idle.wav", "english": "Governor <PERSON> <PERSON><PERSON>", "translation": "Gouverneur - <PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "events/gov/spoolup.wav", "english": "Governor <PERSON> <PERSON><PERSON><PERSON>", "translation": "Gouverneur - Accélération", "needs_translation": false}, {"file": "events/gov/recovery.wav", "english": "Governor - <PERSON>", "translation": "Gouverneur - Récupération", "needs_translation": false}, {"file": "events/gov/active.wav", "english": "Governor - Active", "translation": "Gouverneur - Actif", "needs_translation": false}, {"file": "events/gov/thr-off.wav", "english": "Governor - <PERSON><PERSON><PERSON><PERSON>", "translation": "Gouverneur - Accélération coupé", "needs_translation": false}, {"file": "events/gov/lost-hs.wav", "english": "Governor - <PERSON> Headspeed", "translation": "Gouverneur - Vitesse rotor perdue", "needs_translation": false}, {"file": "events/gov/autorot.wav", "english": "Governor - Auto Rotation", "translation": "Gouverneur - Autorotation", "needs_translation": false}, {"file": "events/gov/bailout.wav", "english": "Governor <PERSON> <PERSON><PERSON><PERSON>", "translation": "Gouverneur - Sauvetage", "needs_translation": false}, {"file": "events/gov/disabled.wav", "english": "Governor - Disabled", "translation": "Gouverneur - Désactivé", "needs_translation": false}, {"file": "events/gov/disarmed.wav", "english": "Governor - <PERSON><PERSON><PERSON>", "translation": "Gouverneur - Désarmé", "needs_translation": false}, {"file": "events/gov/unknown.wav", "english": "Governor - Unknown", "translation": "Gouverneur - <PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/pitch.wav", "english": "Pitch", "translation": "Tangage", "needs_translation": false}, {"file": "adjfunctions/roll.wav", "english": "Roll", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/yaw.wav", "english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/rc.wav", "english": "RC", "translation": "Radiocommande", "needs_translation": false}, {"file": "adjfunctions/rate.wav", "english": "Rate", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/p.wav", "english": "P", "translation": "P", "needs_translation": false}, {"file": "adjfunctions/i.wav", "english": "I", "translation": "I", "needs_translation": false}, {"file": "adjfunctions/d.wav", "english": "D", "translation": "D", "needs_translation": false}, {"file": "adjfunctions/f.wav", "english": "F", "translation": "F", "needs_translation": false}, {"file": "adjfunctions/o.wav", "english": "O", "translation": "O", "needs_translation": false}, {"file": "adjfunctions/gain.wav", "english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/cw.wav", "english": "CW", "translation": "<PERSON>s horaire", "needs_translation": false}, {"file": "adjfunctions/ccw.wav", "english": "CCW", "translation": "Sens <PERSON>", "needs_translation": false}, {"file": "adjfunctions/cyclic.wav", "english": "Cyclic", "translation": "Cyclique", "needs_translation": false}, {"file": "adjfunctions/ff.wav", "english": "Feed Forward", "translation": "Avance anticipatrice", "needs_translation": false}, {"file": "adjfunctions/collective.wav", "english": "Collective", "translation": "Pas collectif", "needs_translation": false}, {"file": "adjfunctions/dyn.wav", "english": "Dynamic", "translation": "Dynamique", "needs_translation": false}, {"file": "adjfunctions/decay.wav", "english": "Decay", "translation": "Atténuation", "needs_translation": false}, {"file": "adjfunctions/gyro.wav", "english": "Gyro", "translation": "Gyroscope", "needs_translation": false}, {"file": "adjfunctions/cutoff.wav", "english": "Cut Off", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/dterm.wav", "english": "d term", "translation": "Terme D", "needs_translation": false}, {"file": "adjfunctions/rescue.wav", "english": "Rescue", "translation": "Sauvetage", "needs_translation": false}, {"file": "adjfunctions/climb.wav", "english": "Climb", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/hover.wav", "english": "Hover", "translation": "Vol stationnaire", "needs_translation": false}, {"file": "adjfunctions/alt.wav", "english": "Altitude", "translation": "Altitude", "needs_translation": false}, {"file": "adjfunctions/angle.wav", "english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/level.wav", "english": "Level", "translation": "Niveau", "needs_translation": false}, {"file": "adjfunctions/horizon.wav", "english": "horizon", "translation": "Horizon", "needs_translation": false}, {"file": "adjfunctions/acro.wav", "english": "Acro", "translation": "Acro", "needs_translation": false}, {"file": "adjfunctions/gov.wav", "english": "Governor", "translation": "Gouverneur", "needs_translation": false}, {"file": "adjfunctions/crossc.wav", "english": "Cross Coupling", "translation": "Couplage crois<PERSON>", "needs_translation": false}, {"file": "adjfunctions/acc.wav", "english": "Accelerometer", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/trim.wav", "english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/setpoint.wav", "english": "Setpoint", "translation": "Consigne", "needs_translation": false}, {"file": "adjfunctions/precomp.wav", "english": "Precomp", "translation": "Précompensation", "needs_translation": false}, {"file": "adjfunctions/inertia.wav", "english": "Inertia", "translation": "Inertie", "needs_translation": false}, {"file": "adjfunctions/boost.wav", "english": "Boost", "translation": "Amplification", "needs_translation": false}, {"file": "adjfunctions/filter.wav", "english": "Filter", "translation": "Filtre", "needs_translation": false}, {"file": "adjfunctions/deadband.wav", "english": "Deadband", "translation": "Zone morte", "needs_translation": false}, {"file": "status/alerts/current.wav", "english": "Current", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "status/alerts/esc.wav", "english": "Esc Temperature", "translation": "Temperature ESC", "needs_translation": false}, {"file": "status/alerts/mcu.wav", "english": "MCU Temperature", "translation": "Temperature Processeur", "needs_translation": false}, {"file": "status/alerts/fuel.wav", "english": "Fuel", "translation": "Carburant", "needs_translation": false}, {"file": "status/alerts/headspeed.wav", "english": "Head Speed", "translation": "Vitesse rotor", "needs_translation": false}, {"file": "status/alerts/lowfuel.wav", "english": "Low Fuel", "translation": "Carburant faible", "needs_translation": false}, {"file": "status/alerts/lowvoltage.wav", "english": "Low Voltage", "translation": "Tension faible", "needs_translation": false}, {"file": "status/alerts/lq.wav", "english": "Link Quality", "translation": "Qualité du Signal", "needs_translation": false}, {"file": "status/alerts/remaining.wav", "english": "remaining", "translation": "Restant", "needs_translation": false}, {"file": "status/alerts/timer.wav", "english": "timer", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "status/alerts/voltage.wav", "english": "voltage", "translation": "Tension", "needs_translation": false}]