[{"file": "app/timeout.wav", "english": "Timed out", "translation": "<PERSON>ie<PERSON>", "needs_translation": false}, {"file": "app/powercycleesc.wav", "english": "Please power cycle the speed controller", "translation": "Apague y encienda el variador de velocidad", "needs_translation": false}, {"file": "app/soverideen.wav", "english": "Servo overide - enabled", "translation": "Toma de servo - activada", "needs_translation": false}, {"file": "app/soveridedis.wav", "english": "<PERSON><PERSON> overide - disabled", "translation": "Toma de servo - desactivada", "needs_translation": false}, {"file": "app/eraseflash.wav", "english": "Erasing Data Flash", "translation": "<PERSON><PERSON>ndo memoria flash", "needs_translation": false}, {"file": "app/moverideen.wav", "english": "Mixer overide - enabled", "translation": "Toma de mezclador - activada", "needs_translation": false}, {"file": "app/moveridedis.wav", "english": "Mixer overide - disabled", "translation": "Toma de mezclador - desactivada", "needs_translation": false}, {"file": "events/alerts/lowvoltage.wav", "english": "Low Voltage", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "events/alerts/lowfuel.wav", "english": "Low Fuel", "translation": "Combustible Bajo", "needs_translation": false}, {"file": "events/alerts/armed.wav", "english": "Armed", "translation": "Armado", "needs_translation": false}, {"file": "events/alerts/disarmed.wav", "english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "events/alerts/profile.wav", "english": "Profile", "translation": "Perfil", "needs_translation": false}, {"file": "events/alerts/rates.wav", "english": "Rates", "translation": "Tasa<PERSON>", "needs_translation": false}, {"file": "events/alerts/elapsed.wav", "english": "Timer Elapsed", "translation": "Transcurrido <PERSON>r", "needs_translation": false}, {"file": "events/alerts/becvolt.wav", "english": "BEC Voltage Warning", "translation": "Advertencia Voltaje BEC", "needs_translation": false}, {"file": "events/alerts/esctemp.wav", "english": "ESC Temperature Warning", "translation": "Advertencia Temperatura ESC", "needs_translation": false}, {"file": "events/alerts/rxvolt.wav", "english": "RX Batt Voltage Warning", "translation": null, "needs_translation": true}, {"file": "events/gov/off.wav", "english": "Governor - Off", "translation": "Gov<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "events/gov/idle.wav", "english": "Governor <PERSON> <PERSON><PERSON>", "translation": "Govérnor - Ralentí", "needs_translation": false}, {"file": "events/gov/spoolup.wav", "english": "Governor <PERSON> <PERSON><PERSON><PERSON>", "translation": "Govérnor <PERSON> <PERSON><PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "events/gov/recovery.wav", "english": "Governor - <PERSON>", "translation": "Govérnor - Recuperación", "needs_translation": false}, {"file": "events/gov/active.wav", "english": "Governor - Active", "translation": "Govérnor - Activo", "needs_translation": false}, {"file": "events/gov/thr-off.wav", "english": "Governor - <PERSON><PERSON><PERSON><PERSON>", "translation": "Govérnor - <PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "events/gov/lost-hs.wav", "english": "Governor - <PERSON> Headspeed", "translation": "Govérnor - Pérdida de Velocidad del Rotor", "needs_translation": false}, {"file": "events/gov/autorot.wav", "english": "Governor - Auto Rotation", "translation": "Govérnor - Auto Rotación", "needs_translation": false}, {"file": "events/gov/bailout.wav", "english": "Governor <PERSON> <PERSON><PERSON><PERSON>", "translation": "Govérnor - Rescate", "needs_translation": false}, {"file": "events/gov/disabled.wav", "english": "Governor - Disabled", "translation": "Govérnor <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "events/gov/disarmed.wav", "english": "Governor - <PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "events/gov/unknown.wav", "english": "Governor - Unknown", "translation": "Govérnor - Desconocido", "needs_translation": false}, {"file": "adjfunctions/pitch.wav", "english": "Pitch", "translation": "Cabeceo", "needs_translation": false}, {"file": "adjfunctions/roll.wav", "english": "Roll", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/yaw.wav", "english": "Yaw", "translation": "Rotación", "needs_translation": false}, {"file": "adjfunctions/rc.wav", "english": "RC", "translation": "RC", "needs_translation": false}, {"file": "adjfunctions/rate.wav", "english": "Rate", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/p.wav", "english": "P", "translation": "P", "needs_translation": false}, {"file": "adjfunctions/i.wav", "english": "I", "translation": "I", "needs_translation": false}, {"file": "adjfunctions/d.wav", "english": "D", "translation": "D", "needs_translation": false}, {"file": "adjfunctions/f.wav", "english": "F", "translation": "F", "needs_translation": false}, {"file": "adjfunctions/o.wav", "english": "O", "translation": "O", "needs_translation": false}, {"file": "adjfunctions/gain.wav", "english": "<PERSON><PERSON>", "translation": "Ganancia", "needs_translation": false}, {"file": "adjfunctions/cw.wav", "english": "CW", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/ccw.wav", "english": "CCW", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/cyclic.wav", "english": "Cyclic", "translation": "Cíclico", "needs_translation": false}, {"file": "adjfunctions/ff.wav", "english": "Feed Forward", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/collective.wav", "english": "Collective", "translation": "Colectivo", "needs_translation": false}, {"file": "adjfunctions/dyn.wav", "english": "Dynamic", "translation": "Dinámico", "needs_translation": false}, {"file": "adjfunctions/decay.wav", "english": "Decay", "translation": "Decadencia", "needs_translation": false}, {"file": "adjfunctions/gyro.wav", "english": "Gyro", "translation": "Giro", "needs_translation": false}, {"file": "adjfunctions/cutoff.wav", "english": "Cut Off", "translation": "Corte", "needs_translation": false}, {"file": "adjfunctions/dterm.wav", "english": "d term", "translation": "<PERSON><PERSON><PERSON><PERSON> d", "needs_translation": false}, {"file": "adjfunctions/rescue.wav", "english": "Rescue", "translation": "Rescate", "needs_translation": false}, {"file": "adjfunctions/climb.wav", "english": "Climb", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/hover.wav", "english": "Hover", "translation": "So<PERSON>vuelo", "needs_translation": false}, {"file": "adjfunctions/alt.wav", "english": "Altitude", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/angle.wav", "english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/level.wav", "english": "Level", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "adjfunctions/horizon.wav", "english": "horizon", "translation": "horizonte", "needs_translation": false}, {"file": "adjfunctions/acro.wav", "english": "Acro", "translation": "Acro", "needs_translation": false}, {"file": "adjfunctions/gov.wav", "english": "Governor", "translation": "Govérnor", "needs_translation": false}, {"file": "adjfunctions/crossc.wav", "english": "Cross Coupling", "translation": "Acoplamiento Cruzado", "needs_translation": false}, {"file": "adjfunctions/acc.wav", "english": "Accelerometer", "translation": "Acelerómetro", "needs_translation": false}, {"file": "adjfunctions/trim.wav", "english": "<PERSON><PERSON>", "translation": "Recorte", "needs_translation": false}, {"file": "adjfunctions/setpoint.wav", "english": "Setpoint", "translation": "<PERSON>unto de a<PERSON>", "needs_translation": false}, {"file": "adjfunctions/precomp.wav", "english": "Precomp", "translation": "Precompensación", "needs_translation": false}, {"file": "adjfunctions/inertia.wav", "english": "Inertia", "translation": "Inercia", "needs_translation": false}, {"file": "adjfunctions/boost.wav", "english": "Boost", "translation": "<PERSON>mpul<PERSON>", "needs_translation": false}, {"file": "adjfunctions/filter.wav", "english": "Filter", "translation": "Filtro", "needs_translation": false}, {"file": "adjfunctions/deadband.wav", "english": "Deadband", "translation": "Zona muerta", "needs_translation": false}, {"file": "status/alerts/current.wav", "english": "Current", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": false}, {"file": "status/alerts/esc.wav", "english": "Esc Temperature", "translation": "Temperatura del variador", "needs_translation": false}, {"file": "status/alerts/mcu.wav", "english": "MCU Temperature", "translation": "Temperatura del procesador", "needs_translation": false}, {"file": "status/alerts/fuel.wav", "english": "Fuel", "translation": "Combustible", "needs_translation": false}, {"file": "status/alerts/headspeed.wav", "english": "Head Speed", "translation": "Velocidad del Rotor", "needs_translation": false}, {"file": "status/alerts/lowfuel.wav", "english": "Low Fuel", "translation": "Combustible Bajo", "needs_translation": false}, {"file": "status/alerts/lowvoltage.wav", "english": "Low Voltage", "translation": "<PERSON><PERSON>", "needs_translation": false}, {"file": "status/alerts/lq.wav", "english": "Link Quality", "translation": "Calidad de la Conexión", "needs_translation": false}, {"file": "status/alerts/remaining.wav", "english": "remaining", "translation": "restante", "needs_translation": false}, {"file": "status/alerts/timer.wav", "english": "timer", "translation": "timer", "needs_translation": false}, {"file": "status/alerts/voltage.wav", "english": "voltage", "translation": "voltaje", "needs_translation": false}]