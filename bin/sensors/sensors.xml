<Sensors>

    <Group name="Flight Control">
        <Sensor name="armflags" label="Arm"  type="bool" default="0"  multiplier="1" unit="" />
        <Sensor name="governor" label="Governor" type="select">
            <Option value="0" label="OFF" />
            <Option value="1" label="IDLE" />
            <Option value="2" label="SPOOLUP" />
            <Option value="3" label="RECOVERY" />
            <Option value="4" label="ACTIVE" />
            <Option value="5" label="THROTTLE-OFF" />
            <Option value="6" label="LOST HEADSPEED" />
            <Option value="7" label="AUTOROTATION" />
            <Option value="8" label="BAILOUT" />
            <Option value="100" label="DISABLED" />
            <Option value="101" label="DISAMED" />
        </Sensor>
        <Sensor name="rpm" label="Headspeed" round="true" type="range" default="1800" min="0" max="2000" rand="10" multiplier="1" unit="rpm" />
    </Group>

    <Group name="Power System">
        <Sensor name="voltage" label="Main Voltage" type="range" min="0" max="25.2" rand="0" multiplier="100" unit="V" default="24.2" />
        <Sensor name="bec_voltage" label="BEC Voltage" type="range" rand="5" min="4.0" max="8.0" multiplier="100" unit="V" default="8.0" />
        <Sensor name="current" label="Current" type="range" rand="5" min="0" max="200" multiplier="1" unit="A" />
        <Sensor name="consumption" label="Consumption" round="true" type="range" default="0" min="0" max="5000" rand="0" multiplier="1" unit="mAh" />
        <Sensor name="fuel" label="Fuel" round="true" type="range" default="0" min="0" max="100" rand="0" multiplier="1" unit="%" />
    </Group>

    <Group name="Flight Profiles">

        <Sensor name="pid_profile" label="PID Profile" type="select">
            <Option value="1" label="1" />
            <Option value="2" label="2" />
            <Option value="3" label="3" />
            <Option value="4" label="4" />
            <Option value="5" label="5" />
            <Option value="6" label="6" />
        </Sensor>
        <Sensor name="rate_profile" label="Rate Profile" type="select">
            <Option value="1" label="1" />
            <Option value="2" label="2" />
            <Option value="3" label="3" />
            <Option value="4" label="4" />
            <Option value="5" label="5" />
            <Option value="6" label="6" />
        </Sensor>
    </Group>

    <Group name="Other Sensors">
        <Sensor name="temp_esc" label="ESC Temp" round="true" type="range" rand="5" min="10" max="80" default="50" unit="°C" />
        <Sensor name="temp_mcu" label="MCU Temp" round="true" type="range" rand="5" min="10" max="80" default="30" unit="°C" />
        <Sensor name="throttle_percent" label="Throttle %" rand="5" round="true" type="range" min="0" max="100" unit="%" default="80" />
        <Sensor name="altitude" label="Altitude" rand="5" round="true" type="range" min="0" max="30" unit="m" default="25" />   
        <Sensor name="cell_count" label="Cell Count" round="true" type="range" min="1" max="12" unit="" default="6" />     
    </Group>

    <Group name="Adjustment Functions">
        <Sensor name="adj_f" round="true" label="Adjust Function" type="number" default="0" min="0" max="200" />
        <Sensor name="adj_v" round="true" label="Adjust Value" type="number" default="0" min="0" max="2000"/>
    </Group>  

    <Group name="Flight Dynamics">
        <Sensor name="attpitch" round="true" rand="20" label="Pitch" type="number" default="0" min="-360" max="360" multiplier="10"/>
        <Sensor name="attroll" round="true" rand="20" label="Roll" type="number" default="0" min="-360" max="360" multiplier="10"/>
        <Sensor name="attyaw" round="true" rand="20" label="Yaw" type="number" default="0" min="-360" max="360" multiplier="10"/>
        <Sensor name="altitude" round="true" rand="5" label="Altitude" type="number" default="0" min="-1000" max="1000" />
        <Sensor name="groundspeed" round="true" rand="5"  label="Ground Speed" type="number" default="0" min="-1000" max="1000" />        
     </Group>       

    <Group name="System Events">
        <Sensor name="simevent_telemetry_state" label="Telemetry State"  type="select" round="true"  default="0"  multiplier="1" unit="">
            <Option value="0" label="Enabled" />
            <Option value="1" label="Disabled" />
        </Sensor>    
     </Group>    


</Sensors>
