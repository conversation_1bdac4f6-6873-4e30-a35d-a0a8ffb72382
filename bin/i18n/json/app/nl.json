{"btn_ok": {"english": "          OK           ", "translation": "          OK           ", "needs_translation": "false"}, "btn_close": {"english": "CLOSE", "translation": "SLUITEN", "needs_translation": "false"}, "navigation_menu": {"english": "MENU", "translation": "MENU", "needs_translation": "false"}, "menu_section_hardware": {"english": "Hardware", "translation": "Hardware", "needs_translation": "false"}, "msg_please_disarm_to_save_warning": {"english": "Settings will only be saved to e<PERSON>rom on disarm", "translation": "Instellingen worden alleen opgeslagen in EEPROM na uitschakelen", "needs_translation": "false"}, "msg_saving_settings": {"english": "Saving settings...", "translation": "Instellingen opslaan...", "needs_translation": "false"}, "msg_saving_to_fbl": {"english": "Saving data to flight controller...", "translation": "Data opslaan op vliegcontroller...", "needs_translation": "false"}, "navigation_reload": {"english": "RELOAD", "translation": "Reload", "needs_translation": "false"}, "menu_section_developer": {"english": "Developer", "translation": "Ontwikkelaar", "needs_translation": "false"}, "check_msp_version": {"english": "Unable to determine MSP version in use.", "translation": "<PERSON><PERSON> is niet mogelijk om de MSP versie te bepalen.", "needs_translation": "false"}, "menu_section_about": {"english": "About", "translation": "Over", "needs_translation": "false"}, "msg_please_disarm_to_save": {"english": "Please disarm to save", "translation": "Disarm voor opslaan om data integriteit te verzekeren.", "needs_translation": "false"}, "unit_hertz": {"english": "Hz", "translation": "Hz", "needs_translation": "false"}, "msg_rebooting": {"english": "Rebooting...", "translation": "Opnieuw opstarten...", "needs_translation": "false"}, "msg_save_settings": {"english": "Save settings", "translation": "Instellingen opslaan", "needs_translation": "false"}, "btn_cancel": {"english": "CANCEL", "translation": "ANNULEREN", "needs_translation": "false"}, "msg_connecting_to_fbl": {"english": "Connecting to flight controller...", "translation": "<PERSON><PERSON><PERSON><PERSON> met vliegcontroller...", "needs_translation": "false"}, "navigation_help": {"english": "?", "translation": "?", "needs_translation": "false"}, "modules": {"stats": {"name": {"english": "Stats", "translation": "Status", "needs_translation": "false"}, "totalflighttime": {"english": "Total Flight Time", "translation": "Totale Flight tijd", "needs_translation": "false"}, "flightcount": {"english": "Flight Count", "translation": "Aantal vluchten", "needs_translation": "false"}, "lastflighttime": {"english": "Last Flight Time", "translation": "Tijd laatste vlucht", "needs_translation": "false"}, "help_p1": {"english": "Use this module to update the recorded flight statistics on the flight controller.", "translation": "Gebruik deze module om de flight statistieken te updaten op de flight controller.", "needs_translation": "false"}}, "settings": {"name": {"english": "Settings", "translation": "Instellingen", "needs_translation": "false"}, "no_themes_available_to_configure": {"english": "No configurable themes installed on this device", "translation": "<PERSON><PERSON> <PERSON> beschik<PERSON>ar om in te stellen", "needs_translation": "false"}, "txt_audio_timer": {"english": "Timer", "translation": "Timer", "needs_translation": "false"}, "txt_audio_events": {"english": "Events", "translation": "Gebeurtenissen", "needs_translation": "false"}, "txt_audio_switches": {"english": "Switches", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "txt_iconsize": {"english": "Icon Size", "translation": "Icon grootte", "needs_translation": "false"}, "txt_general": {"english": "General", "translation": "General", "needs_translation": "false"}, "txt_text": {"english": "TEXT", "translation": "TEKST", "needs_translation": "false"}, "txt_small": {"english": "SMALL", "translation": "KLEIN", "needs_translation": "false"}, "txt_large": {"english": "LARGE", "translation": "Groot", "needs_translation": "false"}, "txt_syncname": {"english": "Sync model name", "translation": "Sync model naam", "needs_translation": "false"}, "txt_devtools": {"english": "Developer Tools", "translation": "Developer Tools", "needs_translation": "false"}, "txt_apiversion": {"english": "API Version", "translation": "API Versie", "needs_translation": "false"}, "txt_logging": {"english": "Logging", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "txt_compilation": {"english": "Compilation", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "txt_loglocation": {"english": "Log location", "translation": "Log locatie", "needs_translation": "false"}, "txt_console": {"english": "CONSOLE", "translation": "CONSOLE", "needs_translation": "false"}, "txt_consolefile": {"english": "CONSOLE & FILE", "translation": "CONSOLE & BESTAND", "needs_translation": "false"}, "txt_loglevel": {"english": "Log level", "translation": "Log niveau", "needs_translation": "false"}, "txt_off": {"english": "OFF", "translation": "uit", "needs_translation": "false"}, "txt_info": {"english": "INFO", "translation": "INFO", "needs_translation": "false"}, "txt_debug": {"english": "DEBUG", "translation": "DEBUG", "needs_translation": "false"}, "txt_mspdata": {"english": "Log msp data", "translation": "Log msp data", "needs_translation": "false"}, "txt_queuesize": {"english": "Log MSP queue size", "translation": "Log MSP wachtlijst grootte", "needs_translation": "false"}, "txt_memusage": {"english": "Log memory usage", "translation": "Log geheugengebruik", "needs_translation": "false"}, "txt_batttype": {"english": "Tx Battery Options", "translation": "Tx Battery Options", "needs_translation": "true"}, "txt_battdef": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "true"}, "txt_batttext": {"english": "Text", "translation": "Text", "needs_translation": "true"}, "txt_battdig": {"english": "Digital", "translation": "Digital", "needs_translation": "true"}, "dashboard": {"english": "Dashboard", "translation": "Dashboard", "needs_translation": "false"}, "dashboard_theme": {"english": "Theme", "translation": "<PERSON>a", "needs_translation": "false"}, "dashboard_theme_panel_global": {"english": "Default theme for all models", "translation": "Standaard thema voor alle modellen", "needs_translation": "false"}, "dashboard_theme_panel_model": {"english": "Optional theme for this model", "translation": "Optioneel thema voor dit model", "needs_translation": "false"}, "dashboard_theme_panel_model_disabled": {"english": "Disabled", "translation": "Uitgeschakeld", "needs_translation": "false"}, "dashboard_settings": {"english": "Settings", "translation": "Instellingen", "needs_translation": "false"}, "dashboard_theme_preflight": {"english": "Preflight Theme", "translation": "Preflight Thema", "needs_translation": "false"}, "dashboard_theme_inflight": {"english": "Inflight Theme", "translation": "Inflight Thema", "needs_translation": "false"}, "dashboard_theme_postflight": {"english": "Postflight Theme", "translation": "Postflight Thema", "needs_translation": "false"}, "audio": {"english": "Audio", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "localizations": {"english": "Localization", "translation": "Localisatie", "needs_translation": "false"}, "txt_development": {"english": "Development", "translation": "Development", "needs_translation": "false"}, "temperature_unit": {"english": "Temperature Unit", "translation": "Temperatuur unit", "needs_translation": "false"}, "altitude_unit": {"english": "Altitude Unit", "translation": "Hoogte unit", "needs_translation": "false"}, "celcius": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "fahrenheit": {"english": "Fahrenheit", "translation": "Fahrenheit", "needs_translation": "false"}, "meters": {"english": "Meters", "translation": "Meters", "needs_translation": "false"}, "feet": {"english": "Feet", "translation": "Feet", "needs_translation": "false"}, "warning": {"english": "Warning", "translation": "Waarschuwing", "needs_translation": "false"}, "governor_state": {"english": "Governor State", "translation": "Governor Status", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Arming Flags", "needs_translation": "false"}, "voltage": {"english": "Voltage", "translation": "Voltage", "needs_translation": "false"}, "pid_rates_profile": {"english": "PID/Rates Profile", "translation": "PID/Rates Profiel", "needs_translation": "false"}, "pid_profile": {"english": "PID Profile", "translation": "PID Profiel", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "Rate Profiel", "needs_translation": "false"}, "esc_temperature": {"english": "ESC Temperature", "translation": "ESC Temperatuur", "needs_translation": "false"}, "esc_threshold": {"english": "Thresh<PERSON> (°)", "translation": "Limiet (°)", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "BEC Voltage", "needs_translation": "false"}, "bec_threshold": {"english": "<PERSON><PERSON><PERSON><PERSON> (V)", "translation": "Limiet (V)", "needs_translation": "false"}, "fuel": {"english": "Fuel", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "fuel_callout_default": {"english": "Default (Only at 10%)", "translation": "Standaard (Alleen per 10%)", "needs_translation": "false"}, "fuel_callout_10": {"english": "Every 10%", "translation": "Elke 10%", "needs_translation": "false"}, "fuel_callout_20": {"english": "Every 20%", "translation": "Elke 20%", "needs_translation": "false"}, "fuel_callout_25": {"english": "Every 25%", "translation": "Elke 25%", "needs_translation": "false"}, "fuel_callout_50": {"english": "Every 50%", "translation": "Elke 50%", "needs_translation": "false"}, "fuel_callout_percent": {"english": "Callout %", "translation": "Oproep %", "needs_translation": "false"}, "fuel_repeats_below": {"english": "Repeats below 0%", "translation": "<PERSON><PERSON><PERSON><PERSON> onder 0%", "needs_translation": "false"}, "fuel_haptic_below": {"english": "Haptic below 0%", "translation": "<PERSON><PERSON> onder 0%", "needs_translation": "false"}, "timer_alerting": {"english": "Timer Alerting", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "timer_elapsed_alert_mode": {"english": "Timer <PERSON><PERSON><PERSON>", "translation": "Timer afgelopen waarschuwing", "needs_translation": "false"}, "timer_prealert_options": {"english": "Pre-timer <PERSON><PERSON>", "translation": "Voor-timer waarschuwing opties", "needs_translation": "false"}, "timer_prealert": {"english": "Pre-timer <PERSON><PERSON>", "translation": "<PERSON><PERSON>-timer <PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "timer_alert_period": {"english": "Alert <PERSON>", "translation": "Waarschuwings periode", "needs_translation": "false"}, "timer_postalert_options": {"english": "Post-timer <PERSON><PERSON>", "translation": "Na-timer <PERSON><PERSON><PERSON><PERSON><PERSON> opties", "needs_translation": "false"}, "timer_postalert": {"english": "Post-timer <PERSON><PERSON>", "translation": "Na-timer wa<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "timer_postalert_period": {"english": "Alert <PERSON>", "translation": "Waarschuwing periode", "needs_translation": "false"}, "timer_postalert_interval": {"english": "<PERSON><PERSON>", "translation": "Waarschuwing interval", "needs_translation": "false"}}, "validate_sensors": {"help_p1": {"english": "This tool attempts to list all the sensors that you are not receiving in a concise list.", "translation": "Deze tool probeert een beknopt overzicht te geven van alle sensoren die u niet ontvangt.", "needs_translation": "false"}, "invalid": {"english": "INVALID", "translation": "INVALID", "needs_translation": "false"}, "name": {"english": "Sensors", "translation": "Sensors", "needs_translation": "false"}, "msg_repair": {"english": "Enable required sensors on flight controller?", "translation": "Zet de benodigde sensoren aan in de vliegcontroller?", "needs_translation": "false"}, "msg_repair_fin": {"english": "The flight controller has been configured? You may need to perform a discover sensors to see the changes.", "translation": "Is de vliegcontroller geconfigureerd? Het is waarschijnlijk benodigd om opnieuw 'Vind nieuwe sensoren' te gebruiken", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "Use this tool to ensure you are sending the correct sensors.", "translation": "Met deze tool kunt u controleren of u de juiste sensoren verzendt.", "needs_translation": "false"}}, "msp_exp": {"help_p1": {"english": "This tool provides the ability to send a custom byte string to the flight controller. It is useful for developers when debugging values.", "translation": "Deze tool biedt de mogelijkheid om een ​​aangepaste bytestring naar de flight controller te sturen. Het is handig voor ontwikkelaars bij het debuggen van waarden.", "needs_translation": "false"}, "name": {"english": "MSP Expermental", "translation": "MSP Experimenteel", "needs_translation": "false"}, "help_p2": {"english": "If you do not understand what you are doing, do not use it as bad things can happen.", "translation": "Als je niet begrijpt wat je doet, gebruik het dan niet, want er kunnen rare dingen gebeuren.", "needs_translation": "false"}}, "esc_tools": {"unknown": {"english": "UNKNOWN", "translation": "Onbekend", "needs_translation": "false"}, "name": {"english": "ESC Tools", "translation": "ESC Tools", "needs_translation": "false"}, "please_powercycle": {"english": "Please power cycle the ESC...", "translation": "Restart de esc...", "needs_translation": "false"}, "mfg": {"hw5": {"esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "brake_force": {"english": "Brake Force%", "translation": "Remkracht %", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Motor Draairichting", "needs_translation": "false"}, "soft_start": {"english": "Soft Start", "translation": "Langzame opstart", "needs_translation": "false"}, "name": {"english": "Hobbywing V5", "translation": "Hobbywing V5", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "BEC Spanning", "needs_translation": "false"}, "gov_i_gain": {"english": "I-G<PERSON>", "translation": "I-G<PERSON>", "needs_translation": "false"}, "startup_time": {"english": "Startup Time", "translation": "Opstart tijd", "needs_translation": "false"}, "lipo_cell_count": {"english": "LiPo Cell Count", "translation": "LiPo aantal cellen", "needs_translation": "false"}, "restart_time": {"english": "Restart Time", "translation": "Herstart tijd", "needs_translation": "false"}, "volt_cutoff_type": {"english": "Volt Cutoff Type", "translation": "Lage spanning limiet", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "Motor", "needs_translation": "false"}, "brake_type": {"english": "Brake Type", "translation": "Rem type", "needs_translation": "false"}, "brake": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Geavanceerd", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "flight_mode": {"english": "Flight Mode", "translation": "<PERSON><PERSON>cht modus", "needs_translation": "false"}, "auto_restart": {"english": "Auto Restart", "translation": "Auto Herstart", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Active Freewheel", "needs_translation": "false"}, "cutoff_voltage": {"english": "Cutoff Voltage", "translation": "Uitschakelingsspanning", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Opstart vermogen", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Ander", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Motor timing", "needs_translation": "false"}, "gov_p_gain": {"english": "P-Gain", "translation": "P-Gain", "needs_translation": "false"}}, "xdfly": {"hv_bec_voltage": {"english": "HV BEC Voltage", "translation": "HV BEC Spanning", "needs_translation": "false"}, "gov": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "brake_force": {"english": "Brake Force", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "sr_function": {"english": "SR Function", "translation": "SR Function", "needs_translation": "false"}, "name": {"english": "XDFLY", "translation": "XDFLY", "needs_translation": "false"}, "lv_bec_voltage": {"english": "LV BEC Voltage", "translation": "LV BEC Spanning", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Auto herstart tijd", "needs_translation": "false"}, "acceleration": {"english": "Acceleration", "translation": "Accelerat<PERSON>", "needs_translation": "false"}, "motor_direction": {"english": "Motor Direction", "translation": "Motor draairichting", "needs_translation": "false"}, "smart_fan": {"english": "Smart Fan", "translation": "Smart Fan", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Geavanceerd", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "Cell Cutoff", "needs_translation": "false"}, "led_color": {"english": "LED Color", "translation": "<PERSON> Kleur", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Opstart geluid", "needs_translation": "false"}, "motor_poles": {"english": "Motor Poles", "translation": "Motor Polen", "needs_translation": "false"}, "capacity_correction": {"english": "Capacity Correction", "translation": "Capaciteit correctie", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Motortiming", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}, "flrtr": {"gov": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "motor_temp_sensor": {"english": "Motor temp sensor", "translation": "Motor temp sensor", "needs_translation": "false"}, "starting_torque": {"english": "Starting torque", "translation": "Start koppel", "needs_translation": "false"}, "cell_count": {"english": "Cell count", "translation": "Aantal cellen", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}, "motor_erpm_max": {"english": "Motor ERPM max", "translation": "Motor ERPM max", "needs_translation": "false"}, "name": {"english": "FLYROTOR", "translation": "FLYROTOR", "needs_translation": "false"}, "low_voltage_protection": {"english": "Low voltage protection", "translation": "Lage spanning beveil.", "needs_translation": "false"}, "gov_d": {"english": "Gov-D", "translation": "Gov-D", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry protocol", "translation": "Telemetry protocol", "needs_translation": "false"}, "motor_direction": {"english": "Motor direction", "translation": "Motor draairichting", "needs_translation": "false"}, "throttle_protocol": {"english": "Throttle protocol", "translation": "Throttle protocol", "needs_translation": "false"}, "soft_start": {"english": "Soft start", "translation": "Langzame opstart", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Ander", "needs_translation": "false"}, "temperature_protection": {"english": "Temperature protection", "translation": "Temperatuur beveilig.", "needs_translation": "false"}, "buzzer_volume": {"english": "Buzzer volume", "translation": "Piep volume", "needs_translation": "false"}, "timing_angle": {"english": "Timing angle", "translation": "Motor Timing", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Geavanceerd", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "bec_voltage": {"english": "BEC voltage", "translation": "BEC spanning", "needs_translation": "false"}, "fan_control": {"english": "Fan control", "translation": "Fan besturing", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "current_gain": {"english": "Current gain", "translation": "Stroom gain", "needs_translation": "false"}, "led_color": {"english": "LED color", "translation": "LED kleur", "needs_translation": "false"}, "motor_temp": {"english": "Motor temperture", "translation": "Motor temperatuur", "needs_translation": "false"}, "response_speed": {"english": "Response speed", "translation": "Reactiesnelheid", "needs_translation": "false"}, "battery_capacity": {"english": "Battery capacity", "translation": "Batterij capaciteit", "needs_translation": "false"}}, "scorp": {"esc_mode": {"english": "ESC Mode", "translation": "ESC Modus", "needs_translation": "false"}, "min_voltage": {"english": "Min Voltage", "translation": "<PERSON>", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Motor draairichting", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry Protocol", "translation": "Telemetrie Protocol", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "runup_time": {"english": "Runup Time", "translation": "Opstarttijd", "needs_translation": "false"}, "motor_startup_sound": {"english": "Motor Startup Sound", "translation": "Motor opstart geluid", "needs_translation": "false"}, "gov_integral": {"english": "Gov <PERSON><PERSON>", "translation": "Gov <PERSON><PERSON>", "needs_translation": "false"}, "gov_proportional": {"english": "Gov Proportional", "translation": "Gov Proportional", "needs_translation": "false"}, "cutoff_handling": {"english": "Cutoff Handling", "translation": "Stop beveiliging", "needs_translation": "false"}, "bailout": {"english": "Bailout", "translation": "Bailout", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "soft_start_time": {"english": "Soft Start Time", "translation": "Lang<PERSON>e opstart tijd", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Geavanceerd", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "BEC Spanning", "needs_translation": "false"}, "extra_msg_save": {"english": "Please reboot the ESC to apply the changes", "translation": "Herstart de ESC om de wijzigingen op te slaan", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "max_current": {"english": "Max <PERSON>", "translation": "Max Stroom", "needs_translation": "false"}, "max_temperature": {"english": "Max Temperature", "translation": "<PERSON>", "needs_translation": "false"}, "protection_delay": {"english": "Protection Delay", "translation": "Beveiliging delay", "needs_translation": "false"}, "max_used": {"english": "Max Used", "translation": "<PERSON>", "needs_translation": "false"}}, "yge": {"esc_mode": {"english": "ESC Mode", "translation": "ESC Modus", "needs_translation": "false"}, "esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "current_limit": {"english": "Current Limit", "translation": "Stroom limiet", "needs_translation": "false"}, "f3c_auto": {"english": "F3C Autorotation", "translation": "F3C Autorotation", "needs_translation": "false"}, "name": {"english": "YGE", "translation": "YGE", "needs_translation": "false"}, "max_start_power": {"english": "Max Start Power", "translation": "Max Start vermogen", "needs_translation": "false"}, "lv_bec_voltage": {"english": "BEC", "translation": "BEC Spanning", "needs_translation": "false"}, "pinion_teeth": {"english": "Pinion Teeth", "translation": "Pinion Tabdeb", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Auto herstart tijd", "needs_translation": "false"}, "main_teeth": {"english": "Main Teeth", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tanden", "needs_translation": "false"}, "other": {"english": "Other", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "Cell Cutoff", "needs_translation": "false"}, "throttle_response": {"english": "Throttle Response", "translation": "<PERSON><PERSON><PERSON><PERSON> Reactie", "needs_translation": "false"}, "stick_zero_us": {"english": "<PERSON>", "translation": "Stick nul", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Geavanceerd", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "motor_pole_pairs": {"english": "Motor Pole Pairs", "translation": "Motor Polen pair", "needs_translation": "false"}, "stick_range_us": {"english": "Stick Range", "translation": "<PERSON> bereik", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "min_start_power": {"english": "Min Start Power", "translation": "Min Start vermogen", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Active Freewheel", "needs_translation": "false"}, "direction": {"english": "Direction", "translation": "Motor draairichting", "needs_translation": "false"}, "timing": {"english": "Motor Timing", "translation": "Motor Timing", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}}, "searching": {"english": "Searching", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "pids": {"help_p1": {"english": "FeedForward (Roll/Pitch): Start at 70, increase until stops are sharp with no drift. Keep roll and pitch equal.", "translation": "FeedForward (Roll/Pitch): <PERSON><PERSON> bij 70, ver<PERSON><PERSON> tot stops scherp zijn zonder drift. Houd roll en pitch gelijk.", "needs_translation": "false"}, "o": {"english": "O", "translation": "O", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "help_p5": {"english": "Test & Adjust: Fly, observe, and fine-tune for best performance in real conditions.", "translation": "Test & Adjust: <PERSON><PERSON><PERSON>, observeer en optimaliseer voor de beste prestaties onder reële omstandigheden.\n\n", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "PIDs", "translation": "PIDs", "needs_translation": "false"}, "help_p2": {"english": "<PERSON> Gain (Roll/Pitch): Raise gradually for stable piro pitch pumps. Too high causes wobbles; match roll/pitch values.", "translation": "<PERSON> (Roll/Pitch): Verhoog geleidelijk voor stabiele piro-pitch pumps. Te hoog veroorzaakt wiebelen; match rol-/pitchwaarden.", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "b": {"english": "B", "translation": "B", "needs_translation": "false"}, "help_p4": {"english": "Tail Stop Gain (CW/CCW): Adjust separately for clean, bounce-free stops in both directions.", "translation": "Tail Stop Gain (CW/CCW): Afzonderlijk instelbaar voor een zuivere stop in beide richtingen.", "needs_translation": "false"}, "help_p3": {"english": "Tail P/I/D Gains: Increase P until slight wobble in funnels, then back off slightly. Raise I until tail holds firm in hard moves (too high causes slow wag). Adjust D for smooth stops—higher for slow servos, lower for fast ones.", "translation": "Tail P/I/D Gains: Verhoog P tot een lichte wag in hurricanes, en ga dan iets terug. Verhoog I tot de staart locked-in is in harde bewegingen (te hoog veroorzaakt langzame wag). Pas D aan voor soepele stops: hoger voor langzame servo's, lager voor snelle.", "needs_translation": "false"}}, "msp_speed": {"seconds_600": {"english": "  600S  ", "translation": "  600S  ", "needs_translation": "false"}, "avg_query_time": {"english": "Average query time", "translation": "Average query time", "needs_translation": "false"}, "seconds_30": {"english": "  30S  ", "translation": "  30S  ", "needs_translation": "false"}, "name": {"english": "MSP Speed", "translation": "MSP Speed", "needs_translation": "false"}, "max_query_time": {"english": "Maximum query time", "translation": "Maximum query time", "needs_translation": "false"}, "help_p1": {"english": "This tool attempts to determine the quality of your MSP data link by performing as many large MSP queries within 30 seconds as possible.", "translation": "Deze tool probeert de kwaliteit van uw MSP-datalink te bepalen door binnen 30 seconden zoveel mogelijk grote MSP-query's uit te voeren.\n\n", "needs_translation": "false"}, "retries": {"english": "Retries", "translation": "Retries", "needs_translation": "false"}, "checksum_errors": {"english": "Checksum errors", "translation": "Checksum errors", "needs_translation": "false"}, "test_length": {"english": "Test length", "translation": "Test length", "needs_translation": "false"}, "start": {"english": "Start", "translation": "Start", "needs_translation": "false"}, "memory_free": {"english": "Memory free", "translation": "Memory free", "needs_translation": "false"}, "start_prompt": {"english": "Would you like to start the test? Choose the test run time below.", "translation": "Would you like to start the test? Choose the test run time below.", "needs_translation": "false"}, "rf_protocol": {"english": "RF protocol", "translation": "RF protocol", "needs_translation": "false"}, "min_query_time": {"english": "Minimum query time", "translation": "Minimum query time", "needs_translation": "false"}, "seconds_120": {"english": "  120S  ", "translation": "  120S  ", "needs_translation": "false"}, "seconds_300": {"english": "  300S  ", "translation": "  300S  ", "needs_translation": "false"}, "testing": {"english": "Testing", "translation": "Testing", "needs_translation": "false"}, "successful_queries": {"english": "Successful queries", "translation": "Successful queries", "needs_translation": "false"}, "timeouts": {"english": "Timeouts", "translation": "Timeouts", "needs_translation": "false"}, "testing_performance": {"english": "Testing MSP performance...", "translation": "Testing MSP performance...", "needs_translation": "false"}, "total_queries": {"english": "Total queries", "translation": "Total queries", "needs_translation": "false"}}, "copyprofiles": {"profile_type": {"english": "Profile Type", "translation": "Profiel <PERSON>", "needs_translation": "false"}, "profile_type_pid": {"english": "PID", "translation": "PID", "needs_translation": "false"}, "profile_type_rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "msgbox_save": {"english": "Save settings", "translation": "Instellingen opslaan", "needs_translation": "false"}, "name": {"english": "Copy Profiles", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Copy PID profile or Rate profile from Source to Destination.", "translation": "<PERSON><PERSON><PERSON> profiel of Hoeksnelheid profiel van bron naar doel.", "needs_translation": "false"}, "dest_profile": {"english": "Dest. Profile", "translation": "Doel. <PERSON>", "needs_translation": "false"}, "source_profile": {"english": "Source Profile", "translation": "<PERSON>ron Profiel", "needs_translation": "false"}, "msgbox_msg": {"english": "Save current page to flight controller?", "translation": "Sla huidige pagina op in de vliegcontroller?", "needs_translation": "false"}, "help_p2": {"english": "Choose the source and destinations and save to copy the profile.", "translation": "Kies de bron en doel en klik op opslaan om het kopieren te starten.\n\n", "needs_translation": "false"}}, "esc_motors": {"min_throttle": {"english": "0% Throttle PWM Value", "translation": "0% Throttle PWM waarde", "needs_translation": "false"}, "tail_motor_ratio": {"english": "Tail Motor Ratio", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "max_throttle": {"english": "100% Throttle PWM value", "translation": "100% Throttle PWM waarde", "needs_translation": "false"}, "main_motor_ratio": {"english": "Main Motor Ratio", "translation": "Main Ratio", "needs_translation": "false"}, "pinion": {"english": "Pinion", "translation": "Pinion", "needs_translation": "false"}, "main": {"english": "Main", "translation": "Main gear", "needs_translation": "false"}, "help_p1": {"english": "Configure the motor and speed controller features.", "translation": "Motor en esc functies.", "needs_translation": "false"}, "rear": {"english": "Rear", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "front": {"english": "Front", "translation": "Voor", "needs_translation": "false"}, "voltage_correction": {"english": "Voltage Correction", "translation": "Spanning correctie", "needs_translation": "false"}, "mincommand": {"english": "Motor Stop PWM Value", "translation": "Motor Stop PWM waarde", "needs_translation": "false"}, "name": {"english": "ESC/Motors", "translation": "ESC/Motor", "needs_translation": "false"}, "motor_pole_count": {"english": "Motor Pole Count", "translation": "Motor Polen", "needs_translation": "false"}, "current_correction": {"english": "Current Correction", "translation": "Stroom correctie", "needs_translation": "false"}, "consumption_correction": {"english": "Consumption Correction", "translation": "Verbruik correctie", "needs_translation": "false"}}, "radio_config": {"deflection": {"english": "Deflection", "translation": "Deflection", "needs_translation": "false"}, "max_throttle": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "stick": {"english": "Stick", "translation": "Stick", "needs_translation": "false"}, "arming": {"english": "Arming", "translation": "Arming", "needs_translation": "false"}, "yaw_deadband": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "cyclic": {"english": "Cyclic", "translation": "Cyclic", "needs_translation": "false"}, "name": {"english": "Radio Config", "translation": "Radio Config", "needs_translation": "false"}, "help_p1": {"english": "Configure your radio settings. Stick center, arm, throttle hold, and throttle cut.", "translation": "Configureer uw radio settings. Stick center, arm, throttle hold, en throttle cut.\n", "needs_translation": "false"}, "min_throttle": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "throttle": {"english": "<PERSON>hrottle", "translation": "<PERSON>hrottle", "needs_translation": "false"}, "deadband": {"english": "Deadband", "translation": "Deadband", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Center", "needs_translation": "false"}}, "profile_select": {"help_p1": {"english": "Set the current flight profile or rate profile you would like to use.", "translation": "Stel het huidige pid profiel of rateprofiel in dat u wilt gebruiken.", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "Rate Profiel", "needs_translation": "false"}, "pid_profile": {"english": "PID profile", "translation": "PID profiel", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "Sla huidige pagina op in de vliegcontroller?", "needs_translation": "false"}, "save_prompt_local": {"english": "Save current page to radio?", "translation": "Sla huidige instellingen op?", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "Afbreken", "needs_translation": "false"}, "name": {"english": "Select Profile", "translation": "Selecteer profiel", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "Instellingen opslaan", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "If you use a switch on your radio to change flight or rate modes, this will override this choice as soon as you toggle the switch.", "translation": "Als u een schakelaar op uw radio gebruikt om de pid- of ratemodus te wijzigen, wordt deze keuze genegeerd zodra u de schakelaar omzet.\n", "needs_translation": "false"}}, "profile_governor": {"tail_torque_assist": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "cyc": {"english": "Cyc", "translation": "Cyc", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "help_p1": {"english": "Full headspeed: Headspeed target when at 100% throttle input.", "translation": "Max rotortoerental: Rotortoerental doel bij 100% throttle", "needs_translation": "false"}, "help_p6": {"english": "Tail Torque Assist: For motorized tails. Gain and limit of headspeed increase when using main rotor torque for yaw assist.", "translation": "Tail Torque Assist: Voor gemotoriseerde staart. Gains en minimale rotortoerental nemen toe voorhoofdrotorkoppel voor yaw-assistentie./n", "needs_translation": "false"}, "help_p4": {"english": "Precomp: Governor precomp gain for yaw, cyclic, and collective inputs.", "translation": "Precomp: Governor precomp gain voor yaw, cyclic, en collective inputs.", "needs_translation": "false"}, "max_throttle": {"english": "<PERSON> throttle", "translation": "<PERSON> throttle", "needs_translation": "false"}, "full_headspeed": {"english": "Full headspeed", "translation": "<PERSON>", "needs_translation": "false"}, "precomp": {"english": "Precomp", "translation": "Precomp", "needs_translation": "false"}, "gain": {"english": "PID master gain", "translation": "PID master gain", "needs_translation": "false"}, "disabled_message": {"english": "Rotorflight governor is not enabled", "translation": "Rotorflight governor is niet a<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p3": {"english": "Gains: Fine tuning of the governor.", "translation": "Gains: <PERSON><PERSON><PERSON><PERSON>.", "needs_translation": "false"}, "col": {"english": "Col", "translation": "Col", "needs_translation": "false"}, "min_throttle": {"english": "<PERSON> throttle", "translation": "<PERSON> throttle", "needs_translation": "false"}, "tta_limit": {"english": "Limit", "translation": "Limit", "needs_translation": "false"}, "help_p2": {"english": "PID master gain: How hard the governor works to hold the RPM.", "translation": "PID master gain: <PERSON><PERSON> hard de governor werkt om de RPM in stand te houden.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p5": {"english": "Max throttle: The maximum throttle % the governor is allowed to use.", "translation": "Max throttle: Het maximale throttle % de governor mag g<PERSON><PERSON><PERSON>n.", "needs_translation": "false"}, "tta_gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "profile_tailrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p4": {"english": "Collective FF Gain: Tail precompensation for collective inputs.", "translation": "Collective FF Gain: Tail precompensation voor collective inputs.", "needs_translation": "false"}, "collective_impulse_ff": {"english": "Collective Impulse FF", "translation": "Collective Impulse FF", "needs_translation": "false"}, "help_p2": {"english": "Precomp Cutoff: Frequency limit for all yaw precompensation actions.", "translation": "Precomp Cutoff: Frequentie limiet voor alle staart precompensatie acties.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "help_p3": {"english": "Cyclic FF Gain: Tail precompensation for cyclic inputs.", "translation": "Cyclic FF Gain: Tail precompensation voor cyclic inputs.", "needs_translation": "false"}, "help_p1": {"english": "Yaw Stop Gain: Higher stop gain will make the tail stop more aggressively but may cause oscillations if too high. Adjust CW or CCW to make the yaw stops even.", "translation": "Staart Stop Gain: <PERSON><PERSON><PERSON> stop gain zal de tail stop agressiever maken, maar kan oscillaties veroorzaken als deze te hoog is. Pas CW of CCW aan om de yaw stops gelijkmatiger te maken.", "needs_translation": "false"}, "inertia_precomp": {"english": "Inertia Precomp", "translation": "Inertia Precomp", "needs_translation": "false"}, "cyclic_ff_gain": {"english": "Cyclic FF gain", "translation": "Cyclic FF gain", "needs_translation": "false"}, "help_p5": {"english": "Collective Impulse FF: Impulse tail precompensation for collective inputs. If you need extra tail precompensation at the beginning of collective input.", "translation": "Collective Impuls FF: Impuls staart precompensatie voor collectieve inputs. Als u extra precompensatie nodig met begin van de klim.\n\n", "needs_translation": "false"}, "cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "yaw_stop_gain": {"english": "Yaw stop gain", "translation": "Staart stop gain", "needs_translation": "false"}, "precomp_cutoff": {"english": "Precomp Cutoff", "translation": "Precomp Cutoff", "needs_translation": "false"}, "collective_ff_gain": {"english": "Collective FF gain", "translation": "Collective FF gain", "needs_translation": "false"}, "name": {"english": "Tail Rotor", "translation": "Staart Rotor", "needs_translation": "false"}, "decay": {"english": "Decay", "translation": "Decay", "needs_translation": "false"}}, "profile_pidcontroller": {"help_p4": {"english": "Error rotation: Allow errors to be shared between all axes.", "translation": "Error rotation: <PERSON><PERSON><PERSON> ervoor dat fouten tussen alle assen worden gedeeld.", "needs_translation": "false"}, "ground_error_decay": {"english": "Ground Error Decay", "translation": "Ground Error Decay", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "inflight_error_decay": {"english": "Inflight Error Decay", "translation": "Inflight Error Decay", "needs_translation": "false"}, "help_p2": {"english": "Error limit: Angle limit for I-term.", "translation": "Error limit: <PERSON>ek limiet voor I-term.", "needs_translation": "false"}, "error_limit": {"english": "Error limit", "translation": "Error limit", "needs_translation": "false"}, "help_p3": {"english": "Offset limit: Angle limit for High Speed Integral (O-term).", "translation": "Offset limit: <PERSON>ek limiet voor High Speed Integral (O-term).", "needs_translation": "false"}, "cutoff_point": {"english": "Cut-off point", "translation": "Cut-off point", "needs_translation": "false"}, "limit": {"english": "Limit", "translation": "Limit", "needs_translation": "false"}, "iterm_relax": {"english": "I-term relax", "translation": "I-term relax", "needs_translation": "false"}, "hsi_offset_limit": {"english": "HSI Offset limit", "translation": "HSI Offset limit", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "name": {"english": "PID Controller", "translation": "PID Controller", "needs_translation": "false"}, "error_rotation": {"english": "Error rotation", "translation": "Error rotation", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}, "help_p5": {"english": "I-term relax: Limit accumulation of I-term during fast movements - helps reduce bounce back after fast stick movements. Generally needs to be lower for large helis and can be higher for small helis. Best to only reduce as much as is needed for your flying style.", "translation": "I-term relax: <PERSON><PERSON><PERSON> de ophoping van I-term tijdens snelle bewegingen - helpt de bounce back na snelle stickbewegingen te verminderen. <PERSON><PERSON> over het algemeen lager zijn voor grote heli's en kan hoger zijn voor kleine heli's. Het beste is om alleen zoveel te verminderen als nodig is voor uw vliegstijl.\n\n", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Time", "needs_translation": "false"}, "help_p1": {"english": "Error decay ground: PID decay to help prevent heli from tipping over when on the ground.", "translation": "Error decay ground: PID-verval om te voorkomen dat de helikopter omvalt als hij op de grond staat.", "needs_translation": "false"}}, "logs": {"help_logs_p2": {"english": "Note. To enable logging it is essential for you to have the following sensors enabled.", "translation": "Let op. Om logging in te schakelen, is het essentieel dat u de volgende sensoren hebt ingeschakeld.", "needs_translation": "false"}, "name": {"english": "Logs", "translation": "Logs", "needs_translation": "false"}, "help_logs_p1": {"english": "Please select a log file from the list below.", "translation": "Selecteer een logbestand uit onderstaande lijst.", "needs_translation": "false"}, "msg_no_logs_found": {"english": "NO LOG FILES FOUND", "translation": "Geen log bestand gevonden", "needs_translation": "false"}, "help_logs_tool_p1": {"english": "Please use the slider to navigate the graph.", "translation": "Gebruik de schuifbalk om door de grafiek te navigeren.\n", "needs_translation": "false"}, "help_logs_p3": {"english": "- arm status, voltage, headspeed, current, esc temperature", "translation": "- arm status, voltage, headspeed, current, esc temperature", "needs_translation": "false"}}, "battery": {"calcfuel_local": {"english": "Calculate Fuel Using", "translation": "Calculate fuel using", "needs_translation": "true"}, "max_cell_voltage": {"english": "Max Cell Voltage", "translation": "<PERSON>", "needs_translation": "false"}, "full_cell_voltage": {"english": "Full Cell Voltage", "translation": "Volle Cell Spanning", "needs_translation": "false"}, "name": {"english": "Battery", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "min_cell_voltage": {"english": "Min Cell Voltage", "translation": "<PERSON>", "needs_translation": "false"}, "help_p1": {"english": "The battery settings are used to configure the flight controller to monitor the battery voltage and provide warnings when the voltage drops below a certain level.", "translation": "De batterij instellingen worden ingesteld zodat de vliegcontroller de batterij spanning kan monitoren en waarschuwingen kan geven als de spanning onder een bepaald niveau komt.\n\n", "needs_translation": "false"}, "battery_capacity": {"english": "Battery Capacity", "translation": "Batterij <PERSON>ac<PERSON>it", "needs_translation": "false"}, "warn_cell_voltage": {"english": "Warn Cell Voltage", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "cell_count": {"english": "Cell Count", "translation": "Aantal cellen", "needs_translation": "false"}, "consumption_warning_percentage": {"english": "Consumption Warning %", "translation": "Verbruik Waarschuwing %", "needs_translation": "false"}, "timer": {"english": "Flight Time Alarm", "translation": "Flight Tijd", "needs_translation": "false"}, "voltage_multiplier": {"english": "Sag Compensation", "translation": "Sag compensatie", "needs_translation": "false"}, "kalman_multiplier": {"english": "Filter Compensation", "translation": "Filter compensation", "needs_translation": "true"}, "alert_type": {"english": "Rx Voltage Alert", "translation": "BEC or Rx Batt Voltage Alert", "needs_translation": "true"}, "bec_voltage_alert": {"english": "BEC Alert Value", "translation": "BEC Alert Value", "needs_translation": "true"}, "rx_voltage_alert": {"english": "RxBatt Alert Value", "translation": "RX Batt Alert Value", "needs_translation": "true"}}, "profile_mainrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p4": {"english": "Cross Coupling Freq. Limit: Frequency limit for the compensation, higher value will make the compensation action faster.", "translation": "Cross Coupling Freq. Limit: Frequentie limiet voor de compensatie, <PERSON><PERSON><PERSON> waarde wil de compensatie actie sneller.\n\n", "needs_translation": "false"}, "collective_pitch_comp_short": {"english": "Col. Pitch Compensation", "translation": "Col. Pitch Compensation", "needs_translation": "false"}, "cyclic_cross_coupling": {"english": "Cyclic Cross coupling", "translation": "Cyclic Cross coupling", "needs_translation": "false"}, "collective_pitch_comp": {"english": "Collective Pitch Compensation", "translation": "Collective Pitch Compensation", "needs_translation": "false"}, "name": {"english": "Main Rotor", "translation": "Hoofdrotor", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "ratio": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Collective Pitch Compensation: Increasing will compensate for the pitching motion caused by tail drag when climbing.", "translation": "Collective Pitch Compensation: Verhogen compenseert voor de pitch beweging wat wordt veroorzaakt door staart drag bij het klimmen.", "needs_translation": "false"}, "help_p2": {"english": "Cross Coupling Gain: Removes roll coupling when only elevator is applied.", "translation": "Cross Coupling Gain: Verwijderd Roll koppeling wanneer alleen elevator wordt gebruikt.", "needs_translation": "false"}, "help_p3": {"english": "Cross Coupling Ratio: Amount of compensation (pitch vs roll) to apply.", "translation": "Cross Coupling Ratio: <PERSON><PERSON><PERSON><PERSON><PERSON> compensatie (pitch vs roll) toe te passen.", "needs_translation": "false"}}, "sbusout": {"title": {"english": "SBUS Output", "translation": "SBUS Output", "needs_translation": "false"}, "help_fields_source": {"english": "Source id for the mix, counting from 0-15.", "translation": "Source id voor de mix, optellend van 0-15.", "needs_translation": "false"}, "help_default_p4": {"english": "- For motors, use 0, 1000.", "translation": "- For motoren, gebruik 0, 1000.", "needs_translation": "false"}, "ch_prefix": {"english": "CH", "translation": "CH", "needs_translation": "false"}, "channel_prefix": {"english": "CHANNEL ", "translation": "CHANNEL ", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "Opsla<PERSON>", "needs_translation": "false"}, "name": {"english": "SBUS Out", "translation": "SBUS Out", "needs_translation": "false"}, "channel_page": {"english": "Sbus out / CH", "translation": "Sbus out / CH", "needs_translation": "false"}, "receiver": {"english": "Receiver", "translation": "Receiver", "needs_translation": "false"}, "servo": {"english": "Servo", "translation": "Servo", "needs_translation": "false"}, "type": {"english": "Type", "translation": "Type", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "Opslaan...", "needs_translation": "false"}, "help_fields_max": {"english": "The maximum pwm value to send", "translation": "De maximum pwm waarde welke gestuurd wordt.\n\n", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "Motor", "needs_translation": "false"}, "help_default_p5": {"english": "- Or you can customize your own mapping.", "translation": "- Of je kunt een eigen mapping gebruiken.", "needs_translation": "false"}, "help_default_p1": {"english": "Configure advanced mixing and channel mapping if you have SBUS Out enabled on a serial port.", "translation": "Configureer geavanceerde mixing en kanaal mapping als je SBUS uitgang aangezet hebt op een seriele poort.", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "Sla huidige pagina op in de vliegcontroller?", "needs_translation": "false"}, "help_fields_min": {"english": "The minimum pwm value to send.", "translation": "De minimum pwm waarde welke gestuurd wordt.", "needs_translation": "false"}, "mixer": {"english": "Mixer", "translation": "Mixer", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "Afbreken", "needs_translation": "false"}, "help_default_p2": {"english": "- For RX channels or servos (wideband), use 1000, 2000 or 500,1000 for narrow band servos.", "translation": "- Voor RX kanalen of servos (wideband), gebruik 1000, 2000 of 500,1000 voor narrow band servos.", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "Instellingen opslaan", "needs_translation": "false"}, "min": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "help_default_p3": {"english": "- For mixer rules, use -1000, 1000.", "translation": "- Voor mixer rules, gebruik -1000, 1000.", "needs_translation": "false"}, "source": {"english": "Source", "translation": "Source", "needs_translation": "false"}}, "profile_rescue": {"help_p4": {"english": "Hover: How much collective to maintain a steady hover.", "translation": "Hover: Hoeveel collective nodig is voor stabiele hover.", "needs_translation": "false"}, "hover": {"english": "Hover", "translation": "Hover", "needs_translation": "false"}, "collective": {"english": "Collective", "translation": "Collective", "needs_translation": "false"}, "help_p2": {"english": "Pull-up: How much collective and for how long to arrest the fall.", "translation": "Omhoog: Hoeveel collective en voor hoelang.", "needs_translation": "false"}, "climb": {"english": "Climb", "translation": "K<PERSON><PERSON>", "needs_translation": "false"}, "mode_enable": {"english": "Rescue mode enable", "translation": "Rescue mode Aan", "needs_translation": "false"}, "help_p3": {"english": "Climb: How much collective to maintain a steady climb - and how long.", "translation": "Klimmen: Hoeveel collective om continue te klimmen - en hoelang.", "needs_translation": "false"}, "help_p1": {"english": "Flip to upright: Flip the heli upright when rescue is activated.", "translation": "Omkeren naar rechtop: <PERSON><PERSON> de heli rechtop als rescue wordt geactiveerd.", "needs_translation": "false"}, "flip_upright": {"english": "Flip to upright", "translation": "<PERSON>m<PERSON><PERSON> naar rechtop", "needs_translation": "false"}, "flip": {"english": "Flip", "translation": "Omkeren", "needs_translation": "false"}, "level_gain": {"english": "Level", "translation": "Level", "needs_translation": "false"}, "name": {"english": "Rescue", "translation": "Rescue", "needs_translation": "false"}, "exit_time": {"english": "Exit time", "translation": "Exit tijd", "needs_translation": "false"}, "help_p5": {"english": "Flip: How long to wait before aborting because the flip did not work.", "translation": "Flip: Hoe lang moet je wachten voordat rescue afgebroken wordt omdat de flip niet werkte?.", "needs_translation": "false"}, "help_p6": {"english": "Gains: How hard to fight to keep heli level when engaging rescue mode.", "translation": "Gains: Hoe hard de heli moet vechten om de heli horizontaal te houden als je rescue inschakelt?", "needs_translation": "false"}, "fail_time": {"english": "Fail time", "translation": "<PERSON><PERSON> tijd", "needs_translation": "false"}, "pull_up": {"english": "Pull-up", "translation": "Omhoog", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "help_p7": {"english": "Rate and Accel: Max rotation and acceleration rates when leveling during rescue.", "translation": "Rate and Accel: Maximale rotatie- en acceleratiesnelheden bij het nivelleren tijdens een reddingsactie./n/n", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Tijd", "needs_translation": "false"}, "accel": {"english": "Accel", "translation": "Accel", "needs_translation": "false"}}, "trim": {"disable_mixer_message": {"english": "Return control of the servos to the flight controller.", "translation": "<PERSON><PERSON> de besturing van de servo's te<PERSON> aan de vliegcontroller.", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Motor idle  %", "translation": "Tail Motor idle  %", "needs_translation": "false"}, "disable_mixer_override": {"english": "Disable mixer override", "translation": "<PERSON><PERSON><PERSON> mixer <PERSON><PERSON><PERSON><PERSON><PERSON> uit", "needs_translation": "false"}, "yaw_trim": {"english": "Yaw. trim %", "translation": "Yaw. trim %", "needs_translation": "false"}, "enable_mixer_message": {"english": "Set all servos to their configured center position. \r\n\r\nThis will result in all values on this page being saved when adjusting the servo trim.", "translation": "Zet alle servo's in de geconfigureerde middenpositie. \r\n\r\nDit heeft tot gevolg dat alle waarden op deze pagina worden opgeslagen bij het aanpassen van de servo-trim.", "needs_translation": "false"}, "mixer_override_disabling": {"english": "Disabling mixer override...", "translation": "Uitschakelen mixer overschrijven...", "needs_translation": "false"}, "roll_trim": {"english": "Roll trim %", "translation": "Roll trim %", "needs_translation": "false"}, "pitch_trim": {"english": "Pitch trim %", "translation": "Pitch trim %", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Motorised tail: If using a motorised tail, use this to set the minimum idle speed and zero yaw.", "translation": "Gemotoriseerde staart: Als u een gemotoriseerde staart gebruikt, kunt u hiermee het minimale stationair toerental en de nul yaw instellen.\n\n", "needs_translation": "false"}, "mixer_override": {"english": "Mixer Override", "translation": "Mixer Override", "needs_translation": "false"}, "mixer_override_enabling": {"english": "Enabling mixer override...", "translation": "Inschakelen mixer overschrijven...", "needs_translation": "false"}, "enable_mixer_override": {"english": "Enable mixer override", "translation": "<PERSON><PERSON><PERSON> mixer <PERSON><PERSON><PERSON><PERSON><PERSON> in", "needs_translation": "false"}, "collective_trim": {"english": "Col. trim %", "translation": "Col. trim %", "needs_translation": "false"}, "help_p1": {"english": "Link trims: Use to trim out small leveling issues in your swash plate. Typically only used if the swash links are non-adjustable.", "translation": "Link trims: Gebruiken om kleine afwijkingen in uw swashplate bij te werken. Wordt doorgaans alleen gebruikt als de swashlinks niet verstel<PERSON> zijn.", "needs_translation": "false"}}, "governor": {"help_p1": {"english": "These parameters apply globally to the governor regardless of the profile in use.", "translation": "Deze <PERSON> zijn van toe<PERSON> op de governor, ongeacht het gebruikte profiel.", "needs_translation": "false"}, "handover_throttle": {"english": "Handover throttle%", "translation": "Overdracht throttle%", "needs_translation": "false"}, "spoolup_min_throttle": {"english": "Spoolup min throttle%", "translation": "Min opstart throttle%", "needs_translation": "false"}, "recovery_time": {"english": "Recovery time", "translation": "<PERSON><PERSON><PERSON> tijd", "needs_translation": "false"}, "mode": {"english": "Mode", "translation": "Mode", "needs_translation": "false"}, "help_p2": {"english": "Each parameter is simply a time value in seconds for each governor action.", "translation": "Elke parameter is een tij<PERSON><PERSON> in seconden voor elke governor actie.\n\n", "needs_translation": "false"}, "tracking_time": {"english": "Tracking time", "translation": "Tracking time", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "startup_time": {"english": "Startup time", "translation": "Opstarttijd", "needs_translation": "false"}, "spoolup_time": {"english": "Spoolup time", "translation": "<PERSON><PERSON><PERSON> tijd", "needs_translation": "false"}}, "accelerometer": {"help_p1": {"english": "The accelerometer is used to measure the angle of the flight controller in relation to the horizon. This data is used to stabilize the aircraft and provide self-leveling functionality.", "translation": "De accelerometer wordt gebruikt om de hoek van de flight controller te meten ten opzichte van de horizon. Deze data wordt gebruikt om het vliegtuig te stabiliseren en zelf leveling functionaliteit te bieden.", "needs_translation": "false"}, "name": {"english": "Accelerometer", "translation": "Versnellingsmeter", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "msg_calibrate": {"english": "Calibrate the accelerometer?", "translation": "<PERSON><PERSON>er de versnellingsmeter?", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}}, "rates": {"help_table_5_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Max Rate: Maximale rotatiesnelheid bij volledige stickuitslag in graden per seconde.", "needs_translation": "false"}, "actual": {"english": "ACTUAL", "translation": "ACTUAL", "needs_translation": "false"}, "max_rate": {"english": "Max Rate", "translation": "Max Rate", "needs_translation": "false"}, "help_table_4_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: <PERSON><PERSON><PERSON><PERSON> in het midden van de stick, waar nauwkeurige bediening nodig is.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "help_table_5_p1": {"english": "RC Rate: Use to reduce sensitivity around center stick. RC Rate set to one half of the Max Rate is linear. A lower number will reduce sensitivity around center stick. Higher than one half of the Max Rate will also increase the Max Rate.", "translation": "RC Rate: Gebruik om de gevoeligheid rond de center stick te verminderen. RC Rate ingesteld op de helft van de Max Rate is lineair. Een lager getal zal de gevoeligheid rond de center stick verminderen. Hoger dan de helft van de Max Rate zal ook de Max Rate verhogen.", "needs_translation": "false"}, "help_table_4_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Max Rate: Maximale rotatiesnelheid bij volledige stickuitslag in graden per seconde.", "needs_translation": "false"}, "center_sensitivity": {"english": "Cntr. Sens.", "translation": "Cntr. Sens.", "needs_translation": "false"}, "rc_curve": {"english": "RC Curve", "translation": "RC Curve", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "none": {"english": "NONE", "translation": "NONE", "needs_translation": "false"}, "betaflight": {"english": "BETAFLIGHT", "translation": "BETAFLIGHT", "needs_translation": "false"}, "kiss": {"english": "KISS", "translation": "KISS", "needs_translation": "false"}, "help_table_1_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: <PERSON><PERSON><PERSON><PERSON> in het midden van de stick, waar nauwkeurige bediening nodig is.", "needs_translation": "false"}, "help_table_3_p2": {"english": "Rate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "Rate: Verhoogt de maximale rotatiesnelheid en vermindert de gevoeligheid tot ongeveer de helft van de <PERSON>.", "needs_translation": "false"}, "help_table_2_p2": {"english": "Acro+: Increases the maximum rotation rate while reducing sensitivity around half stick.", "translation": "Acro+: Verhoogt de maximale rotatiesnelheid terwij<PERSON> de gevoeligheid met onge<PERSON><PERSON> de helft wordt verlaagd.", "needs_translation": "false"}, "superrate": {"english": "SuperRate", "translation": "SuperRate", "needs_translation": "false"}, "help_table_2_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: <PERSON><PERSON><PERSON><PERSON> in het midden van de stick, waar nauwkeurige bediening nodig is.", "needs_translation": "false"}, "raceflight": {"english": "RACEFLIGHT", "translation": "RACEFLIGHT", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "collective": {"english": "Col", "translation": "Col", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "Rates", "needs_translation": "false"}, "help_table_5_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: <PERSON><PERSON><PERSON><PERSON> in het midden van de stick, waar nauwkeurige bediening nodig is.\n\n", "needs_translation": "false"}, "help_table_3_p3": {"english": "RC Curve: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "RC Curve: <PERSON><PERSON><PERSON><PERSON> in het midden van de stick, waar nauwkeurige bediening nodig is.", "needs_translation": "false"}, "expo": {"english": "Expo", "translation": "Expo", "needs_translation": "false"}, "help_table_1_p2": {"english": "SuperRate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "SuperRate: Verhoogt de maximale rotatiesnelheid en vermindert de gevoeligheid tot ongeveer de helft van de <PERSON>.", "needs_translation": "false"}, "help_default_p2": {"english": "We will use the sub keys below.", "translation": "We gebruiken de onderstaande subsleutels.", "needs_translation": "false"}, "help_default_p1": {"english": "Default: We keep this to make button appear for rates.", "translation": "Default: We gebruiken dit zodat de knop voor de rates zichtbaar is.", "needs_translation": "false"}, "quick": {"english": "QUICK", "translation": "QUICK", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "acroplus": {"english": "Acro+", "translation": "Acro+", "needs_translation": "false"}, "help_table_1_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "RC Rate: Maximale rotatie rate met volledige stick input.", "needs_translation": "false"}, "rc_rate": {"english": "RC Rate", "translation": "RC Rate", "needs_translation": "false"}, "help_table_2_p1": {"english": "Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Rate: Maximale rotatiesnelheid bij volledige stickuitslag in graden per seconde.", "needs_translation": "false"}, "help_table_4_p1": {"english": "Center Sensitivity: Use to reduce sensitivity around center stick. Set Center Sensitivity to the same as Max Rate for a linear response. A lower number than Max Rate will reduce sensitivity around center stick. Note that higher than Max Rate will increase the Max Rate - not recommended as it causes issues in the Blackbox log.", "translation": "Center Sensitivity: Gebruik om de gevoeligheid rond de center stick te verminderen. Stel Center Sensitivity in op hetzelfde als Max Rate voor een lineaire respons. Een lager getal dan Max Rate zal de gevoeligheid rond de center stick verminderen. Let op dat hoger dan Max Rate de Max Rate zal verhogen - niet aanbevolen omdat het problemen veroorzaakt in het Blackbox-logboek.", "needs_translation": "false"}, "help_table_0_p1": {"english": "All values are set to zero because no RATE TABLE is in use.", "translation": "Alle waarden zijn op nul gezet omdat er geen RATE TABEL in gebruik is.", "needs_translation": "false"}, "help_table_3_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "RC Rate: Maximale rotatie bij volledige stick inut.", "needs_translation": "false"}}, "mixer": {"help_p1": {"english": "Adust swash plate geometry, phase angles, and limits.", "translation": "Adust swash plate geometry, phase angles, and limits.", "needs_translation": "false"}, "collective_tilt_correction_pos": {"english": "Positive", "translation": "Positive", "needs_translation": "false"}, "geo_correction": {"english": "Geo Correction", "translation": "Geo Correction", "needs_translation": "false"}, "swash_tta_precomp": {"english": "TTA Precomp", "translation": "TTA Precomp", "needs_translation": "false"}, "name": {"english": "Mixer", "translation": "Mixer", "needs_translation": "false"}, "collective_tilt_correction_neg": {"english": "Negative", "translation": "Negative", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Idle Thr%", "translation": "Tail Idle Thr%", "needs_translation": "false"}, "swash_phase": {"english": "Phase Angle", "translation": "Phase Angle", "needs_translation": "false"}, "collective_tilt_correction": {"english": "Collective Tilt Correction", "translation": "Collective Tilt Correction", "needs_translation": "false"}, "swash_pitch_limit": {"english": "Total Pitch Limit", "translation": "Total Pitch Limit", "needs_translation": "false"}}, "about": {"help_p1": {"english": "This page provides some useful information that you may be asked for when requesting support.", "translation": "Op deze pagina vindt u nuttige informatie die u mogelijk wordt gevraagd wanneer u ondersteuning nodig heeft", "needs_translation": "false"}, "msgbox_credits": {"english": "Credits", "translation": "Credits", "needs_translation": "false"}, "ethos_version": {"english": "Ethos Version", "translation": "<PERSON><PERSON><PERSON> V<PERSON>", "needs_translation": "false"}, "rf_version": {"english": "Rotorflight Version", "translation": "Rotorflight Versie", "needs_translation": "false"}, "fc_version": {"english": "FC Version", "translation": "FC Versie", "needs_translation": "false"}, "name": {"english": "About", "translation": "Over", "needs_translation": "false"}, "supported_versions": {"english": "Supported MSP Versions", "translation": "Ondersteunde MSP Versies", "needs_translation": "false"}, "license": {"english": "You may copy, distribute, and modify the software as long as you track changes/dates in source files. Any modifications to or software including (via compiler) GPL-licensed code must also be made available under the GPL along with build & install instructions.", "translation": "You may copy, distribute, and modify the software as long as you track changes/dates in source files. Any modifications to or software including (via compiler) GPL-licensed code must also be made available under the GPL along with build & install instructions.", "needs_translation": "false"}, "simulation": {"english": "Simulation", "translation": "Simulatie", "needs_translation": "false"}, "help_p2": {"english": "For support, please first read the help pages on www.rotorflight.org", "translation": "<PERSON><PERSON>, lees eerst de helppagina's op www.rotorflight.org\n\n", "needs_translation": "false"}, "opener": {"english": "Rotorflight is an open source project. Contribution from other like minded people, keen to assist in making this software even better, is welcomed and encouraged. You do not have to be a hardcore programmer to help.", "translation": "Rotorflight is een open source project. <PERSON><PERSON><PERSON><PERSON><PERSON> van <PERSON>, die graag willen helpen deze software nog beter te maken, zijn welkom en worden aangemoedigd. Je hoeft geen hardcore programmeur te zijn om te helpen.", "needs_translation": "false"}, "version": {"english": "Version", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "msp_version": {"english": "MSP Version", "translation": "MSP Versie", "needs_translation": "false"}, "credits": {"english": "Notable contributors to both the Rotorflight firmware and this software are: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... and many more who have spent hours testing and providing feedback!", "translation": "Bekende personen die aan de Rotorflight-firmware en deze software hebben bijgedragen zijn: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... en nog veel meer mensen die urenlang hebben getest en feedback hebben gegeven!", "needs_translation": "false"}, "msp_transport": {"english": "MSP Transport", "translation": "MSP Transport", "needs_translation": "false"}}, "rates_advanced": {"dyn_ceiling_gain": {"english": "Dynamic ceiling gain", "translation": "DynPlafVerst", "needs_translation": "false"}, "acc_limit": {"english": "Accelerometer Limit", "translation": "VersnGrens", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "yaw_dynamics": {"english": "Yaw dynamics", "translation": "Yaw dynamics", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "<PERSON>", "needs_translation": "false"}, "col": {"english": "Col", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "setpoint_boost_cutoff": {"english": "Setpoint boost cutoff", "translation": "SetpAfkap", "needs_translation": "false"}, "yaw_dynamic_deadband_gain": {"english": "D. <PERSON>", "translation": "Dode band", "needs_translation": "false"}, "rates_type": {"english": "Rates Type", "translation": "Rates Type", "needs_translation": "false"}, "setpoint_boost_gain": {"english": "Setpoint boost gain", "translation": "SetpVersterk", "needs_translation": "false"}, "msg_reset_to_defaults": {"english": "Rate type changed. Values will be reset to defaults.", "translation": "Rate type changed. Values will be reset to defaults.", "needs_translation": "false"}, "yaw_dynamic_ceiling_gain": {"english": "Ceiling", "translation": "Plafond", "needs_translation": "false"}, "yaw_boost": {"english": "Yaw boost", "translation": "Yaw boost", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "Versterk.", "needs_translation": "false"}, "rate_table": {"english": "Rate Table", "translation": "Tarieftabel", "needs_translation": "false"}, "dynamics": {"english": "Dynamics", "translation": "Dynamics", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "yaw_dynamic_deadband_filter": {"english": "Filter", "translation": "Filter", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "Rates", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_rate_table": {"english": "Please select the rate you would like to use. Saving will apply the choice to the active profile.", "translation": "Selecteer het tarief dat u wilt gebruiken. Opslaan zal de keuze toepassen op het actieve profiel.", "needs_translation": "false"}, "help_p1": {"english": "Rates type: Choose the rate type you prefer flying with. Raceflight and Actual are the most straightforward.", "translation": "Rates type: Choose the rate type you prefer flying with. Raceflight and Actual are the most straightforward.", "needs_translation": "false"}, "pitch_boost": {"english": "Pitch boost", "translation": "Pitch boost", "needs_translation": "false"}, "help_p2": {"english": "Dynamics: Applied regardless of rates type. Typically left on defaults but can be adjusted to smooth heli movements, like with scale helis.", "translation": "Dynamics: Applied regardless of rates type. Typically left on defaults but can be adjusted to smooth heli movements, like with scale helis.\n\n", "needs_translation": "false"}, "accel_limit": {"english": "Accel", "translation": "Accel", "needs_translation": "false"}, "dyn_deadband_filter": {"english": "Dynamic deadband filter", "translation": "<PERSON>yn<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "roll_boost": {"english": "Roll boost", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "dyn_deadband_gain": {"english": "Dynamic deadband gain", "translation": "DynDodeVerst", "needs_translation": "false"}, "collective_dynamics": {"english": "Collective dynamics", "translation": "Collective dynamics", "needs_translation": "false"}, "roll_dynamics": {"english": "Roll dynamics", "translation": "Roll dynamics", "needs_translation": "false"}, "collective_boost": {"english": "Collective boost", "translation": "Collective boost", "needs_translation": "false"}, "pitch_dynamics": {"english": "Pitch dynamics", "translation": "Pitch dynamics", "needs_translation": "false"}, "response_time": {"english": "Response Time", "translation": "ReactieTijd", "needs_translation": "false"}}, "servos": {"tbl_yes": {"english": "YES", "translation": "YES", "needs_translation": "false"}, "enable_servo_override": {"english": "Enable servo override", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "disabling_servo_override": {"english": "Disabling servo override...", "translation": "<PERSON>vo overschrijven uitschakelen...", "needs_translation": "false"}, "help_tool_p3": {"english": "Minimum/Maximum: Adjust the end points of the selected servo.", "translation": "Minimum/Maximum: <PERSON><PERSON> <PERSON> van de geselecteerde servo aan.", "needs_translation": "false"}, "tail": {"english": "TAIL", "translation": "TAIL", "needs_translation": "false"}, "scale_negative": {"english": "Scale Negative", "translation": "<PERSON><PERSON><PERSON> negatief", "needs_translation": "false"}, "help_tool_p1": {"english": "Override: [*] Enable override to allow real-time updates of servo center point.", "translation": "Override: [*] <PERSON><PERSON><PERSON> override in om realtime veranderingen van het servo-middenpunt toe te staan.", "needs_translation": "false"}, "tbl_no": {"english": "NO", "translation": "NO", "needs_translation": "false"}, "maximum": {"english": "Maximum", "translation": "Maximum", "needs_translation": "false"}, "help_tool_p6": {"english": "Speed: The speed the servo moves. Generally only used for the cyclic servos to help the swash move evenly. Optional - leave all at 0 if unsure.", "translation": "Snelheid: De snelheid waarmee de servo beweegt. Wordt over het algemeen alleen gebruikt voor cyclische servo's om de swash gelijkmatig te laten bewegen. Optioneel - laat alles op 0 staan ​​als u het niet zeker weet.", "needs_translation": "false"}, "help_fields_rate": {"english": "Servo PWM rate.", "translation": "Servo PWM snelheid.", "needs_translation": "false"}, "cyc_pitch": {"english": "CYC.PITCH", "translation": "CYC. PITCH", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Center", "needs_translation": "false"}, "minimum": {"english": "Minimum", "translation": "Minimum", "needs_translation": "false"}, "speed": {"english": "Speed", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_fields_speed": {"english": "Servo motion speed in milliseconds.", "translation": "Servo bewegingssnelheid in milliseconds.", "needs_translation": "false"}, "disable_servo_override": {"english": "Disable servo override", "translation": "<PERSON><PERSON> overs<PERSON>", "needs_translation": "false"}, "help_fields_scale_pos": {"english": "Servo positive scaling.", "translation": "Servo positief schalen.", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "Opslaan data...", "needs_translation": "false"}, "cyc_left": {"english": "CYC.LEFT", "translation": "CYC. LEFT", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "Opsla<PERSON>", "needs_translation": "false"}, "name": {"english": "Servos", "translation": "Servos", "needs_translation": "false"}, "help_tool_p5": {"english": "Rate: The frequency the servo runs best at - check with manufacturer.", "translation": "Rotatiesnelheid: Op welke frequentie de servo het beste werkt, kunt u navragen bij de fabrikant.", "needs_translation": "false"}, "help_tool_p2": {"english": "Center: Adjust the center position of the servo.", "translation": "Center: <PERSON><PERSON> <PERSON> middenposi<PERSON> van de servo aan.", "needs_translation": "false"}, "enabling_servo_override": {"english": "Enabling servo override...", "translation": "<PERSON>vo overs<PERSON>ven inschakelen...", "needs_translation": "false"}, "servo_prefix": {"english": "SERVO ", "translation": "SERVO ", "needs_translation": "false"}, "reverse": {"english": "Reverse", "translation": "Omgekeerd", "needs_translation": "false"}, "enable_servo_override_msg": {"english": "Servo override allows you to 'trim' your servo center point in real time.", "translation": "<PERSON><PERSON> overschrijven <PERSON> kunt u het middelpunt van uw servo in realtime 'trimmen'.", "needs_translation": "false"}, "cyc_right": {"english": "CYC.RIGHT", "translation": "CYC. RIGHT", "needs_translation": "false"}, "help_default_p2": {"english": "Primary flight controls that use the rotorflight mixer will display in the section called 'mixer'.", "translation": "Primaire vluchtbesturingen die gebruikmaken van de rotorflight-mixer worden weergegeven in het gedeelte 'mixer'.", "needs_translation": "false"}, "scale_positive": {"english": "Scale Positive", "translation": "Schaal positief", "needs_translation": "false"}, "help_default_p1": {"english": "Please select the servo you would like to configure from the list below.", "translation": "Selecteer de servo die u wilt configureren uit de onderstaande lijst.", "needs_translation": "false"}, "servo_override": {"english": "Servo Override", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "disable_servo_override_msg": {"english": "Return control of the servos to the flight controller.", "translation": "<PERSON><PERSON> de besturing van de servo's te<PERSON> aan de vliegcontroller.", "needs_translation": "false"}, "help_fields_min": {"english": "Servo negative travel limit.", "translation": "Servo negatief limiet.", "needs_translation": "false"}, "help_default_p3": {"english": "Any other servos that are not controlled by the primary flight mixer will be displayed in the section called 'Other servos'.", "translation": "Alle andere servo's die niet door de primaire vluchtmixer worden aangestuurd, worden weergegeven in het gedeelte 'Andere servos'.", "needs_translation": "false"}, "help_fields_mid": {"english": "Servo center position pulse width.", "translation": "Pulsbreed<PERSON> van de servomiddenpositie.", "needs_translation": "false"}, "help_fields_scale_neg": {"english": "Servo negative scaling.", "translation": "Servo negatief schalen.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "PWM snelheid", "needs_translation": "false"}, "help_tool_p4": {"english": "Scale: Adjust the amount the servo moves for a given input.", "translation": "Schaal: <PERSON><PERSON> de mate aan waarin de servo beweegt voor een bepaalde invoer.", "needs_translation": "false"}, "help_fields_flags": {"english": "0 = <PERSON><PERSON><PERSON>, 1=Reverse, 2 = Geo Correction, 3 = Reverse + Geo Correction", "translation": "0 = <PERSON><PERSON><PERSON>, 1=Reverse, 2 = Geo Correction, 3 = Reverse + Geo Correction\n\n", "needs_translation": "false"}, "geometry": {"english": "Geometry", "translation": "Geometrie", "needs_translation": "false"}, "help_fields_max": {"english": "Servo positive travel limit.", "translation": "Servo positief limiet.", "needs_translation": "false"}}, "profile_autolevel": {"acro_trainer": {"english": "Acro trainer", "translation": "Acro trainer", "needs_translation": "false"}, "angle_mode": {"english": "Angle mode", "translation": "Angle mode", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "name": {"english": "Autolevel", "translation": "Autolevel", "needs_translation": "false"}, "help_p1": {"english": "Acro Trainer: How aggressively the heli tilts back to level when flying in Acro Trainer Mode.", "translation": "Acro Trainer: Ho<PERSON> agressief de heli terugkantelt naar een horizontale positie tijdens het vliegen in de Acro Trainer-modus.", "needs_translation": "false"}, "horizon_mode": {"english": "Horizon mode", "translation": "Horizon mode", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Angle Mode: How aggressively the heli tilts back to level when flying in Angle Mode.", "translation": "Angle Mode: Hoe agressief de heli terug kantelt naar een horizontaal wanneer  in de angled mode gevlogen word.", "needs_translation": "false"}, "help_p3": {"english": "Horizon Mode: How aggressively the heli tilts back to level when flying in Horizon Mode.", "translation": "Horizon Mode: Ho<PERSON> agressief de heli terug kantelt naar horizontaal tijdens het vliegen in de Horizon modus.\n\n", "needs_translation": "false"}}, "filters": {"filter_type": {"english": "Filter type", "translation": "Filter type", "needs_translation": "false"}, "help_p4": {"english": "Dynamic Notch Filters: Automatically creates notch filters within the min and max frequency range.", "translation": "Dynamic Notch Filters: <PERSON><PERSON>t automatisch notchfilters aan binnen het minimale en maximale frequentiebereik.\n\n", "needs_translation": "false"}, "notch_c": {"english": "Notch Count", "translation": "Notch aantal", "needs_translation": "false"}, "rpm_preset": {"english": "Type", "translation": "Type", "needs_translation": "false"}, "lowpass_1": {"english": "Lowpass 1", "translation": "Lowpass 1", "needs_translation": "false"}, "rpm_min_hz": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Gyro lowpass: Lowpass filters for the gyro signal. Typically left at default.", "translation": "Gyro lowpass: Lowpassfilters voor het gyrosignaal. Meestal op standaard gelaten.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "notch_1": {"english": "Notch 1", "translation": "Notch 1", "needs_translation": "false"}, "max_cutoff": {"english": "Max cutoff", "translation": "Max cutoff", "needs_translation": "false"}, "help_p3": {"english": "Gyro notch filters: Use for filtering specific frequency ranges. Typically not needed in most helis.", "translation": "Gyro notch filters: Gebruik voor het filteren van specifieke frequentiebereiken. Me<PERSON>l niet nodig in de meeste heli's.", "needs_translation": "false"}, "lowpass_2": {"english": "Lowpass 2", "translation": "Lowpass 2", "needs_translation": "false"}, "rpm_filter": {"english": "RPM filter", "translation": "RPM filter", "needs_translation": "false"}, "help_p1": {"english": "Typically you would not edit this page without checking your Blackbox logs!", "translation": "Normaal gesproken zou u deze pagina niet bewerken zonder eerst uw Blackbox log te controleren!", "needs_translation": "false"}, "dyn_notch": {"english": "Dynamic Filters", "translation": "Dynamische Filters", "needs_translation": "false"}, "notch_q": {"english": "Notch Q", "translation": "Notch Q", "needs_translation": "false"}, "lowpass_1_dyn": {"english": "Lowpass 1 dyn.", "translation": "Lowpass 1 dyn.", "needs_translation": "false"}, "notch_min_hz": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "notch_max_hz": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "notch_2": {"english": "Notch 2", "translation": "Notch 2", "needs_translation": "false"}, "name": {"english": "Filters", "translation": "Filters", "needs_translation": "false"}, "min_cutoff": {"english": "Min cutoff", "translation": "Min cutoff", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Center", "needs_translation": "false"}}, "status": {"arming_disable_flag_3": {"english": "Bad RX Recovery", "translation": "Bad RX Recovery", "needs_translation": "false"}, "arming_disable_flag_20": {"english": "RPM Filter", "translation": "RPM Filter", "needs_translation": "false"}, "arming_disable_flag_11": {"english": "Load", "translation": "Belasting", "needs_translation": "false"}, "arming_disable_flag_22": {"english": "DSHOT Bitbang", "translation": "DSHOT Bitbang", "needs_translation": "false"}, "dataflash_free_space": {"english": "Dataflash Free Space", "translation": "Dataflash vrije rui<PERSON>te", "needs_translation": "false"}, "arming_disable_flag_25": {"english": "Arm Switch", "translation": "<PERSON>", "needs_translation": "false"}, "erasing": {"english": "Erasing", "translation": "Wissen", "needs_translation": "false"}, "arming_disable_flag_9": {"english": "Boot <PERSON>", "translation": "Boot <PERSON>", "needs_translation": "false"}, "megabyte": {"english": "MB", "translation": "MB", "needs_translation": "false"}, "arming_disable_flag_17": {"english": "Paralyze", "translation": "Paralyze", "needs_translation": "false"}, "arming_disable_flag_5": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "arming_disable_flag_8": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_1": {"english": "Fail Safe", "translation": "Fail Safe", "needs_translation": "false"}, "cpu_load": {"english": "CPU Load", "translation": "CPU belasting", "needs_translation": "false"}, "arming_disable_flag_15": {"english": "BST", "translation": "BST", "needs_translation": "false"}, "arming_disable_flag_12": {"english": "Calibrating", "translation": "Calibreren", "needs_translation": "false"}, "arming_disable_flag_19": {"english": "Resc", "translation": "Resc", "needs_translation": "false"}, "arming_disable_flag_4": {"english": "Box Fail Safe", "translation": "Box Fail Safe", "needs_translation": "false"}, "arming_disable_flag_24": {"english": "Motor Protocol", "translation": "Motor Protocol", "needs_translation": "false"}, "real_time_load": {"english": "Real-time Load", "translation": "Real-time belasting", "needs_translation": "false"}, "help_p2": {"english": "To erase the dataflash for more log file storage, press the button on the menu denoted by a '*'.", "translation": "Om de dataflash te wissen voor meer opslag van logbestanden, drukt u op de knop in het menu die wordt aangegeven met een '*'.\n\n", "needs_translation": "false"}, "arming_disable_flag_2": {"english": "RX Fail Safe", "translation": "RX Fail Safe", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "arming_disable_flag_0": {"english": "No Gyro", "translation": "No Gyro", "needs_translation": "false"}, "arming_disable_flag_18": {"english": "GPS", "translation": "GPS", "needs_translation": "false"}, "help_p1": {"english": "Use this page to view your current flight controller status. This can be useful when determining why your heli will not arm.", "translation": "Gebruik deze pagina om uw huidige flight controller status te bekijken. Dit kan handig zijn om te bepalen waarom heli niet wilt armen", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Arming Flags", "needs_translation": "false"}, "unsupported": {"english": "Unsupported", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "erase_prompt": {"english": "Would you like to erase the dataflash?", "translation": "Wilt u de dataflash wissen?", "needs_translation": "false"}, "erase": {"english": "Erase", "translation": "Wissen", "needs_translation": "false"}, "arming_disable_flag_10": {"english": "No Pre Arm", "translation": "No Pre Arm", "needs_translation": "false"}, "arming_disable_flag_21": {"english": "Reboot Required", "translation": "Herstart vereist", "needs_translation": "false"}, "name": {"english": "Status", "translation": "Status", "needs_translation": "false"}, "arming_disable_flag_13": {"english": "CLI", "translation": "CLI", "needs_translation": "false"}, "arming_disable_flag_14": {"english": "CMS Menu", "translation": "CMS Menu", "needs_translation": "false"}, "arming_disable_flag_16": {"english": "MSP", "translation": "MSP", "needs_translation": "false"}, "arming_disable_flag_7": {"english": "<PERSON>hrottle", "translation": "<PERSON>hrottle", "needs_translation": "false"}, "erasing_dataflash": {"english": "Erasing dataflash...", "translation": "Wissen dataflash...", "needs_translation": "false"}, "arming_disable_flag_23": {"english": "Acc Calibration", "translation": "Acc Calibratie", "needs_translation": "false"}}, "profile_pidbandwidth": {"help_p1": {"english": "PID Bandwidth: Overall bandwidth in HZ used by the PID loop.", "translation": "PID Bandwidth: Totale bandbreedte in Hz die door de PID-loop wordt gebruikt.", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "name": {"english": "PID Bandwidth", "translation": "PID Bandbreedte", "needs_translation": "false"}, "bterm_cutoff": {"english": "B-term cut-off", "translation": "B-term cut-off", "needs_translation": "false"}, "help_p3": {"english": "B-term cutoff: B-term cutoff frequency in HZ.", "translation": "B-term cutoff: B-term afsnijdingsfrequentie in HZ.\n\n", "needs_translation": "false"}, "dterm_cutoff": {"english": "D-term cut-off", "translation": "D-term cut-off", "needs_translation": "false"}, "help_p2": {"english": "D-term cutoff: D-term cutoff frequency in HZ.", "translation": "D-term cutoff: D-term afsnijdingsfrequentie in HZ.", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}}}, "navigation_save": {"english": "SAVE", "translation": "Save", "needs_translation": "false"}, "menu_section_flight_tuning": {"english": "Flight Tuning", "translation": "Vlucht Tuning", "needs_translation": "false"}, "error_timed_out": {"english": "Error: timed out", "translation": "Error: timed out", "needs_translation": "false"}, "check_rf_module_on": {"english": "Please check your rf module is turned on.", "translation": "Controleer of de rf module aanstaat.", "needs_translation": "false"}, "msg_saving": {"english": "Saving...", "translation": "Opslaan...", "needs_translation": "false"}, "msg_save_not_commited": {"english": "Save not committed to EEPROM", "translation": "Opslaan niet gelukt naar EEPROM", "needs_translation": "false"}, "menu_section_advanced": {"english": "Advanced", "translation": "Geavanceerd", "needs_translation": "false"}, "msg_loading_from_fbl": {"english": "Loading data from flight controller...", "translation": "Laden data van vliegcontroller...", "needs_translation": "false"}, "msg_reload_settings": {"english": "Reload data from flight controller?", "translation": "Herlaad data van vliegcontroller?", "needs_translation": "false"}, "menu_section_tools": {"english": "Tools", "translation": "Tools", "needs_translation": "false"}, "msg_connecting": {"english": "Connecting", "translation": "Verbinden", "needs_translation": "false"}, "msg_save_current_page": {"english": "Save current page to flight controller?", "translation": "Sla huidige pagina op in de vliegcontroller?", "needs_translation": "false"}, "btn_ok_long": {"english": "                OK                ", "translation": "                OK                ", "needs_translation": "false"}, "check_discovered_sensors": {"english": "Please check you have discovered all sensors.", "translation": "Controleer of je alle sensors discovered hebt.", "needs_translation": "false"}, "msg_loading": {"english": "Loading...", "translation": "Laden...", "needs_translation": "false"}, "check_heli_on": {"english": "Please check your heli is powered up and radio connected.", "translation": "Controleer of de heli aanstaat en de radio is verbonden.", "needs_translation": "false"}, "check_bg_task": {"english": "Please enable the background task.", "translation": "<PERSON><PERSON><PERSON> taak in.", "needs_translation": "false"}, "navigation_tools": {"english": "*", "translation": "*", "needs_translation": "false"}, "check_supported_version": {"english": "This version of the Lua script \ncan't be used with the selected model", "translation": "De<PERSON> versie van het lua script \n kan niet gebruikt worden met het gese<PERSON><PERSON><PERSON> model", "needs_translation": "false"}}