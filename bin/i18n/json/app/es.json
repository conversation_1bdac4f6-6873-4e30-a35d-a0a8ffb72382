{"btn_ok": {"english": "          OK           ", "translation": "          OK           ", "needs_translation": "false"}, "btn_close": {"english": "CLOSE", "translation": "CERRAR", "needs_translation": "false"}, "navigation_menu": {"english": "MENU", "translation": "Menú", "needs_translation": "false"}, "menu_section_hardware": {"english": "Hardware", "translation": "Hardware", "needs_translation": "false"}, "msg_please_disarm_to_save_warning": {"english": "Settings will only be saved to e<PERSON>rom on disarm", "translation": "La configuración solo se guardara en EEPROM al desarmar", "needs_translation": "false"}, "msg_saving_settings": {"english": "Saving settings...", "translation": "Guardando configuración...", "needs_translation": "false"}, "msg_saving_to_fbl": {"english": "Saving data to flight controller...", "translation": "Guardando datos en el controlador de vuelo...", "needs_translation": "false"}, "navigation_reload": {"english": "RELOAD", "translation": "Recarga", "needs_translation": "false"}, "menu_section_developer": {"english": "Developer", "translation": "Desarrollador", "needs_translation": "false"}, "check_msp_version": {"english": "Unable to determine MSP version in use.", "translation": "Imposible determinar la versión MSP en uso.", "needs_translation": "false"}, "menu_section_about": {"english": "About", "translation": "Acerca de", "needs_translation": "false"}, "msg_please_disarm_to_save": {"english": "Please disarm to save", "translation": "Desarme para guardar a fin de asegurar la integridad de los datos.", "needs_translation": "false"}, "unit_hertz": {"english": "Hz", "translation": "Hz", "needs_translation": "false"}, "msg_rebooting": {"english": "Rebooting...", "translation": "Reiniciando...", "needs_translation": "false"}, "msg_save_settings": {"english": "Save settings", "translation": "Guardar configuración", "needs_translation": "false"}, "btn_cancel": {"english": "CANCEL", "translation": "CANCELAR", "needs_translation": "false"}, "msg_connecting_to_fbl": {"english": "Connecting to flight controller...", "translation": "Conectando al controlador de vuelo...", "needs_translation": "false"}, "navigation_help": {"english": "?", "translation": "?", "needs_translation": "false"}, "modules": {"stats": {"name": {"english": "Stats", "translation": "Estadísticas", "needs_translation": "false"}, "totalflighttime": {"english": "Total Flight Time", "translation": "Tiempo Total de Vuelo", "needs_translation": "false"}, "flightcount": {"english": "Flight Count", "translation": "Nro de Vuelos", "needs_translation": "false"}, "lastflighttime": {"english": "Last Flight Time", "translation": "Tiempo Ultimo Vuelo", "needs_translation": "false"}, "help_p1": {"english": "Use this module to update the recorded flight statistics on the flight controller.", "translation": "Utilice este módulo para actualizar las estadísticas guardadas en el controlador de vuelo.", "needs_translation": "false"}}, "settings": {"name": {"english": "Settings", "translation": "Configuración", "needs_translation": "false"}, "no_themes_available_to_configure": {"english": "No configurable themes installed on this device", "translation": "No hay temas configurables instalados en este dispositivo", "needs_translation": "false"}, "txt_audio_timer": {"english": "Timer", "translation": "Timer", "needs_translation": "false"}, "txt_audio_events": {"english": "Events", "translation": "Eventos", "needs_translation": "false"}, "txt_audio_switches": {"english": "Switches", "translation": "Botones", "needs_translation": "false"}, "txt_iconsize": {"english": "Icon Size", "translation": "Tamaño Icon", "needs_translation": "false"}, "txt_general": {"english": "General", "translation": "General", "needs_translation": "false"}, "txt_text": {"english": "TEXT", "translation": "TEXTO", "needs_translation": "false"}, "txt_small": {"english": "SMALL", "translation": "PEQUEÑO", "needs_translation": "false"}, "txt_large": {"english": "LARGE", "translation": "GRANDE", "needs_translation": "false"}, "txt_syncname": {"english": "Sync model name", "translation": "Sinc nombre modelo", "needs_translation": "false"}, "txt_devtools": {"english": "Developer Tools", "translation": "Herramientas Desarrollo", "needs_translation": "false"}, "txt_apiversion": {"english": "API Version", "translation": "Versión API", "needs_translation": "false"}, "txt_logging": {"english": "Logging", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "txt_compilation": {"english": "Compilation", "translation": "Compilación", "needs_translation": "false"}, "txt_loglocation": {"english": "Log location", "translation": "Posición Log", "needs_translation": "false"}, "txt_console": {"english": "CONSOLE", "translation": "CONSOLA", "needs_translation": "false"}, "txt_consolefile": {"english": "CONSOLE & FILE", "translation": "CONSOLA y ARCHIVO", "needs_translation": "false"}, "txt_loglevel": {"english": "Log level", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "txt_off": {"english": "OFF", "translation": "APAGADO", "needs_translation": "false"}, "txt_info": {"english": "INFO", "translation": "INFO", "needs_translation": "false"}, "txt_debug": {"english": "DEBUG", "translation": "DEBUG", "needs_translation": "false"}, "txt_mspdata": {"english": "Log msp data", "translation": "Log datos msp", "needs_translation": "false"}, "txt_queuesize": {"english": "Log MSP queue size", "translation": "Tamaño cola Log MSP", "needs_translation": "false"}, "txt_memusage": {"english": "Log memory usage", "translation": "Uso de memoria Log", "needs_translation": "false"}, "txt_batttype": {"english": "Tx Battery Options", "translation": "Tx Battery Options", "needs_translation": "true"}, "txt_battdef": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "true"}, "txt_batttext": {"english": "Text", "translation": "Text", "needs_translation": "true"}, "txt_battdig": {"english": "Digital", "translation": "Digital", "needs_translation": "true"}, "dashboard": {"english": "Dashboard", "translation": "Panel", "needs_translation": "false"}, "dashboard_theme": {"english": "Theme", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "dashboard_theme_panel_global": {"english": "Default theme for all models", "translation": "Tema por defecto Global", "needs_translation": "false"}, "dashboard_theme_panel_model": {"english": "Optional theme for this model", "translation": "Tema opcional para este modelo", "needs_translation": "false"}, "dashboard_theme_panel_model_disabled": {"english": "Disabled", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "dashboard_settings": {"english": "Settings", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "dashboard_theme_preflight": {"english": "Preflight Theme", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "dashboard_theme_inflight": {"english": "Inflight Theme", "translation": "Tema En-Vuelo", "needs_translation": "false"}, "dashboard_theme_postflight": {"english": "Postflight Theme", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "audio": {"english": "Audio", "translation": "Audio", "needs_translation": "false"}, "localizations": {"english": "Localization", "translation": "Localización", "needs_translation": "false"}, "txt_development": {"english": "Development", "translation": "Desarrollo", "needs_translation": "false"}, "temperature_unit": {"english": "Temperature Unit", "translation": "Unidad de Temperatura", "needs_translation": "false"}, "altitude_unit": {"english": "Altitude Unit", "translation": "Unidad de Altitud", "needs_translation": "false"}, "celcius": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "fahrenheit": {"english": "Fahrenheit", "translation": "Fahrenheit", "needs_translation": "false"}, "meters": {"english": "Meters", "translation": "Metros", "needs_translation": "false"}, "feet": {"english": "Feet", "translation": "Pies", "needs_translation": "false"}, "warning": {"english": "Warning", "translation": "Advertencia", "needs_translation": "false"}, "governor_state": {"english": "Governor State", "translation": "Estado del Governor", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Banderas de Armado", "needs_translation": "false"}, "voltage": {"english": "Voltage", "translation": "Voltaje", "needs_translation": "false"}, "pid_rates_profile": {"english": "PID/Rates Profile", "translation": "Perfil PID/Tasas", "needs_translation": "false"}, "pid_profile": {"english": "PID Profile", "translation": "Perfil PID", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "esc_temperature": {"english": "ESC Temperature", "translation": "Temperatura del ESC", "needs_translation": "false"}, "esc_threshold": {"english": "Thresh<PERSON> (°)", "translation": "Umbral (°)", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "Voltaje BEC", "needs_translation": "false"}, "bec_threshold": {"english": "<PERSON><PERSON><PERSON><PERSON> (V)", "translation": "Umbral (V)", "needs_translation": "false"}, "fuel": {"english": "Fuel", "translation": "Combustible", "needs_translation": "false"}, "fuel_callout_default": {"english": "Default (Only at 10%)", "translation": "<PERSON>r defecto (solo al 10%)", "needs_translation": "false"}, "fuel_callout_10": {"english": "Every 10%", "translation": "Cada 10%", "needs_translation": "false"}, "fuel_callout_20": {"english": "Every 20%", "translation": "Cada 20%", "needs_translation": "false"}, "fuel_callout_25": {"english": "Every 25%", "translation": "Cada 25%", "needs_translation": "false"}, "fuel_callout_50": {"english": "Every 50%", "translation": "Cada 50%", "needs_translation": "false"}, "fuel_callout_percent": {"english": "Callout %", "translation": "Anuncio %", "needs_translation": "false"}, "fuel_repeats_below": {"english": "Repeats below 0%", "translation": "Repite debajo de 0%", "needs_translation": "false"}, "fuel_haptic_below": {"english": "Haptic below 0%", "translation": "<PERSON><PERSON>rar debajo de 0%", "needs_translation": "false"}, "timer_alerting": {"english": "Timer Alerting", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "timer_elapsed_alert_mode": {"english": "Timer <PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "timer_prealert_options": {"english": "Pre-timer <PERSON><PERSON>", "translation": "Opciones de Alerta Pre-timer", "needs_translation": "false"}, "timer_prealert": {"english": "Pre-timer <PERSON><PERSON>", "translation": "Alerta Pre-timer", "needs_translation": "false"}, "timer_alert_period": {"english": "Alert <PERSON>", "translation": "Período <PERSON> Alerta", "needs_translation": "false"}, "timer_postalert_options": {"english": "Post-timer <PERSON><PERSON>", "translation": "Opciones Alerta Post-timer", "needs_translation": "false"}, "timer_postalert": {"english": "Post-timer <PERSON><PERSON>", "translation": "Alerta Post-timer", "needs_translation": "false"}, "timer_postalert_period": {"english": "Alert <PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "timer_postalert_interval": {"english": "<PERSON><PERSON>", "translation": "Intervalo Alerta", "needs_translation": "false"}}, "validate_sensors": {"help_p1": {"english": "This tool attempts to list all the sensors that you are not receiving in a concise list.", "translation": "Esta herramienta intenta listar todos los sensores necesarios que faltan.", "needs_translation": "false"}, "invalid": {"english": "INVALID", "translation": "INVALIDOS", "needs_translation": "false"}, "name": {"english": "Sensors", "translation": "Sen<PERSON><PERSON>", "needs_translation": "false"}, "msg_repair": {"english": "Enable required sensors on flight controller?", "translation": "Activa los sensores necesarios en el controlador de vuelo?", "needs_translation": "false"}, "msg_repair_fin": {"english": "The flight controller has been configured? You may need to perform a discover sensors to see the changes.", "translation": "El controlador de vuelo está configurado? Puede que necesite ejecutar 'Descubrir Sensores' para ver los cambios.", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "Use this tool to ensure you are sending the correct sensors.", "translation": "Utilice esta herramienta para asegurar que está enviando los sensores que corresponde.", "needs_translation": "false"}}, "msp_exp": {"help_p1": {"english": "This tool provides the ability to send a custom byte string to the flight controller. It is useful for developers when debugging values.", "translation": "Esta herramienta provee la capacidad de enviar un string de bytes al controlador de vuelo. Lo utilizan los desarrolladores durante el debugging.", "needs_translation": "false"}, "name": {"english": "MSP Expermental", "translation": "MSP Experimental", "needs_translation": "false"}, "help_p2": {"english": "If you do not understand what you are doing, do not use it as bad things can happen.", "translation": "Si no sabe lo que está haciendo no la use, podrian suceder cosas peligrosas.", "needs_translation": "false"}}, "esc_tools": {"unknown": {"english": "UNKNOWN", "translation": "DESCONOCIDO", "needs_translation": "false"}, "name": {"english": "ESC Tools", "translation": "Herramientas ESC", "needs_translation": "false"}, "please_powercycle": {"english": "Please power cycle the ESC...", "translation": "Apague y reencienda el ESC...", "needs_translation": "false"}, "mfg": {"hw5": {"esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "brake_force": {"english": "Brake Force%", "translation": "Fuerza frenado %", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Rotación", "needs_translation": "false"}, "soft_start": {"english": "Soft Start", "translation": "Arranque Su<PERSON>", "needs_translation": "false"}, "name": {"english": "Hobbywing V5", "translation": "Hobbywing V5", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Límites", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "Voltaje BEC", "needs_translation": "false"}, "gov_i_gain": {"english": "I-G<PERSON>", "translation": "Ganancia-I", "needs_translation": "false"}, "startup_time": {"english": "Startup Time", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "lipo_cell_count": {"english": "LiPo Cell Count", "translation": "Nro. <PERSON><PERSON><PERSON>", "needs_translation": "false"}, "restart_time": {"english": "Restart Time", "translation": "Tiempo Reinicio", "needs_translation": "false"}, "volt_cutoff_type": {"english": "Volt Cutoff Type", "translation": "Tipo Corte Voltaje", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "Motor", "needs_translation": "false"}, "brake_type": {"english": "Brake Type", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "brake": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Básico", "needs_translation": "false"}, "flight_mode": {"english": "Flight Mode", "translation": "<PERSON><PERSON> Vuelo", "needs_translation": "false"}, "auto_restart": {"english": "Auto Restart", "translation": "Reinicio Auto", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Corona Liberada", "needs_translation": "false"}, "cutoff_voltage": {"english": "Cutoff Voltage", "translation": "Voltaje de Corte", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Potencia de Inicio", "needs_translation": "false"}, "other": {"english": "Other", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Timing", "needs_translation": "false"}, "gov_p_gain": {"english": "P-Gain", "translation": "Ganancia-P", "needs_translation": "false"}}, "xdfly": {"hv_bec_voltage": {"english": "HV BEC Voltage", "translation": "Voltaje HV BEC", "needs_translation": "false"}, "gov": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "brake_force": {"english": "Brake Force", "translation": "Fuerza de Frenado", "needs_translation": "false"}, "sr_function": {"english": "SR Function", "translation": "Función SR", "needs_translation": "false"}, "name": {"english": "XDFLY", "translation": "XDFLY", "needs_translation": "false"}, "lv_bec_voltage": {"english": "LV BEC Voltage", "translation": "Voltaje LV BEC", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Tiempo Auto Reinicio", "needs_translation": "false"}, "acceleration": {"english": "Acceleration", "translation": "Aceleración", "needs_translation": "false"}, "motor_direction": {"english": "Motor Direction", "translation": "Dirección Motor", "needs_translation": "false"}, "smart_fan": {"english": "Smart Fan", "translation": "Ventilador Inteligente", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "led_color": {"english": "LED Color", "translation": "Color LED", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Básico", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Potencia de Inicio", "needs_translation": "false"}, "motor_poles": {"english": "Motor Poles", "translation": "Polos del Motor", "needs_translation": "false"}, "capacity_correction": {"english": "Capacity Correction", "translation": "Corrección Capacidad", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Timing", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}, "flrtr": {"gov": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "motor_temp_sensor": {"english": "Motor temp sensor", "translation": "Sensor temp. motor", "needs_translation": "false"}, "starting_torque": {"english": "Starting torque", "translation": "Torque de Inicio", "needs_translation": "false"}, "cell_count": {"english": "Cell count", "translation": "Nro. <PERSON>", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}, "motor_erpm_max": {"english": "Motor ERPM max", "translation": "Max ERPM Motor", "needs_translation": "false"}, "name": {"english": "FLYROTOR", "translation": "FLYROTOR", "needs_translation": "false"}, "low_voltage_protection": {"english": "Low voltage protection", "translation": "Proteccion bajo voltaje", "needs_translation": "false"}, "gov_d": {"english": "Gov-D", "translation": "Gov-D", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry protocol", "translation": "Protocolo de telemetria", "needs_translation": "false"}, "motor_direction": {"english": "Motor direction", "translation": "Direccion del Motor", "needs_translation": "false"}, "throttle_protocol": {"english": "Throttle protocol", "translation": "Protocolo de acelerador", "needs_translation": "false"}, "soft_start": {"english": "Soft start", "translation": "Arranque Su<PERSON>", "needs_translation": "false"}, "other": {"english": "Other", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "temperature_protection": {"english": "Temperature protection", "translation": "Proteccion Temperatura", "needs_translation": "false"}, "buzzer_volume": {"english": "Buzzer volume", "translation": "Volumen del Buzzer", "needs_translation": "false"}, "timing_angle": {"english": "Timing angle", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "bec_voltage": {"english": "BEC voltage", "translation": "Voltaje BEC", "needs_translation": "false"}, "fan_control": {"english": "Fan control", "translation": "Control de Ventilador", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basico", "needs_translation": "false"}, "current_gain": {"english": "Current gain", "translation": "Ganancia Corriente", "needs_translation": "false"}, "led_color": {"english": "LED color", "translation": "Color LED", "needs_translation": "false"}, "motor_temp": {"english": "Motor temperture", "translation": "Temperatura del motor", "needs_translation": "false"}, "response_speed": {"english": "Response speed", "translation": "Velocidad de Respuesta", "needs_translation": "false"}, "battery_capacity": {"english": "Battery capacity", "translation": "Capacidad de la bateria", "needs_translation": "false"}}, "scorp": {"esc_mode": {"english": "ESC Mode", "translation": "Modo ESC", "needs_translation": "false"}, "min_voltage": {"english": "Min Voltage", "translation": "Vol<PERSON><PERSON>", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Rotación", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry Protocol", "translation": "Protocolo Telemetría", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "runup_time": {"english": "Runup Time", "translation": "Tiempo Previo", "needs_translation": "false"}, "motor_startup_sound": {"english": "Motor Startup Sound", "translation": "Sonido Inicio Motor", "needs_translation": "false"}, "gov_integral": {"english": "Gov <PERSON><PERSON>", "translation": "Gov <PERSON><PERSON>", "needs_translation": "false"}, "gov_proportional": {"english": "Gov Proportional", "translation": "Gov Proporcional", "needs_translation": "false"}, "cutoff_handling": {"english": "Cutoff Handling", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "bailout": {"english": "Bailout", "translation": "Rescate", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Límites", "needs_translation": "false"}, "soft_start_time": {"english": "Soft Start Time", "translation": "Tiempo Inicio Suave", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "Voltaje BEC", "needs_translation": "false"}, "extra_msg_save": {"english": "Please reboot the ESC to apply the changes", "translation": "Renicie el ESC para aplicar los cambios", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Básico", "needs_translation": "false"}, "max_current": {"english": "Max <PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "max_temperature": {"english": "Max Temperature", "translation": "Temperatura Máx", "needs_translation": "false"}, "protection_delay": {"english": "Protection Delay", "translation": "Retardo Protección", "needs_translation": "false"}, "max_used": {"english": "Max Used", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}}, "yge": {"esc_mode": {"english": "ESC Mode", "translation": "Modo ESC", "needs_translation": "false"}, "esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "current_limit": {"english": "Current Limit", "translation": "Límite de Corriente", "needs_translation": "false"}, "f3c_auto": {"english": "F3C Autorotation", "translation": "Autorrotación F3C", "needs_translation": "false"}, "name": {"english": "YGE", "translation": "YGE", "needs_translation": "false"}, "max_start_power": {"english": "Max Start Power", "translation": "Potencia Máx Inicio", "needs_translation": "false"}, "lv_bec_voltage": {"english": "BEC", "translation": "BEC", "needs_translation": "false"}, "pinion_teeth": {"english": "Pinion Teeth", "translation": "Dientes Piñón", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Tiempo Auto Reinicio", "needs_translation": "false"}, "main_teeth": {"english": "Main Teeth", "translation": "Dientes Corona", "needs_translation": "false"}, "other": {"english": "Other", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Límites", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "throttle_response": {"english": "Throttle Response", "translation": "Respuesta Ace<PERSON>", "needs_translation": "false"}, "stick_zero_us": {"english": "<PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "motor_pole_pairs": {"english": "Motor Pole Pairs", "translation": "Pares Polos Motor", "needs_translation": "false"}, "stick_range_us": {"english": "Stick Range", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Básico", "needs_translation": "false"}, "min_start_power": {"english": "Min Start Power", "translation": "Potencia Mín Inicio", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Corona Liberada", "needs_translation": "false"}, "direction": {"english": "Direction", "translation": "Dirección", "needs_translation": "false"}, "timing": {"english": "Motor Timing", "translation": "Timing <PERSON>", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}}, "searching": {"english": "Searching", "translation": "Buscando", "needs_translation": "false"}}, "pids": {"help_p1": {"english": "FeedForward (Roll/Pitch): Start at 70, increase until stops are sharp with no drift. Keep roll and pitch equal.", "translation": "Ganancia F (Alabeo/Cabeceo): Comience en 70, aumente hasta que las paradas sean en seco sin derivas. Mantenga alabeo y cabeceo en rangos similares.", "needs_translation": "false"}, "o": {"english": "O", "translation": "O", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Cabeceo", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Dirección", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Alabe<PERSON>", "needs_translation": "false"}, "help_p5": {"english": "Test & Adjust: Fly, observe, and fine-tune for best performance in real conditions.", "translation": "Prueba & Ajuste: <PERSON><PERSON><PERSON>, observe, y afine hasta la mejor performance en condiciones reales.", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "PIDs", "translation": "PIDs", "needs_translation": "false"}, "help_p2": {"english": "<PERSON> Gain (Roll/Pitch): Raise gradually for stable piro pitch pumps. Too high causes wobbles; match roll/pitch values.", "translation": "Ganancia I (Alabeo/Cabeceo): Aumente gradualmente para bombeo de cabeceo estable en piruetas. Demasiado alto causa tambaleo; mantega alabeo/cabeceo en rangos similares.", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "b": {"english": "B", "translation": "B", "needs_translation": "false"}, "help_p4": {"english": "Tail Stop Gain (CW/CCW): Adjust separately for clean, bounce-free stops in both directions.", "translation": "Ganancia Detención Cola (Horaria CW/Antihoraria CCW): Ajuste separadamente para detenciones limpias, sin rebotes, en ambos sentidos.", "needs_translation": "false"}, "help_p3": {"english": "Tail P/I/D Gains: Increase P until slight wobble in funnels, then back off slightly. Raise I until tail holds firm in hard moves (too high causes slow wag). Adjust D for smooth stops—higher for slow servos, lower for fast ones.", "translation": "Ganacia P/I/D de cola: Incremente P hasta un leve tambaleo durante embudos, luego disminuya levemente. Aumente I hasta que la cola se mantenga firme durante movimientos rápidos (demasiado alto causa un meneo lento de la cola). Ajuste D para paradas suaves — más alto para servos lentos, más bajo para rápidos.", "needs_translation": "false"}}, "msp_speed": {"seconds_600": {"english": "  600S  ", "translation": "  600S  ", "needs_translation": "false"}, "avg_query_time": {"english": "Average query time", "translation": "Tiempo Promedio Consulta", "needs_translation": "false"}, "seconds_30": {"english": "  30S  ", "translation": "  30S  ", "needs_translation": "false"}, "name": {"english": "MSP Speed", "translation": "Velocidad MSP", "needs_translation": "false"}, "max_query_time": {"english": "Maximum query time", "translation": "Tiempo Máx Consulta", "needs_translation": "false"}, "help_p1": {"english": "This tool attempts to determine the quality of your MSP data link by performing as many large MSP queries within 30 seconds as possible.", "translation": "Esta herramienta intenta determinar la calidad de la conexión de datos MSP llevando a cabo tantas consultas MSP grandes como sea posible en 30 segundos.", "needs_translation": "false"}, "retries": {"english": "Retries", "translation": "Reintentos", "needs_translation": "false"}, "checksum_errors": {"english": "Checksum errors", "translation": "Errores de checksum", "needs_translation": "false"}, "test_length": {"english": "Test length", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "start": {"english": "Start", "translation": "<PERSON><PERSON>o", "needs_translation": "false"}, "memory_free": {"english": "Memory free", "translation": "Memoria libre", "needs_translation": "false"}, "start_prompt": {"english": "Would you like to start the test? Choose the test run time below.", "translation": "Desea comenzar la prueba? A continuacion seleccione el tiempo de ejecución.", "needs_translation": "false"}, "rf_protocol": {"english": "RF protocol", "translation": "Protocolo RF", "needs_translation": "false"}, "min_query_time": {"english": "Minimum query time", "translation": "Tiempo M<PERSON>", "needs_translation": "false"}, "seconds_120": {"english": "  120S  ", "translation": "  120S  ", "needs_translation": "false"}, "seconds_300": {"english": "  300S  ", "translation": "  300S  ", "needs_translation": "false"}, "testing": {"english": "Testing", "translation": "Probando", "needs_translation": "false"}, "successful_queries": {"english": "Successful queries", "translation": "Consultas exitosas", "needs_translation": "false"}, "timeouts": {"english": "Timeouts", "translation": "<PERSON>ie<PERSON>", "needs_translation": "false"}, "testing_performance": {"english": "Testing MSP performance...", "translation": "Probando performance MSP...", "needs_translation": "false"}, "total_queries": {"english": "Total queries", "translation": "Total de consultas", "needs_translation": "false"}}, "copyprofiles": {"profile_type": {"english": "Profile Type", "translation": "Tipo de Perfil", "needs_translation": "false"}, "profile_type_pid": {"english": "PID", "translation": "PID", "needs_translation": "false"}, "profile_type_rate": {"english": "Rate", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "msgbox_save": {"english": "Save settings", "translation": "Guardar Configuración", "needs_translation": "false"}, "name": {"english": "Copy Profiles", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Copy PID profile or Rate profile from Source to Destination.", "translation": "Copiar perfil PID o perfil de tasas de Fuente a Destino.", "needs_translation": "false"}, "dest_profile": {"english": "Dest. Profile", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "source_profile": {"english": "Source Profile", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "msgbox_msg": {"english": "Save current page to flight controller?", "translation": "Guarda esta página en el controlador de vuelo?", "needs_translation": "false"}, "help_p2": {"english": "Choose the source and destinations and save to copy the profile.", "translation": "Seleccione fuente y destino y oprima Guardar para copiar el perfil.", "needs_translation": "false"}}, "esc_motors": {"min_throttle": {"english": "0% Throttle PWM Value", "translation": "Valor PWM Acelerador 0%", "needs_translation": "false"}, "tail_motor_ratio": {"english": "Tail Motor Ratio", "translation": "Relación Motor ", "needs_translation": "false"}, "max_throttle": {"english": "100% Throttle PWM value", "translation": "Valor PWM Acelerador 100%", "needs_translation": "false"}, "main_motor_ratio": {"english": "Main Motor Ratio", "translation": "Relación Motor ", "needs_translation": "false"}, "pinion": {"english": "Pinion", "translation": "Piñón", "needs_translation": "false"}, "main": {"english": "Main", "translation": "Principal", "needs_translation": "false"}, "help_p1": {"english": "Configure the motor and speed controller features.", "translation": "Configure características del motor y ESC.", "needs_translation": "false"}, "rear": {"english": "Rear", "translation": "de Cola", "needs_translation": "false"}, "front": {"english": "Front", "translation": "Frontal", "needs_translation": "false"}, "voltage_correction": {"english": "Voltage Correction", "translation": "Corrección de Voltaje", "needs_translation": "false"}, "mincommand": {"english": "Motor Stop PWM Value", "translation": "Valor PWM Motor Detenido", "needs_translation": "false"}, "name": {"english": "ESC/Motors", "translation": "ESC/Motores", "needs_translation": "false"}, "motor_pole_count": {"english": "Motor Pole Count", "translation": "Nro. de Polos del Motor", "needs_translation": "false"}, "current_correction": {"english": "Current Correction", "translation": "Corrección de Corriente", "needs_translation": "false"}, "consumption_correction": {"english": "Consumption Correction", "translation": "Corrección de Consumo", "needs_translation": "false"}}, "radio_config": {"deflection": {"english": "Deflection", "translation": "Deflección", "needs_translation": "false"}, "max_throttle": {"english": "Max", "translation": "Máx", "needs_translation": "false"}, "stick": {"english": "Stick", "translation": "Joystick", "needs_translation": "false"}, "arming": {"english": "Arming", "translation": "Armado", "needs_translation": "false"}, "yaw_deadband": {"english": "Yaw", "translation": "Dirección", "needs_translation": "false"}, "cyclic": {"english": "Cyclic", "translation": "Cíclico", "needs_translation": "false"}, "name": {"english": "Radio Config", "translation": "Config Radio", "needs_translation": "false"}, "help_p1": {"english": "Configure your radio settings. Stick center, arm, throttle hold, and throttle cut.", "translation": "Configure su radio. Centro del joystick, armado, retención de acelerador, y corte de acelerador.", "needs_translation": "false"}, "min_throttle": {"english": "Min", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "throttle": {"english": "<PERSON>hrottle", "translation": "Acelerador", "needs_translation": "false"}, "deadband": {"english": "Deadband", "translation": "Zona muerta", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Centro", "needs_translation": "false"}}, "profile_select": {"help_p1": {"english": "Set the current flight profile or rate profile you would like to use.", "translation": "Indique el perfil de vuelo o perfil de tasa que desee utilizar.", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "pid_profile": {"english": "PID profile", "translation": "Perfil PID", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "Guarda esta página en el controlador de vuelo?", "needs_translation": "false"}, "save_prompt_local": {"english": "Save current page to radio?", "translation": "Guardar página actual?", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "CANCELAR", "needs_translation": "false"}, "name": {"english": "Select Profile", "translation": "Selección Perfil", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "Guardar config", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "If you use a switch on your radio to change flight or rate modes, this will override this choice as soon as you toggle the switch.", "translation": "Si utiliza un botón en su radio para cambiar los modos de vuelo o tasas, esta función anula la elección tan pronto como cambie la posición del botón.", "needs_translation": "false"}}, "profile_governor": {"tail_torque_assist": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Dirección", "needs_translation": "false"}, "cyc": {"english": "Cyc", "translation": "Cícl.", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "help_p1": {"english": "Full headspeed: Headspeed target when at 100% throttle input.", "translation": "Velocidad Rotor Máx: Velocidad deseada del rotor con el acelerador al 100%.", "needs_translation": "false"}, "help_p6": {"english": "Tail Torque Assist: For motorized tails. Gain and limit of headspeed increase when using main rotor torque for yaw assist.", "translation": "Asistencia de Torque de Cola: Para colas motorizadas. Ganancia y límite de aceleracion del rotor cuando se utiliza el torque del rotor principal para asistir al giro sobre el eje vertical.", "needs_translation": "false"}, "help_p4": {"english": "Precomp: Governor precomp gain for yaw, cyclic, and collective inputs.", "translation": "Precomp: Precompensación del Governor para comandos de cíclico, colectivo, y dirección (cola).", "needs_translation": "false"}, "max_throttle": {"english": "<PERSON> throttle", "translation": "Aceler<PERSON>", "needs_translation": "false"}, "full_headspeed": {"english": "Full headspeed", "translation": "Velocidad <PERSON>", "needs_translation": "false"}, "precomp": {"english": "Precomp", "translation": "Precomp", "needs_translation": "false"}, "gain": {"english": "PID master gain", "translation": "Ganacia PID maestra", "needs_translation": "false"}, "disabled_message": {"english": "Rotorflight governor is not enabled", "translation": "Governor <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> deshabilitado", "needs_translation": "false"}, "help_p3": {"english": "Gains: Fine tuning of the governor.", "translation": "Ganancias: <PERSON><PERSON><PERSON> fino del governor.", "needs_translation": "false"}, "col": {"english": "Col", "translation": "Colec.", "needs_translation": "false"}, "min_throttle": {"english": "<PERSON> throttle", "translation": "Aceler<PERSON>", "needs_translation": "false"}, "tta_limit": {"english": "Limit", "translation": "Limite", "needs_translation": "false"}, "help_p2": {"english": "PID master gain: How hard the governor works to hold the RPM.", "translation": "Ganacia PID maestra: Que tanto trabaja el governor para sostener las RPM.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "Ganancias", "needs_translation": "false"}, "help_p5": {"english": "Max throttle: The maximum throttle % the governor is allowed to use.", "translation": "Acelerador Máx: El porcentaje máximo de acelerador que se le permite utilizar al governor.", "needs_translation": "false"}, "tta_gain": {"english": "<PERSON><PERSON>", "translation": "Ganancia", "needs_translation": "false"}}, "profile_tailrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "Ganancia", "needs_translation": "false"}, "help_p4": {"english": "Collective FF Gain: Tail precompensation for collective inputs.", "translation": "Ganancia FF Colectivo: Precompensación de cola para comandos de colectivo.", "needs_translation": "false"}, "collective_impulse_ff": {"english": "Collective Impulse FF", "translation": "Impulso Colectivo FF", "needs_translation": "false"}, "help_p2": {"english": "Precomp Cutoff: Frequency limit for all yaw precompensation actions.", "translation": "Corte Precomp.: Límite de frecuencia para todas las acciones de precompensación de dirección.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Corte", "needs_translation": "false"}, "help_p3": {"english": "Cyclic FF Gain: Tail precompensation for cyclic inputs.", "translation": "Ganancia FF Cíclico: Precompensación de cola para comandos de cíclico.", "needs_translation": "false"}, "help_p1": {"english": "Yaw Stop Gain: Higher stop gain will make the tail stop more aggressively but may cause oscillations if too high. Adjust CW or CCW to make the yaw stops even.", "translation": "Ganancia de parada de dirección: Un valor más alto hará que la cola se detenga más agresivamente, pero puede causar oscilaciones si el valor es demasiado alto. Ajuste horario (CW) y antihorario (CCW) para hacer que las paradas de dirección sean parejas para ambos lados.", "needs_translation": "false"}, "inertia_precomp": {"english": "Inertia Precomp", "translation": "Precomp. Inercia", "needs_translation": "false"}, "cyclic_ff_gain": {"english": "Cyclic FF gain", "translation": "Ganancia FF Cíclico", "needs_translation": "false"}, "help_p5": {"english": "Collective Impulse FF: Impulse tail precompensation for collective inputs. If you need extra tail precompensation at the beginning of collective input.", "translation": "Impulso Colectivo FF: Precompensación de impulso de cola para comandos de colectivo. Si se necesita precompensación de la cola al principio de un comando de colectivo.", "needs_translation": "false"}, "cw": {"english": "CW", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "ccw": {"english": "CCW", "translation": "AntiH.", "needs_translation": "false"}, "yaw_stop_gain": {"english": "Yaw stop gain", "translation": "<PERSON><PERSON><PERSON> parada dir.", "needs_translation": "false"}, "precomp_cutoff": {"english": "Precomp Cutoff", "translation": "Corte de Precomp.", "needs_translation": "false"}, "collective_ff_gain": {"english": "Collective FF gain", "translation": "Ganancia FF Colectivo", "needs_translation": "false"}, "name": {"english": "Tail Rotor", "translation": "Rotor de Cola", "needs_translation": "false"}, "decay": {"english": "Decay", "translation": "Distensión", "needs_translation": "false"}}, "profile_pidcontroller": {"help_p4": {"english": "Error rotation: Allow errors to be shared between all axes.", "translation": "Error rotación: Permitir que se compartan los errores entre todos los ejes.", "needs_translation": "false"}, "ground_error_decay": {"english": "Ground Error Decay", "translation": "Distensión Error <PERSON>", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "inflight_error_decay": {"english": "Inflight Error Decay", "translation": "Distens. Err en Vuelo", "needs_translation": "false"}, "help_p2": {"english": "Error limit: Angle limit for I-term.", "translation": "Límite Error: <PERSON>ulo límite para I-term.", "needs_translation": "false"}, "error_limit": {"english": "Error limit", "translation": "Límite Error", "needs_translation": "false"}, "help_p3": {"english": "Offset limit: Angle limit for High Speed Integral (O-term).", "translation": "Límite de compensación: Angulo límite para Integral de Alta Velocidad (HSI O-term).", "needs_translation": "false"}, "cutoff_point": {"english": "Cut-off point", "translation": "Punto de Corte", "needs_translation": "false"}, "limit": {"english": "Limit", "translation": "Límite", "needs_translation": "false"}, "iterm_relax": {"english": "I-term relax", "translation": "Distensión I-term", "needs_translation": "false"}, "hsi_offset_limit": {"english": "HSI Offset limit", "translation": "Límite Compensación HSI", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "name": {"english": "PID Controller", "translation": "Controlador PID", "needs_translation": "false"}, "error_rotation": {"english": "Error rotation", "translation": "Error rotación", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}, "help_p5": {"english": "I-term relax: Limit accumulation of I-term during fast movements - helps reduce bounce back after fast stick movements. Generally needs to be lower for large helis and can be higher for small helis. Best to only reduce as much as is needed for your flying style.", "translation": "Distensión I-term: <PERSON><PERSON> la acumulación de I-term durante movimientos rápidos - ayuda a reducir el rebote luego de movimientos rápidos del joystick. En general debe ser menor para helis grandes y puede ser mayor en helis pequeños. Lo mejor es reducir tanto como sea necesario para su estilo de vuelo.", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Tiempo", "needs_translation": "false"}, "help_p1": {"english": "Error decay ground: PID decay to help prevent heli from tipping over when on the ground.", "translation": "Distensión de Error en Tierra: Distensión de PID para ayudar a prevenir que el heli se vuelque estando en tierra.", "needs_translation": "false"}}, "logs": {"help_logs_p2": {"english": "Note. To enable logging it is essential for you to have the following sensors enabled.", "translation": "Nota: Para activar los LOGs es escencial que estén activados los siguientes sensores..", "needs_translation": "false"}, "name": {"english": "Logs", "translation": "Logs", "needs_translation": "false"}, "help_logs_p1": {"english": "Please select a log file from the list below.", "translation": "Seleccione un archivo de la lista que sigue.", "needs_translation": "false"}, "msg_no_logs_found": {"english": "NO LOG FILES FOUND", "translation": "NO HAY LOGS", "needs_translation": "false"}, "help_logs_tool_p1": {"english": "Please use the slider to navigate the graph.", "translation": "Utilice el control deslizante para navegar en el gráfico.", "needs_translation": "false"}, "help_logs_p3": {"english": "- arm status, voltage, headspeed, current, esc temperature", "translation": "- estado de armado, voltaje, velocidad del rotor, corriente, temperatura del ESC", "needs_translation": "false"}}, "battery": {"calcfuel_local": {"english": "Calculate Fuel Using", "translation": "Calculate fuel using", "needs_translation": "true"}, "max_cell_voltage": {"english": "Max Cell Voltage", "translation": "Voltaje <PERSON>", "needs_translation": "false"}, "full_cell_voltage": {"english": "Full Cell Voltage", "translation": "Voltaje de Celda llena", "needs_translation": "false"}, "name": {"english": "Battery", "translation": "Bater<PERSON>", "needs_translation": "false"}, "min_cell_voltage": {"english": "Min Cell Voltage", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "The battery settings are used to configure the flight controller to monitor the battery voltage and provide warnings when the voltage drops below a certain level.", "translation": "La configuración de celdas se utiliza para que el controlador de vuelo pueda monitorear el voltaje de la batería y generar advertencias cuando esté por debajo de cierto nivel.", "needs_translation": "false"}, "battery_capacity": {"english": "Battery Capacity", "translation": "Capacidad Batería", "needs_translation": "false"}, "warn_cell_voltage": {"english": "Warn Cell Voltage", "translation": "Advertencia Voltaje de Celda", "needs_translation": "false"}, "cell_count": {"english": "Cell Count", "translation": "Número <PERSON>", "needs_translation": "false"}, "consumption_warning_percentage": {"english": "Consumption Warning %", "translation": "Advertencia Consumo %", "needs_translation": "false"}, "timer": {"english": "Flight Time Alarm", "translation": "Alarma Tiempo Vuelo", "needs_translation": "false"}, "voltage_multiplier": {"english": "Sag Compensation", "translation": "Compensación de caída", "needs_translation": "false"}, "kalman_multiplier": {"english": "Filter Compensation", "translation": "Filter compensation", "needs_translation": "true"}, "alert_type": {"english": "Rx Voltage Alert", "translation": "BEC or Rx Batt Voltage Alert", "needs_translation": "true"}, "bec_voltage_alert": {"english": "BEC Alert Value", "translation": "BEC Alert Value", "needs_translation": "true"}, "rx_voltage_alert": {"english": "RxBatt Alert Value", "translation": "RX Batt Alert Value", "needs_translation": "true"}}, "profile_mainrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "Ganancia", "needs_translation": "false"}, "help_p4": {"english": "Cross Coupling Freq. Limit: Frequency limit for the compensation, higher value will make the compensation action faster.", "translation": "Límite de Frecuencia de Acople Cruzado: Límite de Frecuencia para la compensación; un valor mayor aumentará la velocidad de la compensación.", "needs_translation": "false"}, "collective_pitch_comp_short": {"english": "Col. Pitch Compensation", "translation": "Compens. Ang. Colectivo.", "needs_translation": "false"}, "cyclic_cross_coupling": {"english": "Cyclic Cross coupling", "translation": "Acople inter-Ciclico", "needs_translation": "false"}, "collective_pitch_comp": {"english": "Collective Pitch Compensation", "translation": "Compensación de Angulo de Colectivo", "needs_translation": "false"}, "name": {"english": "Main Rotor", "translation": "R<PERSON>r Principal", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Corte", "needs_translation": "false"}, "ratio": {"english": "<PERSON><PERSON>", "translation": "Relación", "needs_translation": "false"}, "help_p1": {"english": "Collective Pitch Compensation: Increasing will compensate for the pitching motion caused by tail drag when climbing.", "translation": "Compensación de Angulo de Colectivo: Incrementar este valor compensa el movimiento de cabeceo causado por el arrastre de la cola durante la trepada.", "needs_translation": "false"}, "help_p2": {"english": "Cross Coupling Gain: Removes roll coupling when only elevator is applied.", "translation": "Ganancia de Acople Cruzado: Cancela el acople de alabeo cuando se aplica solo elevador.", "needs_translation": "false"}, "help_p3": {"english": "Cross Coupling Ratio: Amount of compensation (pitch vs roll) to apply.", "translation": "Relación de Acople Cruzado: Cantidad de compensación (cabeceo vs alabeo) a aplicar.", "needs_translation": "false"}}, "sbusout": {"title": {"english": "SBUS Output", "translation": "Salida SBUS", "needs_translation": "false"}, "help_fields_source": {"english": "Source id for the mix, counting from 0-15.", "translation": "ID Fuente para la mezcla , contando desde 0-15.", "needs_translation": "false"}, "help_default_p4": {"english": "- For motors, use 0, 1000.", "translation": "- Para motores, use 0, 1000.", "needs_translation": "false"}, "ch_prefix": {"english": "CH", "translation": "CH", "needs_translation": "false"}, "channel_prefix": {"english": "CHANNEL ", "translation": "CANAL ", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "Guardando", "needs_translation": "false"}, "name": {"english": "SBUS Out", "translation": "Salida SBUS", "needs_translation": "false"}, "channel_page": {"english": "Sbus out / CH", "translation": "Salida Sbus/CH", "needs_translation": "false"}, "receiver": {"english": "Receiver", "translation": "Receptor", "needs_translation": "false"}, "servo": {"english": "Servo", "translation": "Servo", "needs_translation": "false"}, "type": {"english": "Type", "translation": "Tipo", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "Guardando da<PERSON>...", "needs_translation": "false"}, "help_fields_max": {"english": "The maximum pwm value to send", "translation": "El valor máximo PWM a enviar", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "Motor", "needs_translation": "false"}, "help_default_p5": {"english": "- Or you can customize your own mapping.", "translation": "- O bien puede personalizar su propio mapeo.", "needs_translation": "false"}, "help_default_p1": {"english": "Configure advanced mixing and channel mapping if you have SBUS Out enabled on a serial port.", "translation": "Configure las mezclas avanzadas y el mapeo de canales si utiliza la salida SBUS en un puerto serial.", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Máx", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "Guarda esta página en el controlador de vuelo?", "needs_translation": "false"}, "help_fields_min": {"english": "The minimum pwm value to send.", "translation": "El valor mínimo <PERSON> a enviar.", "needs_translation": "false"}, "mixer": {"english": "Mixer", "translation": "Me<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "CANCELAR", "needs_translation": "false"}, "help_default_p2": {"english": "- For RX channels or servos (wideband), use 1000, 2000 or 500,1000 for narrow band servos.", "translation": "- Para canales RX o servos comunes use 1000, 2000; para servos de banda angosta use 500, 1000.", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "Guardar Config.", "needs_translation": "false"}, "min": {"english": "Min", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_default_p3": {"english": "- For mixer rules, use -1000, 1000.", "translation": "- Para reglas de mezclador, use -1000, 1000.", "needs_translation": "false"}, "source": {"english": "Source", "translation": "Fuente", "needs_translation": "false"}}, "profile_rescue": {"help_p4": {"english": "Hover: How much collective to maintain a steady hover.", "translation": "Sobrevuelo: Cuánto colectivo se necesita para mantener el heli sobrevolando el lugar.", "needs_translation": "false"}, "hover": {"english": "Hover", "translation": "So<PERSON>vuelo", "needs_translation": "false"}, "collective": {"english": "Collective", "translation": "Colectivo", "needs_translation": "false"}, "help_p2": {"english": "Pull-up: How much collective and for how long to arrest the fall.", "translation": "Levantado: Cantidad de colectivo y tiempo que se aplica para detener la caída.", "needs_translation": "false"}, "climb": {"english": "Climb", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "mode_enable": {"english": "Rescue mode enable", "translation": "Habilitar Modo <PERSON>", "needs_translation": "false"}, "help_p3": {"english": "Climb: How much collective to maintain a steady climb - and how long.", "translation": "Trepada: Cantidad y tiempo de colectivo para mantener una trepada constante.", "needs_translation": "false"}, "help_p1": {"english": "Flip to upright: Flip the heli upright when rescue is activated.", "translation": "Invertir a Vertical: Dar vuelta el heli a posición vertical cuando se activa la función rescate.", "needs_translation": "false"}, "flip_upright": {"english": "Flip to upright", "translation": "Invertir a Vertical", "needs_translation": "false"}, "flip": {"english": "Flip", "translation": "Invers.", "needs_translation": "false"}, "level_gain": {"english": "Level", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "Rescue", "translation": "Rescate", "needs_translation": "false"}, "exit_time": {"english": "Exit time", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p5": {"english": "Flip: How long to wait before aborting because the flip did not work.", "translation": "Inversión: <PERSON><PERSON>án<PERSON> tiempo esperar antes de abortar una inversión que no funcionó.", "needs_translation": "false"}, "help_p6": {"english": "Gains: How hard to fight to keep heli level when engaging rescue mode.", "translation": "Ganancias: Que tan duro debe luchar para mantener el helicóptero horizontal en modo rescate.", "needs_translation": "false"}, "fail_time": {"english": "Fail time", "translation": "Tiempo Falla", "needs_translation": "false"}, "pull_up": {"english": "Pull-up", "translation": "Levantado", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p7": {"english": "Rate and Accel: Max rotation and acceleration rates when leveling during rescue.", "translation": "Tasa y Acel.: Máxima tasa de rotación y aceleración durante el nivelado de rescate.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "Ganancias", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Tiempo", "needs_translation": "false"}, "accel": {"english": "Accel", "translation": "Acel.", "needs_translation": "false"}}, "trim": {"disable_mixer_message": {"english": "Return control of the servos to the flight controller.", "translation": "Devolver el control de los servos al controlador de vuelo.", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Motor idle  %", "translation": "% Motor cola ralentí", "needs_translation": "false"}, "disable_mixer_override": {"english": "Disable mixer override", "translation": "No permitir toma de mezclador", "needs_translation": "false"}, "yaw_trim": {"english": "Yaw. trim %", "translation": "% ajuste Direccion", "needs_translation": "false"}, "enable_mixer_message": {"english": "Set all servos to their configured center position. \r\n\r\nThis will result in all values on this page being saved when adjusting the servo trim.", "translation": "Ponga todos los servos en su posición central configurada.\r\n\r\nEsto permitirá que se guarden todos los valores de ajuste de los servos.", "needs_translation": "false"}, "mixer_override_disabling": {"english": "Disabling mixer override...", "translation": "Desactivando toma de mezclador...", "needs_translation": "false"}, "roll_trim": {"english": "Roll trim %", "translation": "% ajuste Alabeo", "needs_translation": "false"}, "pitch_trim": {"english": "Pitch trim %", "translation": "% ajuste Cabeceo", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Motorised tail: If using a motorised tail, use this to set the minimum idle speed and zero yaw.", "translation": "Cola motorizada: Si tiene una cola motorizada, utilice este parámetro para configurar la velocidad mínima de ralentí a la que el heli no gira.", "needs_translation": "false"}, "mixer_override": {"english": "Mixer Override", "translation": "<PERSON><PERSON> Mezclador", "needs_translation": "false"}, "mixer_override_enabling": {"english": "Enabling mixer override...", "translation": "Activando toma de mezclador...", "needs_translation": "false"}, "enable_mixer_override": {"english": "Enable mixer override", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "collective_trim": {"english": "Col. trim %", "translation": "% ajuste Colectivo", "needs_translation": "false"}, "help_p1": {"english": "Link trims: Use to trim out small leveling issues in your swash plate. Typically only used if the swash links are non-adjustable.", "translation": "Ajuste de enlaces: Se utiliza para corregir pequeños problemas de nivelación en el swashplate. Normalmente se utiliza cuando los brazos del swashplate no son ajustables.", "needs_translation": "false"}}, "governor": {"help_p1": {"english": "These parameters apply globally to the governor regardless of the profile in use.", "translation": "Estos parámetros se aplican globalmente al governor independientemente del perfil en uso.", "needs_translation": "false"}, "handover_throttle": {"english": "Handover throttle%", "translation": "% Acelerador Transferencia", "needs_translation": "false"}, "spoolup_min_throttle": {"english": "Spoolup min throttle%", "translation": "% Acelerador <PERSON>", "needs_translation": "false"}, "recovery_time": {"english": "Recovery time", "translation": "Tiempo de Recuperación", "needs_translation": "false"}, "mode": {"english": "Mode", "translation": "Modo", "needs_translation": "false"}, "help_p2": {"english": "Each parameter is simply a time value in seconds for each governor action.", "translation": "Todos los parámetros son tiempos en segundos para cada acción del governor.", "needs_translation": "false"}, "tracking_time": {"english": "Tracking time", "translation": "Tiempo de Tracking", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "startup_time": {"english": "Startup time", "translation": "Tiempo de Inicio", "needs_translation": "false"}, "spoolup_time": {"english": "Spoolup time", "translation": "Tiempo de Arranque", "needs_translation": "false"}}, "accelerometer": {"help_p1": {"english": "The accelerometer is used to measure the angle of the flight controller in relation to the horizon. This data is used to stabilize the aircraft and provide self-leveling functionality.", "translation": "El acelerómetro se utiliza para medir el ángulo del controlador de vuelo respecto del horizonte. Los datos obtenidos son utilizados para estabilizar la aeronave y porveer funcionalidad de nivelado automático.", "needs_translation": "false"}, "name": {"english": "Accelerometer", "translation": "Acelerómetro", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Cabeceo", "needs_translation": "false"}, "msg_calibrate": {"english": "Calibrate the accelerometer?", "translation": "Calibrar el acelerómetro?", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Alabe<PERSON>", "needs_translation": "false"}}, "rates": {"help_table_5_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Tasa <PERSON>.: Tasa de rotación máxima durante el desplazamiento máximo del joystick en grados por segundo.", "needs_translation": "false"}, "actual": {"english": "ACTUAL", "translation": "ACTUAL", "needs_translation": "false"}, "max_rate": {"english": "Max Rate", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_table_4_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Reduce la sensibilidad cerca del centro del joystick donde hacen falta controles finos.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_table_5_p1": {"english": "RC Rate: Use to reduce sensitivity around center stick. RC Rate set to one half of the Max Rate is linear. A lower number will reduce sensitivity around center stick. Higher than one half of the Max Rate will also increase the Max Rate.", "translation": "Tasa RC: Se usa para reducir le sensibilidad cerca del centro del joystick. Configurar la Tasa RC a la mitad de la Tasa Máx da por resultado una función lineal. Un valor menor reduce la sesibilidad cerca del centro del joystick y un valor mayor incrementará la Tasa Máx.", "needs_translation": "false"}, "help_table_4_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Tasa M<PERSON>: Tasa máxima de rotación durante el desplazamiento máximo del joystick en grados por segundo.", "needs_translation": "false"}, "center_sensitivity": {"english": "Cntr. Sens.", "translation": "Sens.Cntro.", "needs_translation": "false"}, "rc_curve": {"english": "RC Curve", "translation": "Curva RC", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Alabe<PERSON>", "needs_translation": "false"}, "none": {"english": "NONE", "translation": "NINGUNA", "needs_translation": "false"}, "betaflight": {"english": "BETAFLIGHT", "translation": "BETAFLIGHT", "needs_translation": "false"}, "kiss": {"english": "KISS", "translation": "KISS", "needs_translation": "false"}, "help_table_1_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Reduce la sensibilidad cerca del centro del joystick donde hacen falta controles finos.", "needs_translation": "false"}, "help_table_3_p2": {"english": "Rate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "Tasa: Incrementa la tasa de rotación máxima al tiempo que reduce la sensibilidad cerca del desplazamiento medio del joystick.", "needs_translation": "false"}, "help_table_2_p2": {"english": "Acro+: Increases the maximum rotation rate while reducing sensitivity around half stick.", "translation": "Acro+: Incrementa la tasa de rotación al tiempo que reduce la sensibilidad cerca del desplazamiento medio del joystick.", "needs_translation": "false"}, "superrate": {"english": "SuperRate", "translation": "SuperTasa", "needs_translation": "false"}, "help_table_2_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Reduce la sensibilidad cerca del centro del joystick donde hacen falta controles finos.", "needs_translation": "false"}, "raceflight": {"english": "RACEFLIGHT", "translation": "RACEFLIGHT", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Dirección", "needs_translation": "false"}, "collective": {"english": "Col", "translation": "Colectivo", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "Tasa<PERSON>", "needs_translation": "false"}, "help_table_5_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Reduce la sensibilidad cerca del centro del joystick donde hacen falta controles finos.", "needs_translation": "false"}, "help_table_3_p3": {"english": "RC Curve: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Curva RC: Reduce la sensibilidad cerca del centro del joystick donde hacen falta controles finos.", "needs_translation": "false"}, "expo": {"english": "Expo", "translation": "Expo", "needs_translation": "false"}, "help_table_1_p2": {"english": "SuperRate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "SuperTasa: Aumenta la tasa máxima de rotación al tiempo que reduce la sensibilidad cerca del desplazamiento medio del joystick.", "needs_translation": "false"}, "help_default_p2": {"english": "We will use the sub keys below.", "translation": "Usaremos las teclas que siguen abajo.", "needs_translation": "false"}, "help_default_p1": {"english": "Default: We keep this to make button appear for rates.", "translation": "Por Defecto: Mantenemos esto para hacer que el botón esté disponible para tasas.", "needs_translation": "false"}, "quick": {"english": "QUICK", "translation": "QUICK", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Cabeceo", "needs_translation": "false"}, "acroplus": {"english": "Acro+", "translation": "Acro+", "needs_translation": "false"}, "help_table_1_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "Tasa RC: Tasa de rotación máxima durante el desplazamiento máximo del joystick.", "needs_translation": "false"}, "rc_rate": {"english": "RC Rate", "translation": "Tasa RC", "needs_translation": "false"}, "help_table_2_p1": {"english": "Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Tasa: Tasa de rotación máxima durante el desplazamiento máximo del joystick en grados por segundo.", "needs_translation": "false"}, "help_table_4_p1": {"english": "Center Sensitivity: Use to reduce sensitivity around center stick. Set Center Sensitivity to the same as Max Rate for a linear response. A lower number than Max Rate will reduce sensitivity around center stick. Note that higher than Max Rate will increase the Max Rate - not recommended as it causes issues in the Blackbox log.", "translation": "Sensibilidad Central: Para reducir sensibilidad cerca del centro del joystick. Configure la Sensibilidad Central igual que Tasa Máx para una respuesta lineal. Un valor menor que Tasa Máx reducirá la sensibilidad cerca del centro del joystick. Si es mayor que Tasa Máx se incrementa la Tasa Máx - No se recomienda porque causa problemas en el log de la Caja Negra.", "needs_translation": "false"}, "help_table_0_p1": {"english": "All values are set to zero because no RATE TABLE is in use.", "translation": "Todos los valores en cero porque no hay TABLA DE TASAS en uso.", "needs_translation": "false"}, "help_table_3_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "Tasa RC: Tasa de rotación máxima durante el desplazamiento máximo del joystick.", "needs_translation": "false"}}, "mixer": {"help_p1": {"english": "Adust swash plate geometry, phase angles, and limits.", "translation": "Ajuste de geometría del swashplate, ángulos de fase, y límites.", "needs_translation": "false"}, "collective_tilt_correction_pos": {"english": "Positive", "translation": "Positivo", "needs_translation": "false"}, "geo_correction": {"english": "Geo Correction", "translation": "Geo-Corrección", "needs_translation": "false"}, "swash_tta_precomp": {"english": "TTA Precomp", "translation": "Precomp TTA", "needs_translation": "false"}, "name": {"english": "Mixer", "translation": "Me<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "collective_tilt_correction_neg": {"english": "Negative", "translation": "Negativo", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Idle Thr%", "translation": "% Acel Ralentí Cola", "needs_translation": "false"}, "swash_phase": {"english": "Phase Angle", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "collective_tilt_correction": {"english": "Collective Tilt Correction", "translation": "Corrección Inclinación Colectivo", "needs_translation": "false"}, "swash_pitch_limit": {"english": "Total Pitch Limit", "translation": "Límite Total Cabeceo", "needs_translation": "false"}}, "about": {"help_p1": {"english": "This page provides some useful information that you may be asked for when requesting support.", "translation": "Esta página provee información que le puede ser requerida cuando solicite soporte técnico.", "needs_translation": "false"}, "msgbox_credits": {"english": "Credits", "translation": "C<PERSON>dit<PERSON>", "needs_translation": "false"}, "ethos_version": {"english": "Ethos Version", "translation": "Versión de Ethos", "needs_translation": "false"}, "rf_version": {"english": "Rotorflight Version", "translation": "Versión de Rotorflight", "needs_translation": "false"}, "fc_version": {"english": "FC Version", "translation": "Versión de FC", "needs_translation": "false"}, "name": {"english": "About", "translation": "Acerca de", "needs_translation": "false"}, "supported_versions": {"english": "Supported MSP Versions", "translation": "Ver. MSP Soportadas", "needs_translation": "false"}, "license": {"english": "You may copy, distribute, and modify the software as long as you track changes/dates in source files. Any modifications to or software including (via compiler) GPL-licensed code must also be made available under the GPL along with build & install instructions.", "translation": "<PERSON><PERSON><PERSON> copiar, distribuir, y modificar este software siempre y cuando se comprometa a marcar/fechar los cambios en el código fuente. Todas las modificaciones a nuestro software incluyendo código licenciado mediante GPL (via compilador) deben ser distribuidas bajo licencia GPL junto con instrucciones de compilación e instalación.", "needs_translation": "false"}, "simulation": {"english": "Simulation", "translation": "Simulación", "needs_translation": "false"}, "help_p2": {"english": "For support, please first read the help pages on www.rotorflight.org", "translation": "Para obtener soporte por favor lea primero las páginas de ayuda en www.rotorflight.org", "needs_translation": "false"}, "opener": {"english": "Rotorflight is an open source project. Contribution from other like minded people, keen to assist in making this software even better, is welcomed and encouraged. You do not have to be a hardcore programmer to help.", "translation": "Rotorflight es un proyecto de código abierto. Contribuciones de gente que piensa como nosotros, entusiasta en ayudar a mejorar este software, es bienvenida y motivada. No hace falta que que sea un programador de élite para ayudar.", "needs_translation": "false"}, "version": {"english": "Version", "translation": "Versión", "needs_translation": "false"}, "msp_version": {"english": "MSP Version", "translation": "Versión de MSP", "needs_translation": "false"}, "credits": {"english": "Notable contributors to both the Rotorflight firmware and this software are: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... and many more who have spent hours testing and providing feedback!", "translation": "Contribuyentes notables tanto para el firmware de Rotorflight como a este software son: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... y muchos más que han dedicado horas probando y proveyendo feedback! Traducción al Español: <PERSON>", "needs_translation": "false"}, "msp_transport": {"english": "MSP Transport", "translation": "Transporte MSP", "needs_translation": "false"}}, "rates_advanced": {"dyn_ceiling_gain": {"english": "Dynamic ceiling gain", "translation": "Tope Ganancia", "needs_translation": "false"}, "acc_limit": {"english": "Accelerometer Limit", "translation": "Límite Acel.", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Alabe<PERSON>", "needs_translation": "false"}, "yaw_dynamics": {"english": "Yaw dynamics", "translation": "Dinám. de Dirección", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Cabeceo", "needs_translation": "false"}, "col": {"english": "Col", "translation": "Colec.", "needs_translation": "false"}, "setpoint_boost_cutoff": {"english": "Setpoint boost cutoff", "translation": "Corte Ref.", "needs_translation": "false"}, "yaw_dynamic_deadband_gain": {"english": "D. <PERSON>", "translation": "Banda Muerta Rot.", "needs_translation": "false"}, "rates_type": {"english": "Rates Type", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "setpoint_boost_gain": {"english": "Setpoint boost gain", "translation": "Ganancia Ref.", "needs_translation": "false"}, "msg_reset_to_defaults": {"english": "Rate type changed. Values will be reset to defaults.", "translation": "Cambió el tipo de tasa. Los valores se cambiaron a los por defecto.", "needs_translation": "false"}, "yaw_dynamic_ceiling_gain": {"english": "Ceiling", "translation": "Tope Dirección", "needs_translation": "false"}, "yaw_boost": {"english": "Yaw boost", "translation": "Impulso de Dirección", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "Ganancia", "needs_translation": "false"}, "rate_table": {"english": "Rate Table", "translation": "Tabla de Tasas", "needs_translation": "false"}, "dynamics": {"english": "Dynamics", "translation": "Dinámica", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Dirección", "needs_translation": "false"}, "yaw_dynamic_deadband_filter": {"english": "Filter", "translation": "Filtro Rot.", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "Tasa<PERSON>", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Límite", "needs_translation": "false"}, "help_rate_table": {"english": "Please select the rate you would like to use. Saving will apply the choice to the active profile.", "translation": "Seleccione la tasa que desea utilizar. Al guardar se aplicará la elección al perfil activo.", "needs_translation": "false"}, "help_p1": {"english": "Rates type: Choose the rate type you prefer flying with. Raceflight and Actual are the most straightforward.", "translation": "Tipo de Tasas: Seleccione el tipo de tasa con la que prefiere volar. Raceflight y Actual son las mas directas.", "needs_translation": "false"}, "pitch_boost": {"english": "Pitch boost", "translation": "Impulso de cabeceo", "needs_translation": "false"}, "help_p2": {"english": "Dynamics: Applied regardless of rates type. Typically left on defaults but can be adjusted to smooth heli movements, like with scale helis.", "translation": "Dinámicas: Se aplican independientemente de los tipos de tasas. Normalmente se dejan los valores por defecto, pero pueden ser ajustados para suavizar los movimientos del heli, como es el caso de los modelos a escala.", "needs_translation": "false"}, "accel_limit": {"english": "Accel", "translation": "Acel.", "needs_translation": "false"}, "dyn_deadband_filter": {"english": "Dynamic deadband filter", "translation": "Filtro Zona Muerta", "needs_translation": "false"}, "roll_boost": {"english": "Roll boost", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "dyn_deadband_gain": {"english": "Dynamic deadband gain", "translation": "Gan. <PERSON>", "needs_translation": "false"}, "collective_dynamics": {"english": "Collective dynamics", "translation": "Dinám. de Colectivo", "needs_translation": "false"}, "roll_dynamics": {"english": "Roll dynamics", "translation": "Dinámica de Alabeo", "needs_translation": "false"}, "collective_boost": {"english": "Collective boost", "translation": "Impulso colectivo", "needs_translation": "false"}, "pitch_dynamics": {"english": "Pitch dynamics", "translation": "Dinám. de Cabeceo", "needs_translation": "false"}, "response_time": {"english": "Response Time", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "servos": {"tbl_yes": {"english": "YES", "translation": "SI", "needs_translation": "false"}, "enable_servo_override": {"english": "Enable servo override", "translation": "Activar toma de servo", "needs_translation": "false"}, "disabling_servo_override": {"english": "Disabling servo override...", "translation": "Desactivando toma de servo...", "needs_translation": "false"}, "help_tool_p3": {"english": "Minimum/Maximum: Adjust the end points of the selected servo.", "translation": "Mínimo/Máximo: Ajuste de los puntos límites del servo seleccionado.", "needs_translation": "false"}, "tail": {"english": "TAIL", "translation": "COLA", "needs_translation": "false"}, "scale_negative": {"english": "Scale Negative", "translation": "Escala Negativa", "needs_translation": "false"}, "help_tool_p1": {"english": "Override: [*] Enable override to allow real-time updates of servo center point.", "translation": "Toma de servo: [*] Active la toma de control del servo para permitir la actualización en tiempo real del punto central del servo.", "needs_translation": "false"}, "tbl_no": {"english": "NO", "translation": "NO", "needs_translation": "false"}, "maximum": {"english": "Maximum", "translation": "Máximo", "needs_translation": "false"}, "help_tool_p6": {"english": "Speed: The speed the servo moves. Generally only used for the cyclic servos to help the swash move evenly. Optional - leave all at 0 if unsure.", "translation": "Velocidad: La velocidad a la que se mueve el servo. Generalmente se utiliza solamente para los servos del cíclico para ayudar a que el swashplate se mueva en forma pareja. Opcional - deje todo en 0 si no está seguro.", "needs_translation": "false"}, "help_fields_rate": {"english": "Servo PWM rate.", "translation": "Tasa PWM del servo.", "needs_translation": "false"}, "cyc_pitch": {"english": "CYC.PITCH", "translation": "PASO CICL.", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Centro", "needs_translation": "false"}, "minimum": {"english": "Minimum", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "speed": {"english": "Speed", "translation": "Velocidad", "needs_translation": "false"}, "help_fields_speed": {"english": "Servo motion speed in milliseconds.", "translation": "Velocidad de movimiento del servo en milisegundos.", "needs_translation": "false"}, "disable_servo_override": {"english": "Disable servo override", "translation": "Desactivar toma de servo", "needs_translation": "false"}, "help_fields_scale_pos": {"english": "Servo positive scaling.", "translation": "Escala positiva del servo.", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "Guardando da<PERSON>...", "needs_translation": "false"}, "cyc_left": {"english": "CYC.LEFT", "translation": "CICL. IZQ", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "Guardando", "needs_translation": "false"}, "name": {"english": "Servos", "translation": "Servos", "needs_translation": "false"}, "help_tool_p5": {"english": "Rate: The frequency the servo runs best at - check with manufacturer.", "translation": "Tasa: La frecuencia a la que el servo trabaja mejor. - consulte al fabricante.", "needs_translation": "false"}, "help_tool_p2": {"english": "Center: Adjust the center position of the servo.", "translation": "Centro: Ajuste de la posición central del servo.", "needs_translation": "false"}, "enabling_servo_override": {"english": "Enabling servo override...", "translation": "Activando toma de servo...", "needs_translation": "false"}, "servo_prefix": {"english": "SERVO ", "translation": "SERVO ", "needs_translation": "false"}, "reverse": {"english": "Reverse", "translation": "Invertir sentido", "needs_translation": "false"}, "enable_servo_override_msg": {"english": "Servo override allows you to 'trim' your servo center point in real time.", "translation": "La toma de control del servo permite ajustar en tiempo real el punto central de su servo.", "needs_translation": "false"}, "cyc_right": {"english": "CYC.RIGHT", "translation": "CICL. DER", "needs_translation": "false"}, "help_default_p2": {"english": "Primary flight controls that use the rotorflight mixer will display in the section called 'mixer'.", "translation": "Los controles primarios que utilizan el mezclador de Rotorflight aparecen en la sección llamada 'mezclador'.", "needs_translation": "false"}, "scale_positive": {"english": "Scale Positive", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_default_p1": {"english": "Please select the servo you would like to configure from the list below.", "translation": "Seleccione el servo que desea configurar en la lista que sigue.", "needs_translation": "false"}, "servo_override": {"english": "Servo Override", "translation": "Toma de Servo", "needs_translation": "false"}, "disable_servo_override_msg": {"english": "Return control of the servos to the flight controller.", "translation": "Devolver el control de los servos al controlador de vuelo.", "needs_translation": "false"}, "help_fields_min": {"english": "Servo negative travel limit.", "translation": "Límite de desplazamiento negativo del servo.", "needs_translation": "false"}, "help_default_p3": {"english": "Any other servos that are not controlled by the primary flight mixer will be displayed in the section called 'Other servos'.", "translation": "Aquellos servos que no están controlados por el controlador de vuelo primario aparecen en la sección llamada 'Otros servos'.", "needs_translation": "false"}, "help_fields_mid": {"english": "Servo center position pulse width.", "translation": "Ancho del pulso en la posición central del servo.", "needs_translation": "false"}, "help_fields_scale_neg": {"english": "Servo negative scaling.", "translation": "Escala negativa del servo.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_tool_p4": {"english": "Scale: Adjust the amount the servo moves for a given input.", "translation": "Escala: Ajuste de la cantidad de movimiento del servo en función del movimiento del comamdo.", "needs_translation": "false"}, "help_fields_flags": {"english": "0 = <PERSON><PERSON><PERSON>, 1=Reverse, 2 = Geo Correction, 3 = Reverse + Geo Correction", "translation": "0 = <PERSON><PERSON> <PERSON><PERSON>, 1 = Reversa, 2 = Corrección-Geo, 3 = Reversa + Corrección-Geo", "needs_translation": "false"}, "geometry": {"english": "Geometry", "translation": "Corrección Geometría", "needs_translation": "false"}, "help_fields_max": {"english": "Servo positive travel limit.", "translation": "Límite de desplazamiento positivo del servo.", "needs_translation": "false"}}, "profile_autolevel": {"acro_trainer": {"english": "Acro trainer", "translation": "Entrenador Acro", "needs_translation": "false"}, "angle_mode": {"english": "Angle mode", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Máx", "needs_translation": "false"}, "name": {"english": "Autolevel", "translation": "Autonivelado", "needs_translation": "false"}, "help_p1": {"english": "Acro Trainer: How aggressively the heli tilts back to level when flying in Acro Trainer Mode.", "translation": "Entrenador Acro: Que tan agresivamente el helicótero vuelve a nivel en el modo Entrenador Acro.", "needs_translation": "false"}, "horizon_mode": {"english": "Horizon mode", "translation": "Modo Horizonte", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "Ganancia", "needs_translation": "false"}, "help_p2": {"english": "Angle Mode: How aggressively the heli tilts back to level when flying in Angle Mode.", "translation": "<PERSON><PERSON>: Que tan agresivamente el helicótero vuelve a nivel en el modo Angulo.", "needs_translation": "false"}, "help_p3": {"english": "Horizon Mode: How aggressively the heli tilts back to level when flying in Horizon Mode.", "translation": "Modo Horizonte: Que tan agresivamente el helicótero vuelve a nivel en el modo Horizonte.", "needs_translation": "false"}}, "filters": {"filter_type": {"english": "Filter type", "translation": "Tipo de Filtro", "needs_translation": "false"}, "help_p4": {"english": "Dynamic Notch Filters: Automatically creates notch filters within the min and max frequency range.", "translation": "Puntos de Filtros Dinámicos: Crea automáticamente los puntos de filtro dentro del rango de frecuencias mín y máx", "needs_translation": "false"}, "notch_c": {"english": "Notch Count", "translation": "Nro. de Puntos", "needs_translation": "false"}, "rpm_preset": {"english": "Type", "translation": "Tipo", "needs_translation": "false"}, "lowpass_1": {"english": "Lowpass 1", "translation": "Pasabajos 1", "needs_translation": "false"}, "rpm_min_hz": {"english": "<PERSON><PERSON>", "translation": "Frecuencia M<PERSON>.", "needs_translation": "false"}, "help_p2": {"english": "Gyro lowpass: Lowpass filters for the gyro signal. Typically left at default.", "translation": "Pasabajos Gyro: Filtros pasabajos para la señal del giroscopio. Normalmente se dejan los valores por defecto.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Corte", "needs_translation": "false"}, "notch_1": {"english": "Notch 1", "translation": "Punto 1", "needs_translation": "false"}, "max_cutoff": {"english": "Max cutoff", "translation": "Corte Máx", "needs_translation": "false"}, "help_p3": {"english": "Gyro notch filters: Use for filtering specific frequency ranges. Typically not needed in most helis.", "translation": "Puntos de filtro de Gyro: Utilizado para filtrar rangos específicos de frecuencia. Generalmente no se necesitan en la mayoría de los helicópteros.", "needs_translation": "false"}, "lowpass_2": {"english": "Lowpass 2", "translation": "Pasabajos 2", "needs_translation": "false"}, "rpm_filter": {"english": "RPM filter", "translation": "Filtro RPM", "needs_translation": "false"}, "help_p1": {"english": "Typically you would not edit this page without checking your Blackbox logs!", "translation": "Normalente no se edita esta página sin verificar los logs de la Caja Negra!", "needs_translation": "false"}, "dyn_notch": {"english": "Dynamic Filters", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "notch_q": {"english": "Notch Q", "translation": "Punto Q", "needs_translation": "false"}, "lowpass_1_dyn": {"english": "Lowpass 1 dyn.", "translation": "Pasabajos 1 din.", "needs_translation": "false"}, "notch_min_hz": {"english": "Min", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "notch_max_hz": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "notch_2": {"english": "Notch 2", "translation": "Punto 2", "needs_translation": "false"}, "name": {"english": "Filters", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "min_cutoff": {"english": "Min cutoff", "translation": "Corte Mín", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Centro", "needs_translation": "false"}}, "status": {"arming_disable_flag_3": {"english": "Bad RX Recovery", "translation": "Falla Recup. RX", "needs_translation": "false"}, "arming_disable_flag_20": {"english": "RPM Filter", "translation": "Filtro RPM", "needs_translation": "false"}, "arming_disable_flag_11": {"english": "Load", "translation": "Carga", "needs_translation": "false"}, "arming_disable_flag_22": {"english": "DSHOT Bitbang", "translation": "DSHOT Bitbang", "needs_translation": "false"}, "dataflash_free_space": {"english": "Dataflash Free Space", "translation": "Espacio libre en flash", "needs_translation": "false"}, "arming_disable_flag_25": {"english": "Arm Switch", "translation": "Botón Armado", "needs_translation": "false"}, "erasing": {"english": "Erasing", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_9": {"english": "Boot <PERSON>", "translation": "Tiempo gracia inicio", "needs_translation": "false"}, "megabyte": {"english": "MB", "translation": "MB", "needs_translation": "false"}, "arming_disable_flag_17": {"english": "Paralyze", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_5": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "arming_disable_flag_8": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_1": {"english": "Fail Safe", "translation": "A prueba de fallos", "needs_translation": "false"}, "cpu_load": {"english": "CPU Load", "translation": "Carga de CPU", "needs_translation": "false"}, "arming_disable_flag_15": {"english": "BST", "translation": "BST", "needs_translation": "false"}, "arming_disable_flag_12": {"english": "Calibrating", "translation": "Calibrando", "needs_translation": "false"}, "arming_disable_flag_19": {"english": "Resc", "translation": "Resc", "needs_translation": "false"}, "arming_disable_flag_4": {"english": "Box Fail Safe", "translation": "Pérdida Box", "needs_translation": "false"}, "arming_disable_flag_24": {"english": "Motor Protocol", "translation": "Protocolo Motor", "needs_translation": "false"}, "real_time_load": {"english": "Real-time Load", "translation": "Carga Tiempo-Real", "needs_translation": "false"}, "help_p2": {"english": "To erase the dataflash for more log file storage, press the button on the menu denoted by a '*'.", "translation": "Para borrar la flash de datos y guardar nuevos LOGs, oprima el botón del menú marcado con '*'.", "needs_translation": "false"}, "arming_disable_flag_2": {"english": "RX Fail Safe", "translation": "Pérdida RX", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "arming_disable_flag_0": {"english": "No Gyro", "translation": "No hay Gyro", "needs_translation": "false"}, "arming_disable_flag_18": {"english": "GPS", "translation": "GPS", "needs_translation": "false"}, "help_p1": {"english": "Use this page to view your current flight controller status. This can be useful when determining why your heli will not arm.", "translation": "Utilice esta página para ver el estado del controlador de vuelo. Puede ser útil para determinar la razón por la que el heli no se puede armar.", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Flags de Armado", "needs_translation": "false"}, "unsupported": {"english": "Unsupported", "translation": "No soportado", "needs_translation": "false"}, "erase_prompt": {"english": "Would you like to erase the dataflash?", "translation": "Quiere borrar la flash de datos?", "needs_translation": "false"}, "erase": {"english": "Erase", "translation": "Bo<PERSON>r", "needs_translation": "false"}, "arming_disable_flag_10": {"english": "No Pre Arm", "translation": "No Pre Armado", "needs_translation": "false"}, "arming_disable_flag_21": {"english": "Reboot Required", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "Status", "translation": "Estado", "needs_translation": "false"}, "arming_disable_flag_13": {"english": "CLI", "translation": "CLI", "needs_translation": "false"}, "arming_disable_flag_14": {"english": "CMS Menu", "translation": "Menú CMS", "needs_translation": "false"}, "arming_disable_flag_16": {"english": "MSP", "translation": "MSP", "needs_translation": "false"}, "arming_disable_flag_7": {"english": "<PERSON>hrottle", "translation": "Acelerador", "needs_translation": "false"}, "erasing_dataflash": {"english": "Erasing dataflash...", "translation": "Bo<PERSON>ndo flash de datos...", "needs_translation": "false"}, "arming_disable_flag_23": {"english": "Acc Calibration", "translation": "Calibración Acc.", "needs_translation": "false"}}, "profile_pidbandwidth": {"help_p1": {"english": "PID Bandwidth: Overall bandwidth in HZ used by the PID loop.", "translation": "Ancho Banda PID: Es el ancho de banda en Hertz utilizado por el loop PID.", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "name": {"english": "PID Bandwidth", "translation": "Ancho <PERSON>a PID", "needs_translation": "false"}, "bterm_cutoff": {"english": "B-term cut-off", "translation": "Corte B-term", "needs_translation": "false"}, "help_p3": {"english": "B-term cutoff: B-term cutoff frequency in HZ.", "translation": "Corte B-term: Frecuencia en Hz del corte B-term.", "needs_translation": "false"}, "dterm_cutoff": {"english": "D-term cut-off", "translation": "Corte D-term", "needs_translation": "false"}, "help_p2": {"english": "D-term cutoff: D-term cutoff frequency in HZ.", "translation": "Corte D-term: Frecuencia en Hz del corte D-term.", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}}}, "navigation_save": {"english": "SAVE", "translation": "Guarda", "needs_translation": "false"}, "menu_section_flight_tuning": {"english": "Flight Tuning", "translation": "Ajustes de Vuelo", "needs_translation": "false"}, "error_timed_out": {"english": "Error: timed out", "translation": "Error: tiempo agotado", "needs_translation": "false"}, "check_rf_module_on": {"english": "Please check your rf module is turned on.", "translation": "Verifique que el módulo RF esté encendido.", "needs_translation": "false"}, "msg_saving": {"english": "Saving...", "translation": "Guardando...", "needs_translation": "false"}, "msg_save_not_commited": {"english": "Save not committed to EEPROM", "translation": "No se copió a EEPROM", "needs_translation": "false"}, "menu_section_advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "msg_loading_from_fbl": {"english": "Loading data from flight controller...", "translation": "Cargando datos del controlador de vuelo...", "needs_translation": "false"}, "msg_reload_settings": {"english": "Reload data from flight controller?", "translation": "Recar<PERSON> datos del controlador de vuelo?", "needs_translation": "false"}, "menu_section_tools": {"english": "Tools", "translation": "Herramientas", "needs_translation": "false"}, "msg_connecting": {"english": "Connecting", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "msg_save_current_page": {"english": "Save current page to flight controller?", "translation": "Guarda esta página en el controlador de vuelo?", "needs_translation": "false"}, "btn_ok_long": {"english": "                OK                ", "translation": "                OK                ", "needs_translation": "false"}, "check_discovered_sensors": {"english": "Please check you have discovered all sensors.", "translation": "Verifique que ha descubierto todos los sensores.", "needs_translation": "false"}, "msg_loading": {"english": "Loading...", "translation": "Cargando...", "needs_translation": "false"}, "check_heli_on": {"english": "Please check your heli is powered up and radio connected.", "translation": "Verifique que el helicóptero esté encendido y el radio conectado.", "needs_translation": "false"}, "check_bg_task": {"english": "Please enable the background task.", "translation": "Habilite tarea en segundo plano (en LUA del modelo).", "needs_translation": "false"}, "navigation_tools": {"english": "*", "translation": "*", "needs_translation": "false"}, "check_supported_version": {"english": "This version of the Lua script \ncan't be used with the selected model", "translation": "Esta versión del script Lua \nno se puede utilizar con el modelo seleccionado", "needs_translation": "false"}}