{"btn_ok": {"english": "          OK           ", "translation": "          OK           ", "needs_translation": "false"}, "btn_close": {"english": "CLOSE", "translation": "FERMER", "needs_translation": "false"}, "navigation_menu": {"english": "MENU", "translation": "MENU", "needs_translation": "false"}, "menu_section_hardware": {"english": "Hardware", "translation": "Materiel", "needs_translation": "false"}, "msg_please_disarm_to_save_warning": {"english": "Settings will only be saved to e<PERSON>rom on disarm", "translation": "Les parametres ne seront sauvegardes dans l'EEPROM qu'apres desarmement", "needs_translation": "false"}, "msg_saving_settings": {"english": "Saving settings...", "translation": "Enregistrement des parametres...", "needs_translation": "false"}, "msg_saving_to_fbl": {"english": "Saving data to flight controller...", "translation": "Enregistrement des donnees dans le controleur de vol...", "needs_translation": "false"}, "navigation_reload": {"english": "RELOAD", "translation": "RECHAR.", "needs_translation": "false"}, "menu_section_developer": {"english": "Developer", "translation": "Developpeur", "needs_translation": "false"}, "check_msp_version": {"english": "Unable to determine MSP version in use.", "translation": "Impossible de determiner la version MSP utilisee.", "needs_translation": "false"}, "menu_section_about": {"english": "About", "translation": "A propos", "needs_translation": "false"}, "msg_please_disarm_to_save": {"english": "Please disarm to save", "translation": "Veuillez desarmer pour enregistrer afin d'assurer l'integrite des donnees.", "needs_translation": "false"}, "unit_hertz": {"english": "Hz", "translation": "Hz", "needs_translation": "false"}, "msg_rebooting": {"english": "Rebooting...", "translation": "Redemarrage...", "needs_translation": "false"}, "msg_save_settings": {"english": "Save settings", "translation": "Enregistrer les parametres", "needs_translation": "false"}, "btn_cancel": {"english": "CANCEL", "translation": "ANNULER", "needs_translation": "false"}, "msg_connecting_to_fbl": {"english": "Connecting to flight controller...", "translation": "Connexion au controleur de vol...", "needs_translation": "false"}, "navigation_help": {"english": "?", "translation": "?", "needs_translation": "false"}, "modules": {"stats": {"name": {"english": "Stats", "translation": "Statistiques", "needs_translation": "true"}, "totalflighttime": {"english": "Total Flight Time", "translation": "Temps de vol total", "needs_translation": "true"}, "flightcount": {"english": "Flight Count", "translation": "Nombre de vols", "needs_translation": "true"}, "lastflighttime": {"english": "Last Flight Time", "translation": "Dernier temps de vol", "needs_translation": "true"}, "help_p1": {"english": "Use this module to update the recorded flight statistics on the flight controller.", "translation": "Utilisez ce module pour mettre à jour les statistiques de vol enregistrées sur le contrôleur de vol.", "needs_translation": "true"}}, "settings": {"name": {"english": "Settings", "translation": "Paramètres", "needs_translation": "false"}, "no_themes_available_to_configure": {"english": "No configurable themes installed on this device", "translation": "Aucun theme configurable n'est installe sur cet appareil", "needs_translation": "false"}, "txt_audio_timer": {"english": "Timer", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "txt_audio_events": {"english": "Events", "translation": "Evenements", "needs_translation": "false"}, "txt_audio_switches": {"english": "Switches", "translation": "Interrupteurs", "needs_translation": "false"}, "txt_iconsize": {"english": "Icon Size", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "txt_general": {"english": "General", "translation": "General", "needs_translation": "false"}, "txt_text": {"english": "TEXT", "translation": "TEXTE", "needs_translation": "false"}, "txt_small": {"english": "SMALL", "translation": "PETIT", "needs_translation": "false"}, "txt_large": {"english": "LARGE", "translation": "GRAND", "needs_translation": "false"}, "txt_syncname": {"english": "Sync model name", "translation": "Synchroniser nom modele", "needs_translation": "false"}, "txt_devtools": {"english": "Developer Tools", "translation": "Outils developpeur", "needs_translation": "false"}, "txt_apiversion": {"english": "API Version", "translation": "Version API", "needs_translation": "false"}, "txt_logging": {"english": "Logging", "translation": "Journalisation", "needs_translation": "false"}, "txt_compilation": {"english": "Compilation", "translation": "Compilation", "needs_translation": "false"}, "txt_loglocation": {"english": "Log location", "translation": "Emplacement Journal", "needs_translation": "false"}, "txt_console": {"english": "CONSOLE", "translation": "CONSOLE", "needs_translation": "false"}, "txt_consolefile": {"english": "CONSOLE & FILE", "translation": "CONSOLE ET FICHIER", "needs_translation": "false"}, "txt_loglevel": {"english": "Log level", "translation": "Niveau journal", "needs_translation": "false"}, "txt_off": {"english": "OFF", "translation": "DESACTIVE", "needs_translation": "false"}, "txt_info": {"english": "INFO", "translation": "INFO", "needs_translation": "false"}, "txt_debug": {"english": "DEBUG", "translation": "DEBUG", "needs_translation": "false"}, "txt_mspdata": {"english": "Log msp data", "translation": "Journaliser donnees MSP", "needs_translation": "false"}, "txt_queuesize": {"english": "Log MSP queue size", "translation": "Taille de file d'attente MSP Journal", "needs_translation": "false"}, "txt_memusage": {"english": "Log memory usage", "translation": "Journaliser utilisation memoire", "needs_translation": "false"}, "txt_batttype": {"english": "Tx Battery Options", "translation": "Tx Battery Options", "needs_translation": "true"}, "txt_battdef": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "true"}, "txt_batttext": {"english": "Text", "translation": "Text", "needs_translation": "true"}, "txt_battdig": {"english": "Digital", "translation": "Digital", "needs_translation": "true"}, "dashboard": {"english": "Dashboard", "translation": "Tableau de bord", "needs_translation": "false"}, "dashboard_theme": {"english": "Theme", "translation": "Theme", "needs_translation": "false"}, "dashboard_theme_panel_global": {"english": "Default theme for all models", "translation": "Theme par defaut pour tous les modeles", "needs_translation": "false"}, "dashboard_theme_panel_model": {"english": "Optional theme for this model", "translation": "Theme optionnel pour ce modele", "needs_translation": "false"}, "dashboard_theme_panel_model_disabled": {"english": "Disabled", "translation": "Desactive", "needs_translation": "false"}, "dashboard_settings": {"english": "Settings", "translation": "Options", "needs_translation": "false"}, "dashboard_theme_preflight": {"english": "Preflight Theme", "translation": "Theme prevol", "needs_translation": "false"}, "dashboard_theme_inflight": {"english": "Inflight Theme", "translation": "Theme en vol", "needs_translation": "false"}, "dashboard_theme_postflight": {"english": "Postflight Theme", "translation": "Theme apres vol", "needs_translation": "false"}, "audio": {"english": "Audio", "translation": "Audio", "needs_translation": "false"}, "localizations": {"english": "Localization", "translation": "Localisation", "needs_translation": "false"}, "txt_development": {"english": "Development", "translation": "Developpement", "needs_translation": "false"}, "temperature_unit": {"english": "Temperature Unit", "translation": "Unite de temperature", "needs_translation": "false"}, "altitude_unit": {"english": "Altitude Unit", "translation": "Unite d'altitude", "needs_translation": "false"}, "celcius": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "fahrenheit": {"english": "Fahrenheit", "translation": "Fahrenheit", "needs_translation": "false"}, "meters": {"english": "Meters", "translation": "Metres", "needs_translation": "false"}, "feet": {"english": "Feet", "translation": "Pieds", "needs_translation": "false"}, "warning": {"english": "Warning", "translation": "Avertissement", "needs_translation": "false"}, "governor_state": {"english": "Governor State", "translation": "Etat du <PERSON>", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Indicatifs d'armement", "needs_translation": "false"}, "voltage": {"english": "Voltage", "translation": "Tension", "needs_translation": "false"}, "pid_rates_profile": {"english": "PID/Rates Profile", "translation": "Profil PID/Taux", "needs_translation": "false"}, "pid_profile": {"english": "PID Profile", "translation": "<PERSON>il de <PERSON>", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "esc_temperature": {"english": "ESC Temperature", "translation": "Temperature ESC", "needs_translation": "false"}, "esc_threshold": {"english": "Thresh<PERSON> (°)", "translation": "<PERSON><PERSON> (deg)", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "Tension BEC", "needs_translation": "false"}, "bec_threshold": {"english": "<PERSON><PERSON><PERSON><PERSON> (V)", "translation": "Seuil (Volt)", "needs_translation": "false"}, "fuel": {"english": "Fuel", "translation": "Carburant", "needs_translation": "false"}, "fuel_callout_default": {"english": "Default (Only at 10%)", "translation": "Par defaut (uniquement a 10%)", "needs_translation": "false"}, "fuel_callout_10": {"english": "Every 10%", "translation": "Chaque 10%", "needs_translation": "false"}, "fuel_callout_20": {"english": "Every 20%", "translation": "Chaque 20%", "needs_translation": "false"}, "fuel_callout_25": {"english": "Every 25%", "translation": "Every 25%", "needs_translation": "false"}, "fuel_callout_50": {"english": "Every 50%", "translation": "Every 50%", "needs_translation": "false"}, "fuel_callout_percent": {"english": "Callout %", "translation": "Annonce %", "needs_translation": "false"}, "fuel_repeats_below": {"english": "Repeats below 0%", "translation": "Rappels en dessous de 0%", "needs_translation": "false"}, "fuel_haptic_below": {"english": "Haptic below 0%", "translation": "Vibration en dessous de 0%", "needs_translation": "false"}, "timer_alerting": {"english": "Timer Alerting", "translation": "<PERSON><PERSON>e minuteur", "needs_translation": "false"}, "timer_elapsed_alert_mode": {"english": "Timer <PERSON><PERSON><PERSON>", "translation": "Alerte fin de minuteur", "needs_translation": "false"}, "timer_prealert_options": {"english": "Pre-timer <PERSON><PERSON>", "translation": "Options d'alerte avant minuteur", "needs_translation": "false"}, "timer_prealert": {"english": "Pre-timer <PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON> avant minuteur", "needs_translation": "false"}, "timer_alert_period": {"english": "Alert <PERSON>", "translation": "Periode d'alerte", "needs_translation": "false"}, "timer_postalert_options": {"english": "Post-timer <PERSON><PERSON>", "translation": "Options d'alerte apres minuteur", "needs_translation": "false"}, "timer_postalert": {"english": "Post-timer <PERSON><PERSON>", "translation": "<PERSON><PERSON>e apres minuteur", "needs_translation": "false"}, "timer_postalert_period": {"english": "Alert <PERSON>", "translation": "Periode alerte", "needs_translation": "false"}, "timer_postalert_interval": {"english": "<PERSON><PERSON>", "translation": "Intervalle alerte", "needs_translation": "false"}}, "validate_sensors": {"help_p1": {"english": "This tool attempts to list all the sensors that you are not receiving in a concise list.", "translation": "Cet outil tente de lister tous les capteurs que vous ne recevez pas dans une liste concise.", "needs_translation": "false"}, "invalid": {"english": "INVALID", "translation": "INVALIDE", "needs_translation": "false"}, "name": {"english": "Sensors", "translation": "Capteurs", "needs_translation": "false"}, "msg_repair": {"english": "Enable required sensors on flight controller?", "translation": "Activer les capteurs requis sur le controleur de vol ?", "needs_translation": "false"}, "msg_repair_fin": {"english": "The flight controller has been configured? You may need to perform a discover sensors to see the changes.", "translation": "Le controleur de vol a-t-il ete configure ? Vous devrez peut-etre effectuer une detection des capteurs pour voir les modifications.", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "Use this tool to ensure you are sending the correct sensors.", "translation": "Utilisez cet outil pour vous assurer que vous envoyez les bons capteurs.", "needs_translation": "false"}}, "msp_exp": {"help_p1": {"english": "This tool provides the ability to send a custom byte string to the flight controller. It is useful for developers when debugging values.", "translation": "Cet outil permet d'envoyer une chaîne d'octets personnalisee au controleur de vol. Il est utile aux developpeurs pour le debogage des valeurs.", "needs_translation": "false"}, "name": {"english": "MSP Expermental", "translation": "MSP Experimental", "needs_translation": "false"}, "help_p2": {"english": "If you do not understand what you are doing, do not use it as bad things can happen.", "translation": "Si vous ne comprenez pas ce que vous faites, ne l'utilisez pas, car des problemes graves pourraient survenir.", "needs_translation": "false"}}, "esc_tools": {"unknown": {"english": "UNKNOWN", "translation": "INCONNU", "needs_translation": "false"}, "name": {"english": "ESC Tools", "translation": "Outils ESC", "needs_translation": "false"}, "please_powercycle": {"english": "Please power cycle the ESC...", "translation": "Veuillez redemarrer l'ESC...", "needs_translation": "false"}, "mfg": {"hw5": {"esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "brake_force": {"english": "Brake Force%", "translation": "% Force frein", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Rotation", "needs_translation": "false"}, "soft_start": {"english": "Soft Start", "translation": "Demar. Prog.", "needs_translation": "false"}, "name": {"english": "Hobbywing V5", "translation": "Hobbywing V5", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Limites", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "Tension BEC", "needs_translation": "false"}, "gov_i_gain": {"english": "I-G<PERSON>", "translation": "G<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "startup_time": {"english": "Startup Time", "translation": "Temps demar.", "needs_translation": "false"}, "lipo_cell_count": {"english": "LiPo Cell Count", "translation": "Nombre cellules LiPo", "needs_translation": "false"}, "restart_time": {"english": "Restart Time", "translation": "<PERSON><PERSON> redemarrage", "needs_translation": "false"}, "volt_cutoff_type": {"english": "Volt Cutoff Type", "translation": "Type coupure tension", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "brake_type": {"english": "Brake Type", "translation": "Type de frein", "needs_translation": "false"}, "brake": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Gouverneur", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Avance", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basique", "needs_translation": "false"}, "flight_mode": {"english": "Flight Mode", "translation": "Mode de vol", "needs_translation": "false"}, "auto_restart": {"english": "Auto Restart", "translation": "Redemarrage auto", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Roue libre active", "needs_translation": "false"}, "cutoff_voltage": {"english": "Cutoff Voltage", "translation": "Tension de coupure", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Puissance demarrage", "needs_translation": "false"}, "other": {"english": "Other", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Timing", "needs_translation": "false"}, "gov_p_gain": {"english": "P-Gain", "translation": "Gain-<PERSON>", "needs_translation": "false"}}, "xdfly": {"hv_bec_voltage": {"english": "HV BEC Voltage", "translation": "Tension HV BEC", "needs_translation": "false"}, "gov": {"english": "Governor", "translation": "Gouverneur", "needs_translation": "false"}, "brake_force": {"english": "Brake Force", "translation": "Force de freinage", "needs_translation": "false"}, "sr_function": {"english": "SR Function", "translation": "Synchronisation Rectificative", "needs_translation": "false"}, "name": {"english": "XDFLY", "translation": "XDFLY", "needs_translation": "false"}, "lv_bec_voltage": {"english": "LV BEC Voltage", "translation": "Tension LV BEC", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Temps redemarrage auto", "needs_translation": "false"}, "acceleration": {"english": "Acceleration", "translation": "Acceleration", "needs_translation": "false"}, "motor_direction": {"english": "Motor Direction", "translation": "<PERSON><PERSON> moteur", "needs_translation": "false"}, "smart_fan": {"english": "Smart Fan", "translation": "Ventilateur intelligent", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Gouverneur", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Avance", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "Coupure cellule", "needs_translation": "false"}, "led_color": {"english": "LED Color", "translation": "Couleur LED", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basique", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Puissance demarrage", "needs_translation": "false"}, "motor_poles": {"english": "Motor Poles", "translation": "Poles moteur", "needs_translation": "false"}, "capacity_correction": {"english": "Capacity Correction", "translation": "Correction capacite", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Timing", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}, "flrtr": {"gov": {"english": "Governor", "translation": "Gouverneur", "needs_translation": "false"}, "motor_temp_sensor": {"english": "Motor temp sensor", "translation": "Capteur de temperature moteur", "needs_translation": "false"}, "starting_torque": {"english": "Starting torque", "translation": "<PERSON><PERSON><PERSON> de demarrage", "needs_translation": "false"}, "cell_count": {"english": "Cell count", "translation": "Nombre de cellules", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}, "motor_erpm_max": {"english": "Motor ERPM max", "translation": "ERPM max moteur", "needs_translation": "false"}, "name": {"english": "FLYROTOR", "translation": "FLYROTOR", "needs_translation": "false"}, "low_voltage_protection": {"english": "Low voltage protection", "translation": "Protection basse tension", "needs_translation": "false"}, "gov_d": {"english": "Gov-D", "translation": "Gov-D", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry protocol", "translation": "Protocole de telemetrie", "needs_translation": "false"}, "motor_direction": {"english": "Motor direction", "translation": "<PERSON><PERSON> moteur", "needs_translation": "false"}, "throttle_protocol": {"english": "Throttle protocol", "translation": "Protocole des gaz", "needs_translation": "false"}, "soft_start": {"english": "Soft start", "translation": "Demarrage progressif", "needs_translation": "false"}, "other": {"english": "Other", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "temperature_protection": {"english": "Temperature protection", "translation": "Protection temperature", "needs_translation": "false"}, "buzzer_volume": {"english": "Buzzer volume", "translation": "Volume du buzzer", "needs_translation": "false"}, "timing_angle": {"english": "Timing angle", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Gouverneur", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Avance", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "bec_voltage": {"english": "BEC voltage", "translation": "Tension BEC", "needs_translation": "false"}, "fan_control": {"english": "Fan control", "translation": "Controle ventilateur", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basique", "needs_translation": "false"}, "current_gain": {"english": "Current gain", "translation": "<PERSON><PERSON> courant", "needs_translation": "false"}, "led_color": {"english": "LED color", "translation": "Couleur LED", "needs_translation": "false"}, "motor_temp": {"english": "Motor temperture", "translation": "Temperature moteur", "needs_translation": "false"}, "response_speed": {"english": "Response speed", "translation": "Vitesse de reponse", "needs_translation": "false"}, "battery_capacity": {"english": "Battery capacity", "translation": "Capacite de la batterie", "needs_translation": "false"}}, "scorp": {"esc_mode": {"english": "ESC Mode", "translation": "Mode ESC", "needs_translation": "false"}, "min_voltage": {"english": "Min Voltage", "translation": "Tension min", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Rotation", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry Protocol", "translation": "Protocole telemetrie", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "runup_time": {"english": "Runup Time", "translation": "Temps de montee", "needs_translation": "false"}, "motor_startup_sound": {"english": "Motor Startup Sound", "translation": "Son demarrage moteur", "needs_translation": "false"}, "gov_integral": {"english": "Gov <PERSON><PERSON>", "translation": "Gov <PERSON><PERSON>", "needs_translation": "false"}, "gov_proportional": {"english": "Gov Proportional", "translation": "Gov Proportionnel", "needs_translation": "false"}, "cutoff_handling": {"english": "Cutoff Handling", "translation": "Gestion coupure", "needs_translation": "false"}, "bailout": {"english": "Bailout", "translation": "Bailout", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Limites", "needs_translation": "false"}, "soft_start_time": {"english": "Soft Start Time", "translation": "Temps demarrage progressif", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Avance", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "Tension BEC", "needs_translation": "false"}, "extra_msg_save": {"english": "Please reboot the ESC to apply the changes", "translation": "Veuillez redemarrer l'ESC pour appliquer les changements", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basique", "needs_translation": "false"}, "max_current": {"english": "Max <PERSON>", "translation": "Courant max", "needs_translation": "false"}, "max_temperature": {"english": "Max Temperature", "translation": "Temperature max", "needs_translation": "false"}, "protection_delay": {"english": "Protection Delay", "translation": "Delai protection", "needs_translation": "false"}, "max_used": {"english": "Max Used", "translation": "Max utilise", "needs_translation": "false"}}, "yge": {"esc_mode": {"english": "ESC Mode", "translation": "Mode ESC", "needs_translation": "false"}, "esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "current_limit": {"english": "Current Limit", "translation": "Limite courant", "needs_translation": "false"}, "f3c_auto": {"english": "F3C Autorotation", "translation": "Autorotation F3C", "needs_translation": "false"}, "name": {"english": "YGE", "translation": "YGE", "needs_translation": "false"}, "max_start_power": {"english": "Max Start Power", "translation": "Puissance max demarrage", "needs_translation": "false"}, "lv_bec_voltage": {"english": "BEC", "translation": "BEC", "needs_translation": "false"}, "pinion_teeth": {"english": "Pinion Teeth", "translation": "Dents du pignon", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Temps redemar. auto", "needs_translation": "false"}, "main_teeth": {"english": "Main Teeth", "translation": "Dents principales", "needs_translation": "false"}, "other": {"english": "Other", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Limites", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "Coupure cellule", "needs_translation": "false"}, "throttle_response": {"english": "Throttle Response", "translation": "Reponse acceleration", "needs_translation": "false"}, "stick_zero_us": {"english": "<PERSON>", "translation": "Zero stick", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Avance", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "motor_pole_pairs": {"english": "Motor Pole Pairs", "translation": "Paires de poles moteur", "needs_translation": "false"}, "stick_range_us": {"english": "Stick Range", "translation": "Plage du stick", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basique", "needs_translation": "false"}, "min_start_power": {"english": "Min Start Power", "translation": "Puissance min demarrage", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Roue libre active", "needs_translation": "false"}, "direction": {"english": "Direction", "translation": "<PERSON>s", "needs_translation": "false"}, "timing": {"english": "Motor Timing", "translation": "Timing moteur", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}}, "searching": {"english": "Searching", "translation": "Recherche", "needs_translation": "false"}}, "pids": {"help_p1": {"english": "FeedForward (Roll/Pitch): Start at 70, increase until stops are sharp with no drift. Keep roll and pitch equal.", "translation": "FeedForward (Roulis/Tangage): Commencez a 70, augmentez jusqu'a obtenir des arrets nets sans derive. Gardez les valeurs roulis et tangage egales.", "needs_translation": "false"}, "o": {"english": "O", "translation": "O", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Tangage", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p5": {"english": "Test & Adjust: Fly, observe, and fine-tune for best performance in real conditions.", "translation": "Tester & Ajuster : <PERSON><PERSON>, observez et affinez pour obtenir les meilleures performances dans des conditions reelles.", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "PIDs", "translation": "PIDs", "needs_translation": "false"}, "help_p2": {"english": "<PERSON> Gain (Roll/Pitch): Raise gradually for stable piro pitch pumps. Too high causes wobbles; match roll/pitch values.", "translation": "Gain I (Roulis/Tangage): Augmentez progressivement pour stabiliser les manoeuvres rapides en pirouettes. Trop eleve provoque des oscillations; egalisez les valeurs roulis/tangage.", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "b": {"english": "B", "translation": "B", "needs_translation": "false"}, "help_p4": {"english": "Tail Stop Gain (CW/CCW): Adjust separately for clean, bounce-free stops in both directions.", "translation": "Gain d'arret de la queue (Horaire/Anti-horaire): Ajustez separement pour des arrets nets et sans rebonds dans les deux directions.", "needs_translation": "false"}, "help_p3": {"english": "Tail P/I/D Gains: Increase P until slight wobble in funnels, then back off slightly. Raise I until tail holds firm in hard moves (too high causes slow wag). Adjust D for smooth stops—higher for slow servos, lower for fast ones.", "translation": "Gains P/I/D de queue: Augmentez P jusqu'a un leger tremblement en funnels, puis reduisez legerement. Montez I jusqu'a un maintien ferme de la queue dans les manoeuvres rapides (trop eleve provoque une oscillation lente). Ajustez D pour des arrets en douceur. Plus eleve pour les servos lents et plus faible pour des servos rapide", "needs_translation": "false"}}, "msp_speed": {"seconds_600": {"english": "  600S  ", "translation": "  600S  ", "needs_translation": "false"}, "avg_query_time": {"english": "Average query time", "translation": "Temps moyen par requete", "needs_translation": "false"}, "seconds_30": {"english": "  30S  ", "translation": "  30S  ", "needs_translation": "false"}, "name": {"english": "MSP Speed", "translation": "Vitesse MSP", "needs_translation": "false"}, "max_query_time": {"english": "Maximum query time", "translation": "Temps max. par requete", "needs_translation": "false"}, "help_p1": {"english": "This tool attempts to determine the quality of your MSP data link by performing as many large MSP queries within 30 seconds as possible.", "translation": "Cet outil mesure la qualite de votre liaison de donnees MSP en effectuant le plus possible de grandes requetes MSP en 30 secondes.", "needs_translation": "false"}, "retries": {"english": "Retries", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "checksum_errors": {"english": "Checksum errors", "translation": "Erreurs de checksum", "needs_translation": "false"}, "test_length": {"english": "Test length", "translation": "<PERSON><PERSON> du test", "needs_translation": "false"}, "start": {"english": "Start", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "memory_free": {"english": "Memory free", "translation": "Memoire disponible", "needs_translation": "false"}, "start_prompt": {"english": "Would you like to start the test? Choose the test run time below.", "translation": "Souhaitez-vous demarrer le test ? Choisissez la duree du test ci-dessous.", "needs_translation": "false"}, "rf_protocol": {"english": "RF protocol", "translation": "Protocole RF", "needs_translation": "false"}, "min_query_time": {"english": "Minimum query time", "translation": "Temps min. par requete", "needs_translation": "false"}, "seconds_120": {"english": "  120S  ", "translation": "  120S  ", "needs_translation": "false"}, "seconds_300": {"english": "  300S  ", "translation": "  300S  ", "needs_translation": "false"}, "testing": {"english": "Testing", "translation": "Test en cours", "needs_translation": "false"}, "successful_queries": {"english": "Successful queries", "translation": "<PERSON><PERSON><PERSON> reussies", "needs_translation": "false"}, "timeouts": {"english": "Timeouts", "translation": "Timeouts", "needs_translation": "false"}, "testing_performance": {"english": "Testing MSP performance...", "translation": "Test des performances MSP...", "needs_translation": "false"}, "total_queries": {"english": "Total queries", "translation": "Requetes totales", "needs_translation": "false"}}, "copyprofiles": {"profile_type": {"english": "Profile Type", "translation": "Type de profil", "needs_translation": "false"}, "profile_type_pid": {"english": "PID", "translation": "PID", "needs_translation": "false"}, "profile_type_rate": {"english": "Rate", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "msgbox_save": {"english": "Save settings", "translation": "Enregistrer les parametres", "needs_translation": "false"}, "name": {"english": "Copy Profiles", "translation": "<PERSON><PERSON><PERSON> les profils", "needs_translation": "false"}, "help_p1": {"english": "Copy PID profile or Rate profile from Source to Destination.", "translation": "Copier le profil PID ou le profil de taux depuis la source vers la destination.", "needs_translation": "false"}, "dest_profile": {"english": "Dest. Profile", "translation": "Profil destination", "needs_translation": "false"}, "source_profile": {"english": "Source Profile", "translation": "Profil source", "needs_translation": "false"}, "msgbox_msg": {"english": "Save current page to flight controller?", "translation": "Enregistrer la page actuelle sur le controleur de vol ?", "needs_translation": "false"}, "help_p2": {"english": "Choose the source and destinations and save to copy the profile.", "translation": "Choisissez la source et la destination puis enregistrez pour copier le profil.", "needs_translation": "false"}}, "esc_motors": {"min_throttle": {"english": "0% Throttle PWM Value", "translation": "Valeur PWM accel. 0%", "needs_translation": "false"}, "tail_motor_ratio": {"english": "Tail Motor Ratio", "translation": "Ratio mot. queue", "needs_translation": "false"}, "max_throttle": {"english": "100% Throttle PWM value", "translation": "Valeur PWM accel. 100%", "needs_translation": "false"}, "main_motor_ratio": {"english": "Main Motor Ratio", "translation": "Ratio mot. princ.", "needs_translation": "false"}, "pinion": {"english": "Pinion", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "main": {"english": "Main", "translation": "Principal", "needs_translation": "false"}, "help_p1": {"english": "Configure the motor and speed controller features.", "translation": "Configurer les parametres du moteur et du controleur de vitesse.", "needs_translation": "false"}, "rear": {"english": "Rear", "translation": "Arriere", "needs_translation": "false"}, "front": {"english": "Front", "translation": "Avant", "needs_translation": "false"}, "voltage_correction": {"english": "Voltage Correction", "translation": "Correction de tension", "needs_translation": "false"}, "mincommand": {"english": "Motor Stop PWM Value", "translation": "Valeur PWM arret moteur", "needs_translation": "false"}, "name": {"english": "ESC/Motors", "translation": "ESC/Moteurs", "needs_translation": "false"}, "motor_pole_count": {"english": "Motor Pole Count", "translation": "Nombre de poles moteur", "needs_translation": "false"}, "current_correction": {"english": "Current Correction", "translation": "Correction du courant", "needs_translation": "false"}, "consumption_correction": {"english": "Consumption Correction", "translation": "Correction de consommation", "needs_translation": "false"}}, "radio_config": {"deflection": {"english": "Deflection", "translation": "Deviation", "needs_translation": "false"}, "max_throttle": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "stick": {"english": "Stick", "translation": "Manche", "needs_translation": "false"}, "arming": {"english": "Arming", "translation": "Armement", "needs_translation": "false"}, "yaw_deadband": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "cyclic": {"english": "Cyclic", "translation": "Cyclique", "needs_translation": "false"}, "name": {"english": "Radio Config", "translation": "Config Radio", "needs_translation": "false"}, "help_p1": {"english": "Configure your radio settings. Stick center, arm, throttle hold, and throttle cut.", "translation": "Configurez les parametres de votre radio. Centre du manche, armement, maintien des gaz et coupure des gaz.", "needs_translation": "false"}, "min_throttle": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "throttle": {"english": "<PERSON>hrottle", "translation": "Gaz", "needs_translation": "false"}, "deadband": {"english": "Deadband", "translation": "Zone morte", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Centre", "needs_translation": "false"}}, "profile_select": {"help_p1": {"english": "Set the current flight profile or rate profile you would like to use.", "translation": "Definissez le profil de vol ou le profil de taux actuel que vous souhaitez utiliser.", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "<PERSON><PERSON> de taux", "needs_translation": "false"}, "pid_profile": {"english": "PID profile", "translation": "Profil PID", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "Enregistrer la page actuelle dans le controleur de vol ?", "needs_translation": "false"}, "save_prompt_local": {"english": "Save current page to radio?", "translation": "Enregistrer les parametres actuels?", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "ANNULER", "needs_translation": "false"}, "name": {"english": "Select Profile", "translation": "<PERSON><PERSON> le profil", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "Enregistrer les parametres", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "If you use a switch on your radio to change flight or rate modes, this will override this choice as soon as you toggle the switch.", "translation": "Si vous utilisez un interrupteur sur votre radio pour changer les modes de vol ou de taux, cela remplacera ce choix des que vous basculerez l'interrupteur.", "needs_translation": "false"}}, "profile_governor": {"tail_torque_assist": {"english": "<PERSON><PERSON>", "translation": "Assist. au Couple d'AC", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "cyc": {"english": "Cyc", "translation": "Cycl.", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Gouverneur", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "help_p1": {"english": "Full headspeed: Headspeed target when at 100% throttle input.", "translation": "Regime rotor max : Regime cible lorsque l'entree des gaz est a 100 %.", "needs_translation": "false"}, "help_p6": {"english": "Tail Torque Assist: For motorized tails. Gain and limit of headspeed increase when using main rotor torque for yaw assist.", "translation": "Assistance couple d'anticouple : Pour rotors anticouple motorises. Gain et limite de l'augmentation du regime lors de l'utilisation du couple du rotor principal pour l'assistance en lacet.", "needs_translation": "false"}, "help_p4": {"english": "Precomp: Governor precomp gain for yaw, cyclic, and collective inputs.", "translation": "Precompensation : Gain de precompensation du gouverneur pour les commandes de lacet, cyclique et collectif.", "needs_translation": "false"}, "max_throttle": {"english": "<PERSON> throttle", "translation": "Gaz max", "needs_translation": "false"}, "full_headspeed": {"english": "Full headspeed", "translation": "Regime rotor max", "needs_translation": "false"}, "precomp": {"english": "Precomp", "translation": "Precompensation", "needs_translation": "false"}, "gain": {"english": "PID master gain", "translation": "Gain PID principal", "needs_translation": "false"}, "disabled_message": {"english": "Rotorflight governor is not enabled", "translation": "Le gouverneur Rotorflight n'est pas active", "needs_translation": "false"}, "help_p3": {"english": "Gains: Fine tuning of the governor.", "translation": "Gains : Reglages fins du gouverneur.", "needs_translation": "false"}, "col": {"english": "Col", "translation": "Pas", "needs_translation": "false"}, "min_throttle": {"english": "<PERSON> throttle", "translation": "Gaz min", "needs_translation": "false"}, "tta_limit": {"english": "Limit", "translation": "Limite", "needs_translation": "false"}, "help_p2": {"english": "PID master gain: How hard the governor works to hold the RPM.", "translation": "Gain PID principal : Intensite du maintien du regime par le gouverneur.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p5": {"english": "Max throttle: The maximum throttle % the governor is allowed to use.", "translation": "Gaz max : Pourcentage maximal autorise des gaz pour le gouverneur.", "needs_translation": "false"}, "tta_gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "profile_tailrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p4": {"english": "Collective FF Gain: Tail precompensation for collective inputs.", "translation": "Gain FeedFoward collectif : Precompensation de la queue pour les entrees collectives.", "needs_translation": "false"}, "collective_impulse_ff": {"english": "Collective Impulse FF", "translation": "Impulsion collective FeedFoward", "needs_translation": "false"}, "help_p2": {"english": "Precomp Cutoff: Frequency limit for all yaw precompensation actions.", "translation": "Coupure de precompensation : Limite de frequence pour toutes les actions de precompensation de lacet.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Coupure", "needs_translation": "false"}, "help_p3": {"english": "Cyclic FF Gain: Tail precompensation for cyclic inputs.", "translation": "<PERSON><PERSON>rd cyclique : Precompensation de la queue pour les entrees cycliques.", "needs_translation": "false"}, "help_p1": {"english": "Yaw Stop Gain: Higher stop gain will make the tail stop more aggressively but may cause oscillations if too high. Adjust CW or CCW to make the yaw stops even.", "translation": "Gain d'arret de lacet : Un gain d'arret plus eleve rendra l'arret de la queue plus agressif mais peut provoquer des oscillations s'il est trop eleve. Ajustez Horaire ou Anti-Horaire pour rendre les arrets de lacet uniformes.", "needs_translation": "false"}, "inertia_precomp": {"english": "Inertia Precomp", "translation": "Precomp. d'inertie", "needs_translation": "false"}, "cyclic_ff_gain": {"english": "Cyclic FF gain", "translation": "<PERSON><PERSON> FeedFoward cyclique", "needs_translation": "false"}, "help_p5": {"english": "Collective Impulse FF: Impulse tail precompensation for collective inputs. If you need extra tail precompensation at the beginning of collective input.", "translation": "Impulsion collective FeedFoward : Precompensation d'impulsion de la queue pour les entrees collectives. Si vous avez besoin d'une precompensation supplementaire de la queue au debut de l'entree collective.", "needs_translation": "false"}, "cw": {"english": "CW", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "ccw": {"english": "CCW", "translation": "Anti-Hor.", "needs_translation": "false"}, "yaw_stop_gain": {"english": "Yaw stop gain", "translation": "<PERSON><PERSON> d'arret de lacet", "needs_translation": "false"}, "precomp_cutoff": {"english": "Precomp Cutoff", "translation": "Coupure de precompensation", "needs_translation": "false"}, "collective_ff_gain": {"english": "Collective FF gain", "translation": "Gain FeedFoward collectif", "needs_translation": "false"}, "name": {"english": "Tail Rotor", "translation": "Rotor de queue", "needs_translation": "false"}, "decay": {"english": "Decay", "translation": "Decroissance", "needs_translation": "false"}}, "profile_pidcontroller": {"help_p4": {"english": "Error rotation: Allow errors to be shared between all axes.", "translation": "Rotation d'erreur : Permettre aux erreurs d'etre partagees entre tous les axes.", "needs_translation": "false"}, "ground_error_decay": {"english": "Ground Error Decay", "translation": "Decrois. d'erreur au sol", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Lct", "needs_translation": "false"}, "inflight_error_decay": {"english": "Inflight Error Decay", "translation": "Decrois. d'erreur en vol", "needs_translation": "false"}, "help_p2": {"english": "Error limit: Angle limit for I-term.", "translation": "Limite d'erreur : Limite d'angle pour I-term.", "needs_translation": "false"}, "error_limit": {"english": "Error limit", "translation": "<PERSON>ite d'erreur", "needs_translation": "false"}, "help_p3": {"english": "Offset limit: Angle limit for High Speed Integral (O-term).", "translation": "Limite de decalage : Limite d'angle pour l'integrale a haute vitesse (O-term).", "needs_translation": "false"}, "cutoff_point": {"english": "Cut-off point", "translation": "Point de coupure", "needs_translation": "false"}, "limit": {"english": "Limit", "translation": "Lmt", "needs_translation": "false"}, "iterm_relax": {"english": "I-term relax", "translation": "Relaxation I-term", "needs_translation": "false"}, "hsi_offset_limit": {"english": "HSI Offset limit", "translation": "Limite de decalage HSI", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "Tng", "needs_translation": "false"}, "name": {"english": "PID Controller", "translation": "Controleur PID", "needs_translation": "false"}, "error_rotation": {"english": "Error rotation", "translation": "Rotation d'erreur", "needs_translation": "false"}, "roll": {"english": "R", "translation": "Rol", "needs_translation": "false"}, "help_p5": {"english": "I-term relax: Limit accumulation of I-term during fast movements - helps reduce bounce back after fast stick movements. Generally needs to be lower for large helis and can be higher for small helis. Best to only reduce as much as is needed for your flying style.", "translation": "Relaxation I-term : Limiter l'accumulation de I-term lors des mouvements rapides - aide a reduire le rebond apres des mouvements rapides du manche. Doit generalement etre plus faible pour les grands helicopteres et peut etre plus eleve pour les petits helicopteres. Il est preferable de ne reduire que ce qui est necessaire pour votre style de vol.", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Tmp", "needs_translation": "false"}, "help_p1": {"english": "Error decay ground: PID decay to help prevent heli from tipping over when on the ground.", "translation": "Decroissance d'erreur au sol : Decroissance PID pour aider a empecher l'helicoptere de basculer lorsqu'il est au sol.", "needs_translation": "false"}}, "logs": {"help_logs_p2": {"english": "Note. To enable logging it is essential for you to have the following sensors enabled.", "translation": "Les journaux necessitent une analyse reguliere pour surveiller le comportement de vol.", "needs_translation": "false"}, "name": {"english": "Logs", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_logs_p1": {"english": "Please select a log file from the list below.", "translation": "Veuillez selectionner un fichier journal dans la liste ci-dessous.", "needs_translation": "false"}, "msg_no_logs_found": {"english": "NO LOG FILES FOUND", "translation": "AUCUN FICHIER JOURNAL TROUVE", "needs_translation": "false"}, "help_logs_tool_p1": {"english": "Please use the slider to navigate the graph.", "translation": "Veuillez utiliser le curseur pour naviguer dans le graphique.", "needs_translation": "false"}, "help_logs_p3": {"english": "- arm status, voltage, headspeed, current, esc temperature", "translation": "- Status Actuel d'Armement, Tension Actuelle, Tours Moteurs, Courant Amp, Temperature ESC", "needs_translation": "false"}}, "battery": {"calcfuel_local": {"english": "Calculate Fuel Using", "translation": "Calculate fuel using", "needs_translation": "true"}, "max_cell_voltage": {"english": "Max Cell Voltage", "translation": "Tension max par cellule", "needs_translation": "false"}, "full_cell_voltage": {"english": "Full Cell Voltage", "translation": "Tension pleine de cellule", "needs_translation": "false"}, "name": {"english": "Battery", "translation": "<PERSON><PERSON>ie", "needs_translation": "false"}, "min_cell_voltage": {"english": "Min Cell Voltage", "translation": "Tension min cellule", "needs_translation": "false"}, "help_p1": {"english": "The battery settings are used to configure the flight controller to monitor the battery voltage and provide warnings when the voltage drops below a certain level.", "translation": "Les parametres de la batterie sont utilises pour configurer le controleur de vol afin de surveiller la tension de la batterie et fournir des avertissements lorsque la tension descend en dessous d'un certain niveau.", "needs_translation": "false"}, "battery_capacity": {"english": "Battery Capacity", "translation": "Capacite de la batterie", "needs_translation": "false"}, "warn_cell_voltage": {"english": "Warn Cell Voltage", "translation": "Tension d'alerte de cellule", "needs_translation": "false"}, "cell_count": {"english": "Cell Count", "translation": "Nombre de cellules", "needs_translation": "false"}, "consumption_warning_percentage": {"english": "Consumption Warning %", "translation": "Avert. conso %", "needs_translation": "false"}, "timer": {"english": "Flight Time Alarm", "translation": "Temps de vol", "needs_translation": "false"}, "voltage_multiplier": {"english": "Sag Compensation", "translation": "Compensation de chute", "needs_translation": "false"}, "kalman_multiplier": {"english": "Filter Compensation", "translation": "Filter compensation", "needs_translation": "true"}, "alert_type": {"english": "Rx Voltage Alert", "translation": "BEC or Rx Batt Voltage Alert", "needs_translation": "true"}, "bec_voltage_alert": {"english": "BEC Alert Value", "translation": "BEC Alert Value", "needs_translation": "true"}, "rx_voltage_alert": {"english": "RxBatt Alert Value", "translation": "RX Batt Alert Value", "needs_translation": "true"}}, "profile_mainrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p4": {"english": "Cross Coupling Freq. Limit: Frequency limit for the compensation, higher value will make the compensation action faster.", "translation": "Frequence limite couplage croise : Limite frequentielle de la compensation, une valeur plus elevee accelere l'action compensatrice.", "needs_translation": "false"}, "collective_pitch_comp_short": {"english": "Col. Pitch Compensation", "translation": "Comp. pas collectif", "needs_translation": "false"}, "cyclic_cross_coupling": {"english": "Cyclic Cross coupling", "translation": "Couplage cyclique croise", "needs_translation": "false"}, "collective_pitch_comp": {"english": "Collective Pitch Compensation", "translation": "Compensation pas collectif", "needs_translation": "false"}, "name": {"english": "Main Rotor", "translation": "Rotor principal", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Freq. limite", "needs_translation": "false"}, "ratio": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Collective Pitch Compensation: Increasing will compensate for the pitching motion caused by tail drag when climbing.", "translation": "Compensation pas collectif : Une valeur plus elevee compense mieux le mouvement de tangage dû a la traînee de l'anticouple lors des montees.", "needs_translation": "false"}, "help_p2": {"english": "Cross Coupling Gain: Removes roll coupling when only elevator is applied.", "translation": "Gain couplage croise : <PERSON>uit le couplage en roulis lorsque seule la profondeur est utilisee.", "needs_translation": "false"}, "help_p3": {"english": "Cross Coupling Ratio: Amount of compensation (pitch vs roll) to apply.", "translation": "Ratio couplage croise : Quantite de compensation (tangage vs roulis) appliquee.", "needs_translation": "false"}}, "sbusout": {"title": {"english": "SBUS Output", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_fields_source": {"english": "Source id for the mix, counting from 0-15.", "translation": "ID de la source pour le mixage, comptant de 0 a 15.", "needs_translation": "false"}, "help_default_p4": {"english": "- For motors, use 0, 1000.", "translation": "- Pour les moteurs, utilisez 0, 1000.", "needs_translation": "false"}, "ch_prefix": {"english": "CH", "translation": "CH", "needs_translation": "false"}, "channel_prefix": {"english": "CHANNEL ", "translation": "CANAL ", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "Enregistrement", "needs_translation": "false"}, "name": {"english": "SBUS Out", "translation": "SBUS Sortie", "needs_translation": "false"}, "channel_page": {"english": "Sbus out / CH", "translation": "Sbus sortie / CH", "needs_translation": "false"}, "receiver": {"english": "Receiver", "translation": "Recepteur", "needs_translation": "false"}, "servo": {"english": "Servo", "translation": "Servo", "needs_translation": "false"}, "type": {"english": "Type", "translation": "Type", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "Enregistrement des donnees...", "needs_translation": "false"}, "help_fields_max": {"english": "The maximum pwm value to send", "translation": "La valeur PWM maximale a envoyer", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_default_p5": {"english": "- Or you can customize your own mapping.", "translation": "- Ou vous pouvez personnaliser votre propre mappage.", "needs_translation": "false"}, "help_default_p1": {"english": "Configure advanced mixing and channel mapping if you have SBUS Out enabled on a serial port.", "translation": "Configurez le mixage avance et le mappage des canaux si vous avez active SBUS Out sur un port serie.", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "Enregistrer la page actuelle dans le controleur de vol ?", "needs_translation": "false"}, "help_fields_min": {"english": "The minimum pwm value to send.", "translation": "La valeur PWM minimale a envoyer.", "needs_translation": "false"}, "mixer": {"english": "Mixer", "translation": "Mixeur", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "ANNULER", "needs_translation": "false"}, "help_default_p2": {"english": "- For RX channels or servos (wideband), use 1000, 2000 or 500,1000 for narrow band servos.", "translation": "- Pour les canaux RX ou les servos (large bande), utilisez 1000, 2000 ou 500,1000 pour les servos a bande etroite.", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "Enregistrer les parametres", "needs_translation": "false"}, "min": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "help_default_p3": {"english": "- For mixer rules, use -1000, 1000.", "translation": "- Pour les regles de mixage, utilisez -1000, 1000.", "needs_translation": "false"}, "source": {"english": "Source", "translation": "Source", "needs_translation": "false"}}, "profile_rescue": {"help_p4": {"english": "Hover: How much collective to maintain a steady hover.", "translation": "Stationnaire : Combien de collectif pour maintenir un vol stationnaire stable.", "needs_translation": "false"}, "hover": {"english": "Hover", "translation": "Stationnaire", "needs_translation": "false"}, "collective": {"english": "Collective", "translation": "Collectif", "needs_translation": "false"}, "help_p2": {"english": "Pull-up: How much collective and for how long to arrest the fall.", "translation": "Redresser : Combien de collectif et pendant combien de temps pour arreter la chute.", "needs_translation": "false"}, "climb": {"english": "Climb", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "mode_enable": {"english": "Rescue mode enable", "translation": "Activer le mode secours", "needs_translation": "false"}, "help_p3": {"english": "Climb: How much collective to maintain a steady climb - and how long.", "translation": "<PERSON><PERSON> : Combien de collectif pour maintenir une montee reguliere - et combien de temps.", "needs_translation": "false"}, "help_p1": {"english": "Flip to upright: Flip the heli upright when rescue is activated.", "translation": "Retourner a l'endroit : Retourner l'helicoptere a l'endroit lorsque le mode secours est active.", "needs_translation": "false"}, "flip_upright": {"english": "Flip to upright", "translation": "Retourner a l'endroit", "needs_translation": "false"}, "flip": {"english": "Flip", "translation": "<PERSON><PERSON>.", "needs_translation": "false"}, "level_gain": {"english": "Level", "translation": "Niveau", "needs_translation": "false"}, "name": {"english": "Rescue", "translation": "Secours", "needs_translation": "false"}, "exit_time": {"english": "Exit time", "translation": "Transit.", "needs_translation": "false"}, "help_p5": {"english": "Flip: How long to wait before aborting because the flip did not work.", "translation": "Retourner : <PERSON><PERSON><PERSON> de temps attendre avant d'abandonner parce que le retournement n'a pas fonctionne.", "needs_translation": "false"}, "help_p6": {"english": "Gains: How hard to fight to keep heli level when engaging rescue mode.", "translation": "Gains : Combien d'effort pour maintenir l'helicoptere a niveau lors de l'activation du mode secours.", "needs_translation": "false"}, "fail_time": {"english": "Fail time", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "pull_up": {"english": "Pull-up", "translation": "Redresser", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p7": {"english": "Rate and Accel: Max rotation and acceleration rates when leveling during rescue.", "translation": "Taux et Acceleration : Taux de rotation et d'acceleration maximum lors de la mise a niveau pendant le secours.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Temps", "needs_translation": "false"}, "accel": {"english": "Accel", "translation": "Acceleration", "needs_translation": "false"}}, "trim": {"disable_mixer_message": {"english": "Return control of the servos to the flight controller.", "translation": "Rendre le controle des servos au controleur de vol.", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Motor idle  %", "translation": "Ralenti moteur de queue %", "needs_translation": "false"}, "disable_mixer_override": {"english": "Disable mixer override", "translation": "<PERSON><PERSON><PERSON> le remplacement du mixeur", "needs_translation": "false"}, "yaw_trim": {"english": "Yaw. trim %", "translation": "Trim lacet %", "needs_translation": "false"}, "enable_mixer_message": {"english": "Set all servos to their configured center position. \r\n\r\nThis will result in all values on this page being saved when adjusting the servo trim.", "translation": "<PERSON>lez tous les servos a leur position centrale configuree. \r\n\r\n <PERSON><PERSON> entrainera l'enregistrement de toutes les valeurs de cette page lors du reglage du trim du servo.", "needs_translation": "false"}, "mixer_override_disabling": {"english": "Disabling mixer override...", "translation": "Desactivation du remplacement du mixeur...", "needs_translation": "false"}, "roll_trim": {"english": "Roll trim %", "translation": "Trim roulis %", "needs_translation": "false"}, "pitch_trim": {"english": "Pitch trim %", "translation": "Trim tangage %", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Motorised tail: If using a motorised tail, use this to set the minimum idle speed and zero yaw.", "translation": "Queue motorisee : Si vous utilisez une queue motorisee, utilisez ceci pour regler la vitesse minimale de ralenti et le zero lacet.", "needs_translation": "false"}, "mixer_override": {"english": "Mixer Override", "translation": "Remplacement mixeur", "needs_translation": "false"}, "mixer_override_enabling": {"english": "Enabling mixer override...", "translation": "Activation du remplacement du mixeur...", "needs_translation": "false"}, "enable_mixer_override": {"english": "Enable mixer override", "translation": "<PERSON><PERSON> le remplacement du mixeur", "needs_translation": "false"}, "collective_trim": {"english": "Col. trim %", "translation": "Trim collectif %", "needs_translation": "false"}, "help_p1": {"english": "Link trims: Use to trim out small leveling issues in your swash plate. Typically only used if the swash links are non-adjustable.", "translation": "Lier les trims : Utilisez pour ajuster les petits problemes de nivellement dans votre plateau cyclique. A utilise uniquement si les liens du plateau cyclique ne sont pas ajustables.", "needs_translation": "false"}}, "governor": {"help_p1": {"english": "These parameters apply globally to the governor regardless of the profile in use.", "translation": "Ces parametres s'appliquent globalement au gouverneur, quel que soit le profil utilise.", "needs_translation": "false"}, "handover_throttle": {"english": "Handover throttle%", "translation": "Transfert des gaz %", "needs_translation": "false"}, "spoolup_min_throttle": {"english": "Spoolup min throttle%", "translation": "Gaz min. de demarrage %", "needs_translation": "false"}, "recovery_time": {"english": "Recovery time", "translation": "Temps de recuperation", "needs_translation": "false"}, "mode": {"english": "Mode", "translation": "Mode", "needs_translation": "false"}, "help_p2": {"english": "Each parameter is simply a time value in seconds for each governor action.", "translation": "Chaque paramtre est simplement une valeur de temps en secondes pour chaque action du gouverneur.", "needs_translation": "false"}, "tracking_time": {"english": "Tracking time", "translation": "Temps de suivi", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Gouverneur", "needs_translation": "false"}, "startup_time": {"english": "Startup time", "translation": "Temps de demarrage", "needs_translation": "false"}, "spoolup_time": {"english": "Spoolup time", "translation": "Temps de monte en regime", "needs_translation": "false"}}, "accelerometer": {"help_p1": {"english": "The accelerometer is used to measure the angle of the flight controller in relation to the horizon. This data is used to stabilize the aircraft and provide self-leveling functionality.", "translation": "L'accelerometre est utilise pour mesurer l'angle du controleur de vol par rapport a l'horizon. Ces donnees sont utilisees pour stabiliser l'aeronef et fournir une fonctionnalite d'auto-nivellement.", "needs_translation": "false"}, "name": {"english": "Accelerometer", "translation": "Accelerometre", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Tangage", "needs_translation": "false"}, "msg_calibrate": {"english": "Calibrate the accelerometer?", "translation": "Calibrer l'accelerometre ?", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}}, "rates": {"help_table_5_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Taux Max : Taux de rotation maximal a pleine deviation du manche en degres par seconde.", "needs_translation": "false"}, "actual": {"english": "ACTUAL", "translation": "ACTUEL", "needs_translation": "false"}, "max_rate": {"english": "Max Rate", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_table_4_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo : Reduit la sensibilite pres du centre du manche ou des controles fins sont necessaires.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_table_5_p1": {"english": "RC Rate: Use to reduce sensitivity around center stick. RC Rate set to one half of the Max Rate is linear. A lower number will reduce sensitivity around center stick. Higher than one half of the Max Rate will also increase the Max Rate.", "translation": "Taux RC : Utilisez pour reduire la sensibilite autour du centre du manche. Le Taux RC regle a la moitie du Taux Max est lineaire. Un nombre inferieur reduira la sensibilite autour du centre du manche. Superieur a la moitie du Taux Max augmentera egalement le Taux Max.", "needs_translation": "false"}, "help_table_4_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Taux Max : Taux de rotation maximal a pleine deviation du manche en degres par seconde.", "needs_translation": "false"}, "center_sensitivity": {"english": "Cntr. Sens.", "translation": "Sens. Centre", "needs_translation": "false"}, "rc_curve": {"english": "RC Curve", "translation": "Courbe RC", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "none": {"english": "NONE", "translation": "AUCUN", "needs_translation": "false"}, "betaflight": {"english": "BETAFLIGHT", "translation": "BETAFLIGHT", "needs_translation": "false"}, "kiss": {"english": "KISS", "translation": "KISS", "needs_translation": "false"}, "help_table_1_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo : Reduit la sensibilite pres du centre du manche ou des controles fins sont necessaires.", "needs_translation": "false"}, "help_table_3_p2": {"english": "Rate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "Taux : Augmente le taux de rotation maximal tout en reduisant la sensibilite autour de la moitie du manche.", "needs_translation": "false"}, "help_table_2_p2": {"english": "Acro+: Increases the maximum rotation rate while reducing sensitivity around half stick.", "translation": "Acro+ : Augmente le taux de rotation maximal tout en reduisant la sensibilite autour de la moitie du manche.", "needs_translation": "false"}, "superrate": {"english": "SuperRate", "translation": "SuperTaux", "needs_translation": "false"}, "help_table_2_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo : Reduit la sensibilite pres du centre du manche ou des controles fins sont necessaires.", "needs_translation": "false"}, "raceflight": {"english": "RACEFLIGHT", "translation": "RACEFLIGHT", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "collective": {"english": "Col", "translation": "Collectif", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_table_5_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo : Reduit la sensibilite pres du centre du manche ou des controles fins sont necessaires.", "needs_translation": "false"}, "help_table_3_p3": {"english": "RC Curve: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Courbe RC : Reduit la sensibilite pres du centre du manche ou des controles fins sont necessaires.", "needs_translation": "false"}, "expo": {"english": "Expo", "translation": "Expo", "needs_translation": "false"}, "help_table_1_p2": {"english": "SuperRate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "SuperTaux : Augmente le taux de rotation maximal tout en reduisant la sensibilite autour de la moitie du manche.", "needs_translation": "false"}, "help_default_p2": {"english": "We will use the sub keys below.", "translation": "Nous utiliserons les sous-cles ci-dessous.", "needs_translation": "false"}, "help_default_p1": {"english": "Default: We keep this to make button appear for rates.", "translation": "Par defaut : Nous gardons cela pour faire apparaitre le bouton pour les taux.", "needs_translation": "false"}, "quick": {"english": "QUICK", "translation": "RAPIDE", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Tangage", "needs_translation": "false"}, "acroplus": {"english": "Acro+", "translation": "Acro+", "needs_translation": "false"}, "help_table_1_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "Taux RC : Taux de rotation maximal a pleine deviation du manche.", "needs_translation": "false"}, "rc_rate": {"english": "RC Rate", "translation": "Taux RC", "needs_translation": "false"}, "help_table_2_p1": {"english": "Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Taux : Taux de rotation maximal a pleine deviation du manche en degres par seconde.", "needs_translation": "false"}, "help_table_4_p1": {"english": "Center Sensitivity: Use to reduce sensitivity around center stick. Set Center Sensitivity to the same as Max Rate for a linear response. A lower number than Max Rate will reduce sensitivity around center stick. Note that higher than Max Rate will increase the Max Rate - not recommended as it causes issues in the Blackbox log.", "translation": "Sensibilite Centre : Utilisez pour reduire la sensibilite autour du centre du manche. Reglez la Sensibilite Centre a la meme valeur que le Taux Max pour une reponse lineaire. Un nombre inferieur au Taux Max reduira la sensibilite autour du centre du manche. Notez qu'un nombre superieur au Taux Max augmentera le Taux Max - non recommande car cela cause des problemes dans le journal Blackbox.", "needs_translation": "false"}, "help_table_0_p1": {"english": "All values are set to zero because no RATE TABLE is in use.", "translation": "Toutes les valeurs sont definies a zero car aucune TABLE DE TAUX n'est utilisee.", "needs_translation": "false"}, "help_table_3_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "Taux RC : Taux de rotation maximal a pleine deviation du manche.", "needs_translation": "false"}}, "mixer": {"help_p1": {"english": "Adust swash plate geometry, phase angles, and limits.", "translation": "Ajustez la geometrie du plateau cyclique, les angles de phase et les limites. TTA : Precompensation pour l'aide a l'anticouple", "needs_translation": "false"}, "collective_tilt_correction_pos": {"english": "Positive", "translation": "Positive", "needs_translation": "false"}, "geo_correction": {"english": "Geo Correction", "translation": "Correction Geometrique", "needs_translation": "false"}, "swash_tta_precomp": {"english": "TTA Precomp", "translation": "Precomp. TTA", "needs_translation": "false"}, "name": {"english": "Mixer", "translation": "Mixeur", "needs_translation": "false"}, "collective_tilt_correction_neg": {"english": "Negative", "translation": "Negative", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Idle Thr%", "translation": "Ralenti Queue %", "needs_translation": "false"}, "swash_phase": {"english": "Phase Angle", "translation": "Angle de Phase", "needs_translation": "false"}, "collective_tilt_correction": {"english": "Collective Tilt Correction", "translation": "Correction Inclinaison Collective", "needs_translation": "false"}, "swash_pitch_limit": {"english": "Total Pitch Limit", "translation": "Limite Totale du Pas", "needs_translation": "false"}}, "about": {"help_p1": {"english": "This page provides some useful information that you may be asked for when requesting support.", "translation": "Cette page fournit des informations utiles que vous pourriez devoir fournir lors d'une demande d'assistance.", "needs_translation": "false"}, "msgbox_credits": {"english": "Credits", "translation": "Credits", "needs_translation": "false"}, "ethos_version": {"english": "Ethos Version", "translation": "Version Ethos", "needs_translation": "false"}, "rf_version": {"english": "Rotorflight Version", "translation": "Version Rotorflight", "needs_translation": "false"}, "fc_version": {"english": "FC Version", "translation": "Version FC", "needs_translation": "false"}, "name": {"english": "About", "translation": "A propos", "needs_translation": "false"}, "supported_versions": {"english": "Supported MSP Versions", "translation": "Versions MSP supportes", "needs_translation": "false"}, "license": {"english": "You may copy, distribute, and modify the software as long as you track changes/dates in source files. Any modifications to or software including (via compiler) GPL-licensed code must also be made available under the GPL along with build & install instructions.", "translation": "Vous pouvez copier, distribuer et modifier le logiciel tant que vous suivez les modifications et les dates dans les fichiers sources. Toute modification ou tout logiciel incluant du code sous licence GPL (via le compilateur) doit egalement etre mis a disposition sous la GPL avec les instructions de compilation et d'installation.", "needs_translation": "false"}, "simulation": {"english": "Simulation", "translation": "Simulation", "needs_translation": "false"}, "help_p2": {"english": "For support, please first read the help pages on www.rotorflight.org", "translation": "Pour toute assistance, veuillez d'abord lire les pages d'aide sur www.rotorflight.org", "needs_translation": "false"}, "opener": {"english": "Rotorflight is an open source project. Contribution from other like minded people, keen to assist in making this software even better, is welcomed and encouraged. You do not have to be a hardcore programmer to help.", "translation": "Rotorflight est un projet open source. Toute contribution de personnes partageant le meme etat d'esprit, desireuses d'ameliorer encore ce logiciel, est la bienvenue et encouragee. Vous n'avez pas besoin d'etre un programmeur experimente pour aider.", "needs_translation": "false"}, "version": {"english": "Version", "translation": "Version", "needs_translation": "false"}, "msp_version": {"english": "MSP Version", "translation": "Version MSP", "needs_translation": "false"}, "credits": {"english": "Notable contributors to both the Rotorflight firmware and this software are: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... and many more who have spent hours testing and providing feedback!", "translation": "Les contributeurs notables au firmware Rotorflight et a ce logiciel sont : <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... et bien d'autres qui ont passe des heures a tester et fournir des retours !", "needs_translation": "false"}, "msp_transport": {"english": "MSP Transport", "translation": "Transport MSP", "needs_translation": "false"}}, "rates_advanced": {"dyn_ceiling_gain": {"english": "Dynamic ceiling gain", "translation": "Gain plafond dynamique", "needs_translation": "false"}, "acc_limit": {"english": "Accelerometer Limit", "translation": "Limite d'acceleration", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "yaw_dynamics": {"english": "Yaw dynamics", "translation": "Dynamique de <PERSON>", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Tangage", "needs_translation": "false"}, "col": {"english": "Col", "translation": "Collectif", "needs_translation": "false"}, "setpoint_boost_cutoff": {"english": "Setpoint boost cutoff", "translation": "<PERSON><PERSON> consigne", "needs_translation": "false"}, "yaw_dynamic_deadband_gain": {"english": "D. <PERSON>", "translation": "<PERSON><PERSON> morte", "needs_translation": "false"}, "rates_type": {"english": "Rates Type", "translation": "Type de taux", "needs_translation": "false"}, "setpoint_boost_gain": {"english": "Setpoint boost gain", "translation": "<PERSON><PERSON> consigne", "needs_translation": "false"}, "msg_reset_to_defaults": {"english": "Rate type changed. Values will be reset to defaults.", "translation": "Type de taux change. Les valeurs seront reinitialisees par defaut.", "needs_translation": "false"}, "yaw_dynamic_ceiling_gain": {"english": "Ceiling", "translation": "Plafond", "needs_translation": "false"}, "yaw_boost": {"english": "Yaw boost", "translation": "<PERSON><PERSON> de <PERSON>", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "rate_table": {"english": "Rate Table", "translation": "Table des vitesses", "needs_translation": "false"}, "dynamics": {"english": "Dynamics", "translation": "Dynamique", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "yaw_dynamic_deadband_filter": {"english": "Filter", "translation": "Filtre", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_rate_table": {"english": "Please select the rate you would like to use. Saving will apply the choice to the active profile.", "translation": "Selectionnez la vitesse desiree. L'enregistrement applique ce choix au profil actif.", "needs_translation": "false"}, "help_p1": {"english": "Rates type: Choose the rate type you prefer flying with. Raceflight and Actual are the most straightforward.", "translation": "Type de taux : <PERSON><PERSON><PERSON><PERSON> le type de taux avec lequel vous preferez voler. Raceflight et Actual sont les plus simples.", "needs_translation": "false"}, "pitch_boost": {"english": "Pitch boost", "translation": "Boost de tangage", "needs_translation": "false"}, "help_p2": {"english": "Dynamics: Applied regardless of rates type. Typically left on defaults but can be adjusted to smooth heli movements, like with scale helis.", "translation": "Dynamique : Applique independamment du type de taux. Typiquement laisse par defaut mais peut etre ajuste pour lisser les mouvements de l'helicoptere, comme avec les helicopteres de maquette.", "needs_translation": "false"}, "accel_limit": {"english": "Accel", "translation": "Accel", "needs_translation": "false"}, "dyn_deadband_filter": {"english": "Dynamic deadband filter", "translation": "Filtre zone morte dynamique", "needs_translation": "false"}, "roll_boost": {"english": "Roll boost", "translation": "Boost <PERSON>", "needs_translation": "false"}, "dyn_deadband_gain": {"english": "Dynamic deadband gain", "translation": "Gain zone morte dynamique", "needs_translation": "false"}, "collective_dynamics": {"english": "Collective dynamics", "translation": "Dynamique de pas", "needs_translation": "false"}, "roll_dynamics": {"english": "Roll dynamics", "translation": "Dynamique de roulis", "needs_translation": "false"}, "collective_boost": {"english": "Collective boost", "translation": "Boost collectif", "needs_translation": "false"}, "pitch_dynamics": {"english": "Pitch dynamics", "translation": "Dynamique de prof.", "needs_translation": "false"}, "response_time": {"english": "Response Time", "translation": "Temps de reponse", "needs_translation": "false"}}, "servos": {"tbl_yes": {"english": "YES", "translation": "OUI", "needs_translation": "false"}, "enable_servo_override": {"english": "Enable servo override", "translation": "Activer remplacement servo", "needs_translation": "false"}, "disabling_servo_override": {"english": "Disabling servo override...", "translation": "Desactivation du remplacement servo...", "needs_translation": "false"}, "help_tool_p3": {"english": "Minimum/Maximum: Adjust the end points of the selected servo.", "translation": "Minimum/Maximum : Ajuster les points de fin du servo selectionne.", "needs_translation": "false"}, "tail": {"english": "TAIL", "translation": "QUEUE", "needs_translation": "false"}, "scale_negative": {"english": "Scale Negative", "translation": "Echelle Negative", "needs_translation": "false"}, "help_tool_p1": {"english": "Override: [*] Enable override to allow real-time updates of servo center point.", "translation": "Remplacement : [*] Activer le remplacement pour permettre les mises a jour en temps reel du point central du servo.", "needs_translation": "false"}, "tbl_no": {"english": "NO", "translation": "NON", "needs_translation": "false"}, "maximum": {"english": "Maximum", "translation": "Maximum", "needs_translation": "false"}, "help_tool_p6": {"english": "Speed: The speed the servo moves. Generally only used for the cyclic servos to help the swash move evenly. Optional - leave all at 0 if unsure.", "translation": "Vitesse : La vitesse de deplacement du servo. Generalement utilise uniquement pour les servos cycliques pour aider le plateau cyclique a se deplacer uniformement. Optionnel - laisser tous a 0 si incertain.", "needs_translation": "false"}, "help_fields_rate": {"english": "Servo PWM rate.", "translation": "Taux PWM du servo.", "needs_translation": "false"}, "cyc_pitch": {"english": "CYC.PITCH", "translation": "CYC. TANGAGE", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Centre", "needs_translation": "false"}, "minimum": {"english": "Minimum", "translation": "Minimum", "needs_translation": "false"}, "speed": {"english": "Speed", "translation": "Vitesse", "needs_translation": "false"}, "help_fields_speed": {"english": "Servo motion speed in milliseconds.", "translation": "Vitesse de mouvement du servo en millisecondes.", "needs_translation": "false"}, "disable_servo_override": {"english": "Disable servo override", "translation": "Desactiver remplacement servo", "needs_translation": "false"}, "help_fields_scale_pos": {"english": "Servo positive scaling.", "translation": "<PERSON><PERSON><PERSON> positive du servo.", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "Enregistrement des donnees...", "needs_translation": "false"}, "cyc_left": {"english": "CYC.LEFT", "translation": "CYC. GAUCHE", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "Enregistrement", "needs_translation": "false"}, "name": {"english": "Servos", "translation": "Servos", "needs_translation": "false"}, "help_tool_p5": {"english": "Rate: The frequency the servo runs best at - check with manufacturer.", "translation": "Taux : La frequence a laquelle le servo fonctionne le mieux - verifier avec le fabricant.", "needs_translation": "false"}, "help_tool_p2": {"english": "Center: Adjust the center position of the servo.", "translation": "Centre : Ajuster la position centrale du servo.", "needs_translation": "false"}, "enabling_servo_override": {"english": "Enabling servo override...", "translation": "Activation du remplacement servo...", "needs_translation": "false"}, "servo_prefix": {"english": "SERVO ", "translation": "SERVO ", "needs_translation": "false"}, "reverse": {"english": "Reverse", "translation": "Inversement", "needs_translation": "false"}, "enable_servo_override_msg": {"english": "Servo override allows you to 'trim' your servo center point in real time.", "translation": "Le remplacement du servo vous permet de 'regler' le point central de votre servo en temps reel.", "needs_translation": "false"}, "cyc_right": {"english": "CYC.RIGHT", "translation": "CYC. DROITE", "needs_translation": "false"}, "help_default_p2": {"english": "Primary flight controls that use the rotorflight mixer will display in the section called 'mixer'.", "translation": "Les commandes de vol principales qui utilisent le mixeur rotoflight s'afficheront dans la section appelee 'mixeur'.", "needs_translation": "false"}, "scale_positive": {"english": "Scale Positive", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_default_p1": {"english": "Please select the servo you would like to configure from the list below.", "translation": "Veuillez selectionner le servo que vous souhaitez configurer dans la liste ci-dessous.", "needs_translation": "false"}, "servo_override": {"english": "Servo Override", "translation": "Remplacement Servo", "needs_translation": "false"}, "disable_servo_override_msg": {"english": "Return control of the servos to the flight controller.", "translation": "Rendre le controle des servos au controleur de vol.", "needs_translation": "false"}, "help_fields_min": {"english": "Servo negative travel limit.", "translation": "Limite de deplacement negatif du servo.", "needs_translation": "false"}, "help_default_p3": {"english": "Any other servos that are not controlled by the primary flight mixer will be displayed in the section called 'Other servos'.", "translation": "Tous les autres servos qui ne sont pas controles par le mixeur de vol principal s'afficheront dans la section appelee 'Autres servos'.", "needs_translation": "false"}, "help_fields_mid": {"english": "Servo center position pulse width.", "translation": "Largeur d'impulsion de la position centrale du servo.", "needs_translation": "false"}, "help_fields_scale_neg": {"english": "Servo negative scaling.", "translation": "<PERSON><PERSON>le negative du servo.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_tool_p4": {"english": "Scale: Adjust the amount the servo moves for a given input.", "translation": "Echelle : Ajuster la quantite de mouvement du servo pour une entree donnee.", "needs_translation": "false"}, "help_fields_flags": {"english": "0 = <PERSON><PERSON><PERSON>, 1=Reverse, 2 = Geo Correction, 3 = Reverse + Geo Correction", "translation": "0 = <PERSON><PERSON> defaut, 1=Inverser, 2 = Correction Geometrique, 3 = Inverser + Correction Geometrique", "needs_translation": "false"}, "geometry": {"english": "Geometry", "translation": "Geometrie", "needs_translation": "false"}, "help_fields_max": {"english": "Servo positive travel limit.", "translation": "Limite de deplacement positif du servo.", "needs_translation": "false"}}, "profile_autolevel": {"acro_trainer": {"english": "Acro trainer", "translation": "Entrainement Acro", "needs_translation": "false"}, "angle_mode": {"english": "Angle mode", "translation": "Mode Angle", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "name": {"english": "Autolevel", "translation": "Auto-niveau", "needs_translation": "false"}, "help_p1": {"english": "Acro Trainer: How aggressively the heli tilts back to level when flying in Acro Trainer Mode.", "translation": "Entrainement Acro : Intensite avec laquelle l'helicoptere revient a l'horizontale en mode entrainement Acro.", "needs_translation": "false"}, "horizon_mode": {"english": "Horizon mode", "translation": "Mode Horizon", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Angle Mode: How aggressively the heli tilts back to level when flying in Angle Mode.", "translation": "Mode Angle : Intensite avec laquelle l'helicoptere revient a l'horizontale en mode Angle.", "needs_translation": "false"}, "help_p3": {"english": "Horizon Mode: How aggressively the heli tilts back to level when flying in Horizon Mode.", "translation": "Mode Horizon : Intensite avec laquelle l'helicoptere revient a l'horizontale en mode Horizon.", "needs_translation": "false"}}, "filters": {"filter_type": {"english": "Filter type", "translation": "Type de filtre", "needs_translation": "false"}, "help_p4": {"english": "Dynamic Notch Filters: Automatically creates notch filters within the min and max frequency range.", "translation": "Les Filtreurs elimine une plage de frequences specifiques.", "needs_translation": "false"}, "notch_c": {"english": "Notch Count", "translation": "Nbrs filtres", "needs_translation": "false"}, "rpm_preset": {"english": "Type", "translation": "Type", "needs_translation": "false"}, "lowpass_1": {"english": "Lowpass 1", "translation": "Passe-bas 1", "needs_translation": "false"}, "rpm_min_hz": {"english": "<PERSON><PERSON>", "translation": "Frequence minimum", "needs_translation": "false"}, "help_p2": {"english": "Gyro lowpass: Lowpass filters for the gyro signal. Typically left at default.", "translation": "Le filtre passe-bas elimine les frequences elevees indesirables.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Coupure", "needs_translation": "false"}, "notch_1": {"english": "Notch 1", "translation": "Filtreur 1", "needs_translation": "false"}, "max_cutoff": {"english": "Max cutoff", "translation": "Coupure max", "needs_translation": "false"}, "help_p3": {"english": "Gyro notch filters: Use for filtering specific frequency ranges. Typically not needed in most helis.", "translation": "Choisissez soigneusement les frequences minimales et maximales pour optimiser les performances.", "needs_translation": "false"}, "lowpass_2": {"english": "Lowpass 2", "translation": "Passe-bas 2", "needs_translation": "false"}, "rpm_filter": {"english": "RPM filter", "translation": "Filtre Tours Moteurs", "needs_translation": "false"}, "help_p1": {"english": "Typically you would not edit this page without checking your Blackbox logs!", "translation": "Normalement, vous ne devriez pas modifier cette page sans verifier vos journaux Blackbox !", "needs_translation": "false"}, "dyn_notch": {"english": "Dynamic Filters", "translation": "Filtres dynamique", "needs_translation": "false"}, "notch_q": {"english": "Notch Q", "translation": "Facteurs", "needs_translation": "false"}, "lowpass_1_dyn": {"english": "Lowpass 1 dyn.", "translation": "Passe-bas 1 dyn.", "needs_translation": "false"}, "notch_min_hz": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "notch_max_hz": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "notch_2": {"english": "Notch 2", "translation": "Filtreur 2", "needs_translation": "false"}, "name": {"english": "Filters", "translation": "Filtres", "needs_translation": "false"}, "min_cutoff": {"english": "Min cutoff", "translation": "Coupure min", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Centre", "needs_translation": "false"}}, "status": {"arming_disable_flag_3": {"english": "Bad RX Recovery", "translation": "Mauvaise recuperation RX", "needs_translation": "false"}, "arming_disable_flag_20": {"english": "RPM Filter", "translation": "Filtre RPM", "needs_translation": "false"}, "arming_disable_flag_11": {"english": "Load", "translation": "Charge", "needs_translation": "false"}, "arming_disable_flag_22": {"english": "DSHOT Bitbang", "translation": "DSHOT Bitbang", "needs_translation": "false"}, "dataflash_free_space": {"english": "Dataflash Free Space", "translation": "Espace libre Dataflash", "needs_translation": "false"}, "arming_disable_flag_25": {"english": "Arm Switch", "translation": "Interrupteur d'armement", "needs_translation": "false"}, "erasing": {"english": "Erasing", "translation": "Effacement", "needs_translation": "false"}, "arming_disable_flag_9": {"english": "Boot <PERSON>", "translation": "Temps de grace au demarrage", "needs_translation": "false"}, "megabyte": {"english": "MB", "translation": "MB", "needs_translation": "false"}, "arming_disable_flag_17": {"english": "Paralyze", "translation": "Paralyser", "needs_translation": "false"}, "arming_disable_flag_5": {"english": "Governor", "translation": "Gouverneur", "needs_translation": "false"}, "arming_disable_flag_8": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_1": {"english": "Fail Safe", "translation": "Fail Safe", "needs_translation": "false"}, "cpu_load": {"english": "CPU Load", "translation": "Charge CPU", "needs_translation": "false"}, "arming_disable_flag_15": {"english": "BST", "translation": "BST", "needs_translation": "false"}, "arming_disable_flag_12": {"english": "Calibrating", "translation": "Calibration", "needs_translation": "false"}, "arming_disable_flag_19": {"english": "Resc", "translation": "Secours", "needs_translation": "false"}, "arming_disable_flag_4": {"english": "Box Fail Safe", "translation": "Box Fail Safe", "needs_translation": "false"}, "arming_disable_flag_24": {"english": "Motor Protocol", "translation": "<PERSON>e moteur", "needs_translation": "false"}, "real_time_load": {"english": "Real-time Load", "translation": "Charge en temps reel", "needs_translation": "false"}, "help_p2": {"english": "To erase the dataflash for more log file storage, press the button on the menu denoted by a '*'.", "translation": "Pour effacer le dataflash pour plus de stockage de fichiers journaux, appuyez sur le bouton du menu indique par un '*'.", "needs_translation": "false"}, "arming_disable_flag_2": {"english": "RX Fail Safe", "translation": "RX Fail Safe", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "arming_disable_flag_0": {"english": "No Gyro", "translation": "Pas de G<PERSON>", "needs_translation": "false"}, "arming_disable_flag_18": {"english": "GPS", "translation": "GPS", "needs_translation": "false"}, "help_p1": {"english": "Use this page to view your current flight controller status. This can be useful when determining why your heli will not arm.", "translation": "Utilisez cette page pour voir l'etat actuel de votre controleur de vol. Cela peut etre utile pour determiner pourquoi votre helicoptere ne s'arme pas.", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Drapeaux d'armement", "needs_translation": "false"}, "unsupported": {"english": "Unsupported", "translation": "Non supporte", "needs_translation": "false"}, "erase_prompt": {"english": "Would you like to erase the dataflash?", "translation": "Voulez-vous effacer le dataflash ?", "needs_translation": "false"}, "erase": {"english": "Erase", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_10": {"english": "No Pre Arm", "translation": "Pas de pre-armement", "needs_translation": "false"}, "arming_disable_flag_21": {"english": "Reboot Required", "translation": "Redemarrage requis", "needs_translation": "false"}, "name": {"english": "Status", "translation": "Statut", "needs_translation": "false"}, "arming_disable_flag_13": {"english": "CLI", "translation": "CLI", "needs_translation": "false"}, "arming_disable_flag_14": {"english": "CMS Menu", "translation": "Menu CMS", "needs_translation": "false"}, "arming_disable_flag_16": {"english": "MSP", "translation": "MSP", "needs_translation": "false"}, "arming_disable_flag_7": {"english": "<PERSON>hrottle", "translation": "Gaz", "needs_translation": "false"}, "erasing_dataflash": {"english": "Erasing dataflash...", "translation": "Effacement du dataflash...", "needs_translation": "false"}, "arming_disable_flag_23": {"english": "Acc Calibration", "translation": "Calibration Acc", "needs_translation": "false"}}, "profile_pidbandwidth": {"help_p1": {"english": "PID Bandwidth: Overall bandwidth in HZ used by the PID loop.", "translation": "Bande passante PID : Bande passante globale en Hz utilisee par la boucle PID.", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "name": {"english": "PID Bandwidth", "translation": "Bande passante PID", "needs_translation": "false"}, "bterm_cutoff": {"english": "B-term cut-off", "translation": "Freq. de coupure B-term", "needs_translation": "false"}, "help_p3": {"english": "B-term cutoff: B-term cutoff frequency in HZ.", "translation": "Frequence de coupure B-term : Frequence de coupure B-term en Hz.", "needs_translation": "false"}, "dterm_cutoff": {"english": "D-term cut-off", "translation": "Freq. de coupure D-term", "needs_translation": "false"}, "help_p2": {"english": "D-term cutoff: D-term cutoff frequency in HZ.", "translation": "Frequence de coupure D-term : Frequence de coupure D-term en Hz.", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}}}, "navigation_save": {"english": "SAVE", "translation": "ENREG.", "needs_translation": "false"}, "menu_section_flight_tuning": {"english": "Flight Tuning", "translation": "Reglage du vol", "needs_translation": "false"}, "error_timed_out": {"english": "Error: timed out", "translation": "Erreur : le delai est depasse", "needs_translation": "false"}, "check_rf_module_on": {"english": "Please check your rf module is turned on.", "translation": "Veuillez verifier que votre module RF est active.", "needs_translation": "false"}, "msg_saving": {"english": "Saving...", "translation": "Enregistrement...", "needs_translation": "false"}, "msg_save_not_commited": {"english": "Save not committed to EEPROM", "translation": "Enregistrement non confirme dans l'EEPROM", "needs_translation": "false"}, "menu_section_advanced": {"english": "Advanced", "translation": "Avance", "needs_translation": "false"}, "msg_loading_from_fbl": {"english": "Loading data from flight controller...", "translation": "Chargement des donnees du controleur de vol...", "needs_translation": "false"}, "msg_reload_settings": {"english": "Reload data from flight controller?", "translation": "Recharger les donnees du controleur de vol ?", "needs_translation": "false"}, "menu_section_tools": {"english": "Tools", "translation": "Outils", "needs_translation": "false"}, "msg_connecting": {"english": "Connecting", "translation": "Connexion", "needs_translation": "false"}, "msg_save_current_page": {"english": "Save current page to flight controller?", "translation": "Enregistrer la page actuelle dans le controleur de vol ?", "needs_translation": "false"}, "btn_ok_long": {"english": "                OK                ", "translation": "                OK                ", "needs_translation": "false"}, "check_discovered_sensors": {"english": "Please check you have discovered all sensors.", "translation": "Veuillez verifier que tous les capteurs ont ete decouverts.", "needs_translation": "false"}, "msg_loading": {"english": "Loading...", "translation": "Chargement...", "needs_translation": "false"}, "check_heli_on": {"english": "Please check your heli is powered up and radio connected.", "translation": "Veuillez verifier que votre helicoptere est sous tension et connecte a la radio.", "needs_translation": "false"}, "check_bg_task": {"english": "Please enable the background task.", "translation": "Veuillez activer la tache en arriere-plan.", "needs_translation": "false"}, "navigation_tools": {"english": "*", "translation": "*", "needs_translation": "false"}, "check_supported_version": {"english": "This version of the Lua script \ncan't be used with the selected model", "translation": "Cette version du script Lua ne peut pas etre utilisee avec le modele selectionne.", "needs_translation": "false"}}