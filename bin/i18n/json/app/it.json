{"btn_ok": {"english": "          OK           ", "translation": "          OK           ", "needs_translation": "false"}, "btn_close": {"english": "CLOSE", "translation": "CHIUDI", "needs_translation": "false"}, "navigation_menu": {"english": "MENU", "translation": "MENU", "needs_translation": "false"}, "menu_section_hardware": {"english": "Hardware", "translation": "Hardware", "needs_translation": "false"}, "msg_please_disarm_to_save_warning": {"english": "Settings will only be saved to e<PERSON>rom on disarm", "translation": "I Settaggi saranno salvati su eeprom al disarmo", "needs_translation": "false"}, "msg_saving_settings": {"english": "Saving settings...", "translation": "<PERSON><PERSON> set<PERSON>i...", "needs_translation": "false"}, "msg_saving_to_fbl": {"english": "Saving data to flight controller...", "translation": "<PERSON><PERSON> dati al flight controller...", "needs_translation": "false"}, "navigation_reload": {"english": "RELOAD", "translation": "RICARICA", "needs_translation": "false"}, "menu_section_developer": {"english": "Developer", "translation": "Sviluppatore", "needs_translation": "false"}, "check_msp_version": {"english": "Unable to determine MSP version in use.", "translation": "Impossibile determinare la MSP in uso.", "needs_translation": "false"}, "menu_section_about": {"english": "About", "translation": "A proposito", "needs_translation": "false"}, "msg_please_disarm_to_save": {"english": "Please disarm to save", "translation": "Prego disarmare per salvare", "needs_translation": "false"}, "unit_hertz": {"english": "Hz", "translation": "Hz", "needs_translation": "false"}, "msg_rebooting": {"english": "Rebooting...", "translation": "Rebooting...", "needs_translation": "false"}, "msg_save_settings": {"english": "Save settings", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "btn_cancel": {"english": "CANCEL", "translation": "Configure the motor and speed controller features.", "needs_translation": "false"}, "msg_connecting_to_fbl": {"english": "Connecting to flight controller...", "translation": "Connessione al flight controller...", "needs_translation": "false"}, "navigation_help": {"english": "?", "translation": "?", "needs_translation": "false"}, "modules": {"stats": {"name": {"english": "Stats", "translation": "Stats", "needs_translation": "false"}, "totalflighttime": {"english": "Total Flight Time", "translation": "Totale Tempo Volo", "needs_translation": "false"}, "flightcount": {"english": "Flight Count", "translation": "Conteggio Voli", "needs_translation": "false"}, "lastflighttime": {"english": "Last Flight Time", "translation": "Tempo ultimo Volo", "needs_translation": "false"}, "help_p1": {"english": "Use this module to update the recorded flight statistics on the flight controller.", "translation": "Usa questo modulo per aggiornare le statistiche di volo sulla radio.", "needs_translation": "false"}}, "settings": {"name": {"english": "Settings", "translation": "Impostazioni", "needs_translation": "false"}, "no_themes_available_to_configure": {"english": "No configurable themes installed on this device", "translation": "<PERSON><PERSON>un tema disponibile configurabile", "needs_translation": "false"}, "txt_audio_timer": {"english": "Timer", "translation": "Timer", "needs_translation": "false"}, "txt_audio_events": {"english": "Events", "translation": "Eventi", "needs_translation": "false"}, "txt_audio_switches": {"english": "Switches", "translation": "Interruttori", "needs_translation": "false"}, "txt_iconsize": {"english": "Icon Size", "translation": "Dimensione Icone", "needs_translation": "false"}, "txt_general": {"english": "General", "translation": "Generale", "needs_translation": "false"}, "txt_text": {"english": "TEXT", "translation": "TESTO", "needs_translation": "false"}, "txt_small": {"english": "SMALL", "translation": "SMALL", "needs_translation": "false"}, "txt_large": {"english": "LARGE", "translation": "LARGE", "needs_translation": "false"}, "txt_syncname": {"english": "Sync model name", "translation": "Sync nome modello", "needs_translation": "false"}, "txt_devtools": {"english": "Developer Tools", "translation": "Tools Sviluppatori", "needs_translation": "false"}, "txt_apiversion": {"english": "API Version", "translation": "API Versione", "needs_translation": "false"}, "txt_logging": {"english": "Logging", "translation": "Logging", "needs_translation": "false"}, "txt_compilation": {"english": "Compilation", "translation": "Compilazione", "needs_translation": "false"}, "txt_loglocation": {"english": "Log location", "translation": "Percorso Log", "needs_translation": "false"}, "txt_console": {"english": "CONSOLE", "translation": "CONSOLE", "needs_translation": "false"}, "txt_consolefile": {"english": "CONSOLE & FILE", "translation": "CONSOLE & FILE", "needs_translation": "false"}, "txt_loglevel": {"english": "Log level", "translation": "<PERSON>llo Log", "needs_translation": "false"}, "txt_off": {"english": "OFF", "translation": "OFF", "needs_translation": "false"}, "txt_info": {"english": "INFO", "translation": "INFO", "needs_translation": "false"}, "txt_debug": {"english": "DEBUG", "translation": "DEBUG", "needs_translation": "false"}, "txt_mspdata": {"english": "Log msp data", "translation": "Log msp data", "needs_translation": "false"}, "txt_queuesize": {"english": "Log MSP queue size", "translation": "Dimensione Coda Log MSP", "needs_translation": "false"}, "txt_memusage": {"english": "Log memory usage", "translation": "Uso Memoria Log", "needs_translation": "false"}, "txt_batttype": {"english": "Tx Battery Options", "translation": "Tx Battery Options", "needs_translation": "true"}, "txt_battdef": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "true"}, "txt_batttext": {"english": "Text", "translation": "Text", "needs_translation": "true"}, "txt_battdig": {"english": "Digital", "translation": "Digital", "needs_translation": "true"}, "dashboard": {"english": "Dashboard", "translation": "Dashboard", "needs_translation": "false"}, "dashboard_theme": {"english": "Theme", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "dashboard_theme_panel_global": {"english": "Default theme for all models", "translation": "Globale", "needs_translation": "false"}, "dashboard_theme_panel_model": {"english": "Optional theme for this model", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "dashboard_theme_panel_model_disabled": {"english": "Disabled", "translation": "Disabilitato", "needs_translation": "false"}, "dashboard_settings": {"english": "Settings", "translation": "Set<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "dashboard_theme_preflight": {"english": "Preflight Theme", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "dashboard_theme_inflight": {"english": "Inflight Theme", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "dashboard_theme_postflight": {"english": "Postflight Theme", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "audio": {"english": "Audio", "translation": "Audio", "needs_translation": "false"}, "localizations": {"english": "Localization", "translation": "Localizzazione", "needs_translation": "false"}, "txt_development": {"english": "Development", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "temperature_unit": {"english": "Temperature Unit", "translation": "Unità Temperatura", "needs_translation": "false"}, "altitude_unit": {"english": "Altitude Unit", "translation": "Unità Altitudine", "needs_translation": "false"}, "celcius": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "fahrenheit": {"english": "Fahrenheit", "translation": "Fahrenheit", "needs_translation": "false"}, "meters": {"english": "Meters", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "feet": {"english": "Feet", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "warning": {"english": "Warning", "translation": "Attenzione", "needs_translation": "false"}, "governor_state": {"english": "Governor State", "translation": "Stato Governor", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Arming Flags", "needs_translation": "true"}, "voltage": {"english": "Voltage", "translation": "Voltaggio", "needs_translation": "false"}, "pid_rates_profile": {"english": "PID/Rates Profile", "translation": "Profilo PID/escursioni", "needs_translation": "false"}, "pid_profile": {"english": "PID Profile", "translation": "Profilo PID", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "esc_temperature": {"english": "ESC Temperature", "translation": "ESC Temperatura", "needs_translation": "false"}, "esc_threshold": {"english": "Thresh<PERSON> (°)", "translation": "Soglia (°)", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "Voltaggio BEC", "needs_translation": "false"}, "bec_threshold": {"english": "<PERSON><PERSON><PERSON><PERSON> (V)", "translation": "Soglia (V)", "needs_translation": "false"}, "fuel": {"english": "Fuel", "translation": "Carburante", "needs_translation": "false"}, "fuel_callout_default": {"english": "Default (Only at 10%)", "translation": "De<PERSON>ult (Solo al 10%)", "needs_translation": "false"}, "fuel_callout_10": {"english": "Every 10%", "translation": "Ogni 10%", "needs_translation": "false"}, "fuel_callout_20": {"english": "Every 20%", "translation": "Ogni 20%", "needs_translation": "false"}, "fuel_callout_25": {"english": "Every 25%", "translation": "Ogni 25%", "needs_translation": "false"}, "fuel_callout_50": {"english": "Every 50%", "translation": "Ogni 50%", "needs_translation": "false"}, "fuel_callout_percent": {"english": "Callout %", "translation": "Callout %", "needs_translation": "false"}, "fuel_repeats_below": {"english": "Repeats below 0%", "translation": "<PERSON><PERSON><PERSON> sotto 0%", "needs_translation": "false"}, "fuel_haptic_below": {"english": "Haptic below 0%", "translation": "Haptic sotto 0%", "needs_translation": "false"}, "timer_alerting": {"english": "Timer Alerting", "translation": "Avviso Timer", "needs_translation": "false"}, "timer_elapsed_alert_mode": {"english": "Timer <PERSON><PERSON><PERSON>", "translation": "Avviso Timer <PERSON>", "needs_translation": "false"}, "timer_prealert_options": {"english": "Pre-timer <PERSON><PERSON>", "translation": "Opzioni Avviso Pre-timer", "needs_translation": "false"}, "timer_prealert": {"english": "Pre-timer <PERSON><PERSON>", "translation": "Avviso Pre-timer", "needs_translation": "false"}, "timer_alert_period": {"english": "Alert <PERSON>", "translation": "Periodo Avviso", "needs_translation": "false"}, "timer_postalert_options": {"english": "Post-timer <PERSON><PERSON>", "translation": "Opzioni Avvisi Post-timer", "needs_translation": "false"}, "timer_postalert": {"english": "Post-timer <PERSON><PERSON>", "translation": "Avviso Post-timer", "needs_translation": "false"}, "timer_postalert_period": {"english": "Alert <PERSON>", "translation": "Periodo Avviso", "needs_translation": "false"}, "timer_postalert_interval": {"english": "<PERSON><PERSON>", "translation": "Intervallo Avviso", "needs_translation": "false"}}, "validate_sensors": {"help_p1": {"english": "This tool attempts to list all the sensors that you are not receiving in a concise list.", "translation": "Questo strumento prova a elencare tutti i sensori che non ricevi in una lista concisa.", "needs_translation": "false"}, "invalid": {"english": "INVALID", "translation": "INVALIDO", "needs_translation": "false"}, "name": {"english": "Sensors", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "msg_repair": {"english": "Enable required sensors on flight controller?", "translation": "Abilito i sensori richiesti sul flight controller?", "needs_translation": "false"}, "msg_repair_fin": {"english": "The flight controller has been configured? You may need to perform a discover sensors to see the changes.", "translation": "Il flight controller e' stato configurato? Potresti necessitare di riscoprire i sensori per vedere i cambiamenti.", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "Use this tool to ensure you are sending the correct sensors.", "translation": "Usa questo strumento per assicurarti di inviare i sensori corretti.", "needs_translation": "false"}}, "msp_exp": {"help_p1": {"english": "This tool provides the ability to send a custom byte string to the flight controller. It is useful for developers when debugging values.", "translation": "Questo strumento permette di inviare una stringa byte custom al flight controller. E' utile in fase di sviluppo/Debug", "needs_translation": "false"}, "name": {"english": "MSP Expermental", "translation": "MSP Sperimentale", "needs_translation": "false"}, "help_p2": {"english": "If you do not understand what you are doing, do not use it as bad things can happen.", "translation": "Se non sai cosa stai facendo, non usare, le cose brutte accadono.", "needs_translation": "false"}}, "esc_tools": {"unknown": {"english": "UNKNOWN", "translation": "SCONOSCIUTO", "needs_translation": "false"}, "name": {"english": "ESC Tools", "translation": "Strumenti ESC", "needs_translation": "false"}, "please_powercycle": {"english": "Please power cycle the ESC...", "translation": "Prego spegnere e riaccendere l'ESC...", "needs_translation": "false"}, "mfg": {"hw5": {"esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "brake_force": {"english": "Brake Force%", "translation": "Force Freno%", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Rotazione", "needs_translation": "false"}, "soft_start": {"english": "Soft Start", "translation": "Soft Start", "needs_translation": "false"}, "name": {"english": "Hobbywing V5", "translation": "Hobbywing V5", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Limiti", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "Voltaggio BEC", "needs_translation": "false"}, "gov_i_gain": {"english": "I-G<PERSON>", "translation": "I-G<PERSON>", "needs_translation": "false"}, "startup_time": {"english": "Startup Time", "translation": "Tempo di Startup", "needs_translation": "false"}, "lipo_cell_count": {"english": "LiPo Cell Count", "translation": "Conto Celle LiPo", "needs_translation": "false"}, "restart_time": {"english": "Restart Time", "translation": "Reimposta Tempo", "needs_translation": "false"}, "volt_cutoff_type": {"english": "Volt Cutoff Type", "translation": "Tipo Volt Cutoff", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "Motore", "needs_translation": "false"}, "brake_type": {"english": "Brake Type", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "brake": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basic", "needs_translation": "false"}, "flight_mode": {"english": "Flight Mode", "translation": "Modo <PERSON>", "needs_translation": "false"}, "auto_restart": {"english": "Auto Restart", "translation": "Auto Riavvia", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Attiva Freewheel", "needs_translation": "false"}, "cutoff_voltage": {"english": "Cutoff Voltage", "translation": "Voltaggio <PERSON>lio", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Startup Power", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Altro", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Timing", "needs_translation": "false"}, "gov_p_gain": {"english": "P-Gain", "translation": "P-Gain", "needs_translation": "false"}}, "xdfly": {"hv_bec_voltage": {"english": "HV BEC Voltage", "translation": "Voltaggio HV BEC", "needs_translation": "false"}, "gov": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "brake_force": {"english": "Brake Force", "translation": "Forza Freno", "needs_translation": "false"}, "sr_function": {"english": "SR Function", "translation": "Funzione SR", "needs_translation": "false"}, "name": {"english": "XDFLY", "translation": "XDFLY", "needs_translation": "false"}, "lv_bec_voltage": {"english": "LV BEC Voltage", "translation": "Voltaggio LV BEC", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Tempo di Auto Riavvio", "needs_translation": "false"}, "acceleration": {"english": "Acceleration", "translation": "Accelerazione", "needs_translation": "false"}, "motor_direction": {"english": "Motor Direction", "translation": "Direzione Motore", "needs_translation": "false"}, "smart_fan": {"english": "Smart Fan", "translation": "Ventola Smart", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "led_color": {"english": "LED Color", "translation": "Colore LED", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Base", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Startup Power", "needs_translation": "false"}, "motor_poles": {"english": "Motor Poles", "translation": "Poli Motore", "needs_translation": "false"}, "capacity_correction": {"english": "Capacity Correction", "translation": "Correzione Capacita'", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Timing", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}, "flrtr": {"gov": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "motor_temp_sensor": {"english": "Motor temp sensor", "translation": "Sensore temp Motore", "needs_translation": "false"}, "starting_torque": {"english": "Starting torque", "translation": "Coppia di avvio", "needs_translation": "false"}, "cell_count": {"english": "Cell count", "translation": "Numero Celle", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}, "motor_erpm_max": {"english": "Motor ERPM max", "translation": "ERPM max motore", "needs_translation": "false"}, "name": {"english": "FLYROTOR", "translation": "FLYROTOR", "needs_translation": "false"}, "low_voltage_protection": {"english": "Low voltage protection", "translation": "Protezione Voltaggio basso", "needs_translation": "false"}, "gov_d": {"english": "Gov-D", "translation": "Gov-D", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry protocol", "translation": "Protocollo Telemetria", "needs_translation": "false"}, "motor_direction": {"english": "Motor direction", "translation": "Direzione Motore", "needs_translation": "false"}, "throttle_protocol": {"english": "Throttle protocol", "translation": "Protocollo Gas/Throttle", "needs_translation": "false"}, "soft_start": {"english": "Soft start", "translation": "Soft start", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Altro", "needs_translation": "false"}, "temperature_protection": {"english": "Temperature protection", "translation": "Temperatura di protezione", "needs_translation": "false"}, "buzzer_volume": {"english": "Buzzer volume", "translation": "Volume Buzzer", "needs_translation": "false"}, "timing_angle": {"english": "Timing angle", "translation": "Timing angle", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "bec_voltage": {"english": "BEC voltage", "translation": "Voltaggio BEC", "needs_translation": "false"}, "fan_control": {"english": "Fan control", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Base", "needs_translation": "false"}, "current_gain": {"english": "Current gain", "translation": "Guadagno Corrente", "needs_translation": "false"}, "led_color": {"english": "LED color", "translation": "Colore LED", "needs_translation": "false"}, "motor_temp": {"english": "Motor temperture", "translation": "Temperatura Motore", "needs_translation": "false"}, "response_speed": {"english": "Response speed", "translation": "Velocita' risposta", "needs_translation": "false"}, "battery_capacity": {"english": "Battery capacity", "translation": "Capacita' Batteria", "needs_translation": "false"}}, "scorp": {"esc_mode": {"english": "ESC Mode", "translation": "Modo ESC", "needs_translation": "false"}, "min_voltage": {"english": "Min Voltage", "translation": "Voltaggio Min", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Rotazione", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry Protocol", "translation": "Protocollo Telemetria", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "runup_time": {"english": "Runup Time", "translation": "Tempo Runup", "needs_translation": "false"}, "motor_startup_sound": {"english": "Motor Startup Sound", "translation": "Suono di avvio Motore", "needs_translation": "false"}, "gov_integral": {"english": "Gov <PERSON><PERSON>", "translation": "Gov <PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_proportional": {"english": "Gov Proportional", "translation": "Gov Proporzionale", "needs_translation": "false"}, "cutoff_handling": {"english": "Cutoff Handling", "translation": "Cutoff Handling", "needs_translation": "false"}, "bailout": {"english": "Bailout", "translation": "Soccorso", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Limiti", "needs_translation": "false"}, "soft_start_time": {"english": "Soft Start Time", "translation": "Tempos di Soft Start", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "Voltaggio BEC", "needs_translation": "false"}, "extra_msg_save": {"english": "Please reboot the ESC to apply the changes", "translation": "Prego riavviare l'ESC per applicare i cambiamenti", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Base", "needs_translation": "false"}, "max_current": {"english": "Max <PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "max_temperature": {"english": "Max Temperature", "translation": "Temperatura Max", "needs_translation": "false"}, "protection_delay": {"english": "Protection Delay", "translation": "Ritardo protezion", "needs_translation": "false"}, "max_used": {"english": "Max Used", "translation": "<PERSON>", "needs_translation": "false"}}, "yge": {"esc_mode": {"english": "ESC Mode", "translation": "Modo ESC", "needs_translation": "false"}, "esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "current_limit": {"english": "Current Limit", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "f3c_auto": {"english": "F3C Autorotation", "translation": "F3C Autorotazione", "needs_translation": "false"}, "name": {"english": "YGE", "translation": "YGE", "needs_translation": "false"}, "max_start_power": {"english": "Max Start Power", "translation": "Max Start Power", "needs_translation": "false"}, "lv_bec_voltage": {"english": "BEC", "translation": "BEC", "needs_translation": "false"}, "pinion_teeth": {"english": "Pinion Teeth", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Tempo Auto Restart", "needs_translation": "false"}, "main_teeth": {"english": "Main Teeth", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Altro", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Limiti", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "throttle_response": {"english": "Throttle Response", "translation": "Rispsota Accelerazione", "needs_translation": "false"}, "stick_zero_us": {"english": "<PERSON>", "translation": "Zero Stick", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "motor_pole_pairs": {"english": "Motor Pole Pairs", "translation": "Paio Poli Motore", "needs_translation": "false"}, "stick_range_us": {"english": "Stick Range", "translation": "Range Stick", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Base", "needs_translation": "false"}, "min_start_power": {"english": "Min Start Power", "translation": "Min Start Power", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Attiva RuotaLibera", "needs_translation": "false"}, "direction": {"english": "Direction", "translation": "Direzione", "needs_translation": "false"}, "timing": {"english": "Motor Timing", "translation": "Tim<PERSON>", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}}, "searching": {"english": "Searching", "translation": "Cerco...", "needs_translation": "false"}}, "pids": {"help_p1": {"english": "FeedForward (Roll/Pitch): Start at 70, increase until stops are sharp with no drift. Keep roll and pitch equal.", "translation": "FeedForward (rollio/beccheggio): iniziare a 70, aumentare fino a quando gli arresti sono netti senza deriva. Mantenere il rollio e il beccheggio uguali.", "needs_translation": "false"}, "o": {"english": "O", "translation": "O", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "help_p5": {"english": "Test & Adjust: Fly, observe, and fine-tune for best performance in real conditions.", "translation": "Prova & regola: vola, osserva e regola per ottenere le migliori prestazioni in condizioni reali.", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "PIDs", "translation": "PIDs", "needs_translation": "false"}, "help_p2": {"english": "<PERSON> Gain (Roll/Pitch): Raise gradually for stable piro pitch pumps. Too high causes wobbles; match roll/pitch values.", "translation": "I guadagno (rollio/beccheggio): aumentare gradualmente per stabilizzare le pompe del beccheggio. Valori troppo alti causano oscillazioni; abb<PERSON>re i valori di rollio/beccheggio.", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "b": {"english": "B", "translation": "B", "needs_translation": "false"}, "help_p4": {"english": "Tail Stop Gain (CW/CCW): Adjust separately for clean, bounce-free stops in both directions.", "translation": "Guadagno di arresto della coda (CW/CCW): Regolare separatamente per arresti puliti e senza rimbalzi in entrambe le direzioni.", "needs_translation": "false"}, "help_p3": {"english": "Tail P/I/D Gains: Increase P until slight wobble in funnels, then back off slightly. Raise I until tail holds firm in hard moves (too high causes slow wag). Adjust D for smooth stops—higher for slow servos, lower for fast ones.", "translation": "Guadagni P/I/D della coda: aumentare P fino a quando non si nota una leggera oscillazione negli imbuti, quindi diminuire leggermente. Aumentare I fino a quando la coda non rimane ferma nei movimenti bruschi (troppo alto provoca un'oscillazione lenta). Regolare D per arresti fluidi: piu' alto per i servi lenti, piu' basso per quelli veloci.", "needs_translation": "false"}}, "msp_speed": {"seconds_600": {"english": "  600S  ", "translation": "  600S  ", "needs_translation": "false"}, "avg_query_time": {"english": "Average query time", "translation": "Tempo medio query", "needs_translation": "false"}, "seconds_30": {"english": "  30S  ", "translation": "  30S  ", "needs_translation": "false"}, "name": {"english": "MSP Speed", "translation": "Velocita' MSP", "needs_translation": "false"}, "max_query_time": {"english": "Maximum query time", "translation": "Tempo massimo query", "needs_translation": "false"}, "help_p1": {"english": "This tool attempts to determine the quality of your MSP data link by performing as many large MSP queries within 30 seconds as possible.", "translation": "Questo strumento tenta di determinare la qualita' del collegamento dati MSP eseguendo il maggior numero possibile di query MSP di grandi dimensioni entro 30 secondi.", "needs_translation": "false"}, "retries": {"english": "Retries", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "checksum_errors": {"english": "Checksum errors", "translation": "Checksum errors", "needs_translation": "false"}, "test_length": {"english": "Test length", "translation": "<PERSON> lunghezza", "needs_translation": "false"}, "start": {"english": "Start", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "memory_free": {"english": "Memory free", "translation": "Memoria Libera", "needs_translation": "false"}, "start_prompt": {"english": "Would you like to start the test? Choose the test run time below.", "translation": "Vuoi iniziare il test? Scegli qui sotto il tempo di prova.", "needs_translation": "false"}, "rf_protocol": {"english": "RF protocol", "translation": "Protocollo RF", "needs_translation": "false"}, "min_query_time": {"english": "Minimum query time", "translation": "Tempo Minimo query", "needs_translation": "false"}, "seconds_120": {"english": "  120S  ", "translation": "  120S  ", "needs_translation": "false"}, "seconds_300": {"english": "  300S  ", "translation": "  300S  ", "needs_translation": "false"}, "testing": {"english": "Testing", "translation": "Test in corso", "needs_translation": "false"}, "successful_queries": {"english": "Successful queries", "translation": "Query <PERSON>", "needs_translation": "false"}, "timeouts": {"english": "Timeouts", "translation": "Timeouts", "needs_translation": "false"}, "testing_performance": {"english": "Testing MSP performance...", "translation": "Test performance MSP...", "needs_translation": "false"}, "total_queries": {"english": "Total queries", "translation": "Query <PERSON>i", "needs_translation": "false"}}, "copyprofiles": {"profile_type": {"english": "Profile Type", "translation": "T<PERSON>o Profilo", "needs_translation": "false"}, "profile_type_pid": {"english": "PID", "translation": "PID", "needs_translation": "false"}, "profile_type_rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "msgbox_save": {"english": "Save settings", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "Copy Profiles", "translation": "Copia Profili", "needs_translation": "false"}, "help_p1": {"english": "Copy PID profile or Rate profile from Source to Destination.", "translation": "Copia il profilo PID o il profilo di velocita' dalla sorgente alla destinazione.", "needs_translation": "false"}, "dest_profile": {"english": "Dest. Profile", "translation": "Profilo <PERSON>.", "needs_translation": "false"}, "source_profile": {"english": "Source Profile", "translation": "Prof<PERSON>", "needs_translation": "false"}, "msgbox_msg": {"english": "Save current page to flight controller?", "translation": "<PERSON><PERSON>e la pagina corrente sul controller di volo?", "needs_translation": "false"}, "help_p2": {"english": "Choose the source and destinations and save to copy the profile.", "translation": "Scegli la sorgente e le destinazioni e salva per copiare il profilo.", "needs_translation": "false"}}, "esc_motors": {"min_throttle": {"english": "0% Throttle PWM Value", "translation": "Valore PWM 0% Throttle", "needs_translation": "false"}, "tail_motor_ratio": {"english": "Tail Motor Ratio", "translation": "Rateo Motore Coda", "needs_translation": "false"}, "max_throttle": {"english": "100% Throttle PWM value", "translation": "Valore PWM 100% Throttle", "needs_translation": "false"}, "main_motor_ratio": {"english": "Main Motor Ratio", "translation": "Rateo Motore Primario", "needs_translation": "false"}, "pinion": {"english": "Pinion", "translation": "Pinione", "needs_translation": "false"}, "main": {"english": "Main", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Configure the motor and speed controller features.", "translation": "Configurare le funzioni del motore e del regolatore di velocita'.", "needs_translation": "false"}, "rear": {"english": "Rear", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "front": {"english": "Front", "translation": "Fronte", "needs_translation": "false"}, "voltage_correction": {"english": "Voltage Correction", "translation": "Correzione Voltaggio", "needs_translation": "false"}, "mincommand": {"english": "Motor Stop PWM Value", "translation": "Valore PWM Stop Motore", "needs_translation": "false"}, "name": {"english": "ESC/Motors", "translation": "ESC/Motori", "needs_translation": "false"}, "motor_pole_count": {"english": "Motor Pole Count", "translation": "Numero Magneti Motore", "needs_translation": "false"}, "current_correction": {"english": "Current Correction", "translation": "Correzione Corrente", "needs_translation": "false"}, "consumption_correction": {"english": "Consumption Correction", "translation": "Correzione Consumo", "needs_translation": "false"}}, "radio_config": {"deflection": {"english": "Deflection", "translation": "Deflessione", "needs_translation": "false"}, "max_throttle": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "stick": {"english": "Stick", "translation": "Stick", "needs_translation": "false"}, "arming": {"english": "Arming", "translation": "Armo", "needs_translation": "false"}, "yaw_deadband": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "cyclic": {"english": "Cyclic", "translation": "Ciclico", "needs_translation": "false"}, "name": {"english": "Radio Config", "translation": "Radio Config", "needs_translation": "false"}, "help_p1": {"english": "Configure your radio settings. Stick center, arm, throttle hold, and throttle cut.", "translation": "Configurare le impostazioni della radio. Centro Stick, attivazione, blocco acceleratore e disattivazione acceleratore.", "needs_translation": "false"}, "min_throttle": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "throttle": {"english": "<PERSON>hrottle", "translation": "<PERSON>hrottle", "needs_translation": "false"}, "deadband": {"english": "Deadband", "translation": "Deadband", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Centro", "needs_translation": "false"}}, "profile_select": {"help_p1": {"english": "Set the current flight profile or rate profile you would like to use.", "translation": "Imposta il profilo di volo o il profilo di velocita' corrente che desideri utilizzare.", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "pid_profile": {"english": "PID profile", "translation": "Profilo PID", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "<PERSON><PERSON>e la pagina corrente sul controller di volo?", "needs_translation": "false"}, "save_prompt_local": {"english": "Save current page to radio?", "translation": "<PERSON><PERSON> i settaggi correnti?", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "CANCELLA", "needs_translation": "false"}, "name": {"english": "Select Profile", "translation": "Seleziona Profilo", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "If you use a switch on your radio to change flight or rate modes, this will override this choice as soon as you toggle the switch.", "translation": "Se si utilizza un interruttore sulla radio per cambiare le modalita' di volo o di velocita', questa scelta verra' ignorata non appena si aziona l'interruttore.", "needs_translation": "false"}}, "profile_governor": {"tail_torque_assist": {"english": "<PERSON><PERSON>", "translation": "Assistenza Coppia Rot.coda", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "cyc": {"english": "Cyc", "translation": "Cyc", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "help_p1": {"english": "Full headspeed: Headspeed target when at 100% throttle input.", "translation": "Velocita' massima della Rotore: velocita' target della Rotore quando si e' al 100% dell'input dell'acceleratore.", "needs_translation": "false"}, "help_p6": {"english": "Tail Torque Assist: For motorized tails. Gain and limit of headspeed increase when using main rotor torque for yaw assist.", "translation": "Assistenza alla coppia di coda: per code motorizzate. Guadagno e limite di aumento della velocita' utilizzando la coppia del rotore principale per l'assistenza yaw", "needs_translation": "false"}, "help_p4": {"english": "Precomp: Governor precomp gain for yaw, cyclic, and collective inputs.", "translation": "Precomp: guadagno precomp per gli input di imbardata, ciclico e collettivo.", "needs_translation": "false"}, "max_throttle": {"english": "<PERSON> throttle", "translation": "<PERSON> throttle", "needs_translation": "false"}, "full_headspeed": {"english": "Full headspeed", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "precomp": {"english": "Precomp", "translation": "Precomp", "needs_translation": "false"}, "gain": {"english": "PID master gain", "translation": "Guadagno Master PID", "needs_translation": "false"}, "disabled_message": {"english": "Rotorflight governor is not enabled", "translation": "Governor <PERSON><PERSON><PERSON><PERSON> disabilitato", "needs_translation": "false"}, "help_p3": {"english": "Gains: Fine tuning of the governor.", "translation": "<PERSON><PERSON><PERSON><PERSON>: tuning fine del governor.", "needs_translation": "false"}, "col": {"english": "Col", "translation": "Col", "needs_translation": "false"}, "min_throttle": {"english": "<PERSON> throttle", "translation": "<PERSON> throttle", "needs_translation": "false"}, "tta_limit": {"english": "Limit", "translation": "Limite", "needs_translation": "false"}, "help_p2": {"english": "PID master gain: How hard the governor works to hold the RPM.", "translation": "Guadagno principale PID: quanto lavora il regolatore per mantenere i RPM.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p5": {"english": "Max throttle: The maximum throttle % the governor is allowed to use.", "translation": "Max throttle (Accelerazione massima): La massima percentuale di accelerazione che il regolatore puo' utilizzare.", "needs_translation": "false"}, "tta_gain": {"english": "<PERSON><PERSON>", "translation": "Guadagno", "needs_translation": "false"}}, "profile_tailrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "Guadagno", "needs_translation": "false"}, "help_p4": {"english": "Collective FF Gain: Tail precompensation for collective inputs.", "translation": "Guadagno FF collettivo: Precompensazione della coda per gli input collettivi.", "needs_translation": "false"}, "collective_impulse_ff": {"english": "Collective Impulse FF", "translation": "Impulso FF Collettivo", "needs_translation": "false"}, "help_p2": {"english": "Precomp Cutoff: Frequency limit for all yaw precompensation actions.", "translation": "Precomp Cutoff: limite di frequenza per tutte le azioni di precompensazione dell'imbardata.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "help_p3": {"english": "Cyclic FF Gain: Tail precompensation for cyclic inputs.", "translation": "Guadagno FF ciclico: Precompensazione di coda per ingressi ciclici.", "needs_translation": "false"}, "help_p1": {"english": "Yaw Stop Gain: Higher stop gain will make the tail stop more aggressively but may cause oscillations if too high. Adjust CW or CCW to make the yaw stops even.", "translation": "Guadagno di Stop YAW: uno stop piu' elevato fara' fermare la coda in modo piu' aggressivo, ma potrebbe causare oscillazioni se troppo alto. Regolare CW o CCW per rendere gli stop yaw uniformi.", "needs_translation": "false"}, "inertia_precomp": {"english": "Inertia Precomp", "translation": "Inerzia Precomp", "needs_translation": "false"}, "cyclic_ff_gain": {"english": "Cyclic FF gain", "translation": "Guadagno FF Ciclico", "needs_translation": "false"}, "help_p5": {"english": "Collective Impulse FF: Impulse tail precompensation for collective inputs. If you need extra tail precompensation at the beginning of collective input.", "translation": "Impulso collettivo FF: Precompensazione di coda dell'impulso per input collettivi. Se e' necessaria una precompensazione di coda extra all'inizio dell'input collettivo.", "needs_translation": "false"}, "cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "yaw_stop_gain": {"english": "Yaw stop gain", "translation": "Guadagno Yaw stop", "needs_translation": "false"}, "precomp_cutoff": {"english": "Precomp Cutoff", "translation": "Precomp Cutoff", "needs_translation": "false"}, "collective_ff_gain": {"english": "Collective FF gain", "translation": "Guadagno FF Collettivo", "needs_translation": "false"}, "name": {"english": "Tail Rotor", "translation": "Rotore Coda", "needs_translation": "false"}, "decay": {"english": "Decay", "translation": "Decadimento", "needs_translation": "false"}}, "profile_pidcontroller": {"help_p4": {"english": "Error rotation: Allow errors to be shared between all axes.", "translation": "Errore rotazione: consente di condividere gli errori tra tutti gli assi.", "needs_translation": "false"}, "ground_error_decay": {"english": "Ground Error Decay", "translation": "Errore Decadimento Suolo", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "inflight_error_decay": {"english": "Inflight Error Decay", "translation": "Errore Decadimento InVolo", "needs_translation": "false"}, "help_p2": {"english": "Error limit: Angle limit for I-term.", "translation": "Errore limite: <PERSON><PERSON> limite per I-term.", "needs_translation": "false"}, "error_limit": {"english": "Error limit", "translation": "Errore limite", "needs_translation": "false"}, "help_p3": {"english": "Offset limit: Angle limit for High Speed Integral (O-term).", "translation": "Limite Offset: <PERSON><PERSON> limite per Integrale Alta Velocita' (O-term).", "needs_translation": "false"}, "cutoff_point": {"english": "Cut-off point", "translation": "Punto Cut-off", "needs_translation": "false"}, "limit": {"english": "Limit", "translation": "Limite", "needs_translation": "false"}, "iterm_relax": {"english": "I-term relax", "translation": "I-term relax", "needs_translation": "false"}, "hsi_offset_limit": {"english": "HSI Offset limit", "translation": "Limite Offset HSI", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "name": {"english": "PID Controller", "translation": "Controller PID", "needs_translation": "false"}, "error_rotation": {"english": "Error rotation", "translation": "Errore rotazione", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}, "help_p5": {"english": "I-term relax: Limit accumulation of I-term during fast movements - helps reduce bounce back after fast stick movements. Generally needs to be lower for large helis and can be higher for small helis. Best to only reduce as much as is needed for your flying style.", "translation": "I-term relax: Limitare l'accumulo di I-term durante i movimenti rapidi: Riduce il rimbalzo dopo i movimenti rapidi dello stick. Generalmente deve essere piu' basso per gli elicotteri grandi e puo' essere piu' alto per quelli piccoli. e' meglio ridurlo solo quanto necessario per il proprio stile di volo.", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Tempo", "needs_translation": "false"}, "help_p1": {"english": "Error decay ground: PID decay to help prevent heli from tipping over when on the ground.", "translation": "Errore decadimento suolo: Decadimento PID per evitare che l'elicottero si ribalti quando e' a terra.", "needs_translation": "false"}}, "logs": {"help_logs_p2": {"english": "Note. To enable logging it is essential for you to have the following sensors enabled.", "translation": "Nota. Per abilitare la registrazione e' essenziale che tu abbia abilitato i seguenti sensori.", "needs_translation": "false"}, "name": {"english": "Logs", "translation": "Logs", "needs_translation": "false"}, "help_logs_p1": {"english": "Please select a log file from the list below.", "translation": "Selezionare un file log dall'elenco seguente.", "needs_translation": "false"}, "msg_no_logs_found": {"english": "NO LOG FILES FOUND", "translation": "LOG FILES ASSENTI", "needs_translation": "false"}, "help_logs_tool_p1": {"english": "Please use the slider to navigate the graph.", "translation": "Utilizza lo slider per navigare nel grafico.", "needs_translation": "false"}, "help_logs_p3": {"english": "- arm status, voltage, headspeed, current, esc temperature", "translation": "- stato armo, voltaggio, <PERSON><PERSON><PERSON>, corrente, esc temperatura", "needs_translation": "false"}}, "battery": {"calcfuel_local": {"english": "Calculate Fuel Using", "translation": "Calculate fuel using", "needs_translation": "true"}, "max_cell_voltage": {"english": "Max Cell Voltage", "translation": "Voltaggio Max Cella", "needs_translation": "false"}, "full_cell_voltage": {"english": "Full Cell Voltage", "translation": "Voltaggio Cella pieno", "needs_translation": "false"}, "name": {"english": "Battery", "translation": "Batteria", "needs_translation": "false"}, "min_cell_voltage": {"english": "Min Cell Voltage", "translation": "Voltaggio Min Cella", "needs_translation": "false"}, "help_p1": {"english": "The battery settings are used to configure the flight controller to monitor the battery voltage and provide warnings when the voltage drops below a certain level.", "translation": "Le impostazioni della batteria utilizzate per configurare il controller di volo in modo da monitorare la tensione e fornire avvisi quando la tensione scende al di sotto di un certo livello.", "needs_translation": "false"}, "battery_capacity": {"english": "Battery Capacity", "translation": "Capacita' Batteria", "needs_translation": "false"}, "warn_cell_voltage": {"english": "Warn Cell Voltage", "translation": "Avviso tensione cella", "needs_translation": "false"}, "cell_count": {"english": "Cell Count", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "consumption_warning_percentage": {"english": "Consumption Warning %", "translation": "Avviso Consumo %", "needs_translation": "false"}, "timer": {"english": "Flight Time Alarm", "translation": "Tempo Volo", "needs_translation": "false"}, "voltage_multiplier": {"english": "Sag Compensation", "translation": "Compensazione di caduta", "needs_translation": "false"}, "kalman_multiplier": {"english": "Filter Compensation", "translation": "Filter compensation", "needs_translation": "true"}, "alert_type": {"english": "Rx Voltage Alert", "translation": "BEC or Rx Batt Voltage Alert", "needs_translation": "true"}, "bec_voltage_alert": {"english": "BEC Alert Value", "translation": "BEC Alert Value", "needs_translation": "true"}, "rx_voltage_alert": {"english": "RxBatt Alert Value", "translation": "RX Batt Alert Value", "needs_translation": "true"}}, "profile_mainrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "Guadagno", "needs_translation": "false"}, "help_p4": {"english": "Cross Coupling Freq. Limit: Frequency limit for the compensation, higher value will make the compensation action faster.", "translation": "Limite di frequenza Cross Coupling: limite di frequenza per la compensazione, un valore piu' alto rendera' piu' veloce l'azione di compensazione.", "needs_translation": "false"}, "collective_pitch_comp_short": {"english": "Col. Pitch Compensation", "translation": "Compensazione Col. Pitch", "needs_translation": "false"}, "cyclic_cross_coupling": {"english": "Cyclic Cross coupling", "translation": "Cross coupling ciclico", "needs_translation": "false"}, "collective_pitch_comp": {"english": "Collective Pitch Compensation", "translation": "Compensazione Pitch Collettivo", "needs_translation": "false"}, "name": {"english": "Main Rotor", "translation": "Rotore prim.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "ratio": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Collective Pitch Compensation: Increasing will compensate for the pitching motion caused by tail drag when climbing.", "translation": "Compensazione collettiva del pitch: l'aumento compensera' il movimento di pitch causato dal trascinamento della coda durante la salita.", "needs_translation": "false"}, "help_p2": {"english": "Cross Coupling Gain: Removes roll coupling when only elevator is applied.", "translation": "Guadagno cross coupling: rimuove l'accoppiamento di rollio quando viene applicato solo l'elevatore.", "needs_translation": "false"}, "help_p3": {"english": "Cross Coupling Ratio: Amount of compensation (pitch vs roll) to apply.", "translation": "Cross Coupling Ratio: Quantita di compensazione (pitch vs roll) da applicare.", "needs_translation": "false"}}, "sbusout": {"title": {"english": "SBUS Output", "translation": "SBUS Output", "needs_translation": "false"}, "help_fields_source": {"english": "Source id for the mix, counting from 0-15.", "translation": "id sorgente per il mix, contate da 0-15.", "needs_translation": "false"}, "help_default_p4": {"english": "- For motors, use 0, 1000.", "translation": "- Per motori, usa 0, 1000.", "needs_translation": "false"}, "ch_prefix": {"english": "CH", "translation": "CH", "needs_translation": "false"}, "channel_prefix": {"english": "CHANNEL ", "translation": "CANALE ", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "SBUS Out", "translation": "SBUS Out", "needs_translation": "false"}, "channel_page": {"english": "Sbus out / CH", "translation": "Sbus out / CH", "needs_translation": "false"}, "receiver": {"english": "Receiver", "translation": "Ricevente", "needs_translation": "false"}, "servo": {"english": "Servo", "translation": "Servo", "needs_translation": "false"}, "type": {"english": "Type", "translation": "Tipo", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "Salva dati...", "needs_translation": "false"}, "help_fields_max": {"english": "The maximum pwm value to send", "translation": "Valore PWM massimo da inviare", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "Motore", "needs_translation": "false"}, "help_default_p5": {"english": "- Or you can customize your own mapping.", "translation": "- Oppure puoi personalizzare la mappatura.", "needs_translation": "false"}, "help_default_p1": {"english": "Configure advanced mixing and channel mapping if you have SBUS Out enabled on a serial port.", "translation": "Configurare la miscelazione avanzata e la mappatura dei canali se si dispone di SBUS Out abilitato su una porta seriale.", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "<PERSON><PERSON>e la pagina corrente sul controller di volo?", "needs_translation": "false"}, "help_fields_min": {"english": "The minimum pwm value to send.", "translation": "Il valore pwm minimo da inviare.", "needs_translation": "false"}, "mixer": {"english": "Mixer", "translation": "Mixer", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "CANCELLA", "needs_translation": "false"}, "help_default_p2": {"english": "- For RX channels or servos (wideband), use 1000, 2000 or 500,1000 for narrow band servos.", "translation": "- Per i canali RX o i servi (banda larga), utilizzare 1000, 2000 o 500,1000 per i servi a banda stretta.", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "min": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "help_default_p3": {"english": "- For mixer rules, use -1000, 1000.", "translation": "- Per le regole del mixer, usa -1000, 1000.", "needs_translation": "false"}, "source": {"english": "Source", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}}, "profile_rescue": {"help_p4": {"english": "Hover: How much collective to maintain a steady hover.", "translation": "Hover: Quanto collettivo per mantenere un volo stazionario costante.", "needs_translation": "false"}, "hover": {"english": "Hover", "translation": "Hover", "needs_translation": "false"}, "collective": {"english": "Collective", "translation": "Collettivo", "needs_translation": "false"}, "help_p2": {"english": "Pull-up: How much collective and for how long to arrest the fall.", "translation": "Pull-up: Quanto collettivo e per quanto tempo arrestare la caduta.", "needs_translation": "false"}, "climb": {"english": "Climb", "translation": "Sali", "needs_translation": "false"}, "mode_enable": {"english": "Rescue mode enable", "translation": "Abilita <PERSON>", "needs_translation": "false"}, "help_p3": {"english": "Climb: How much collective to maintain a steady climb - and how long.", "translation": "Salita: quanto collettivo per mantenere una salita costante e per quanto tempo.", "needs_translation": "false"}, "help_p1": {"english": "Flip to upright: Flip the heli upright when rescue is activated.", "translation": "Capovolgi in posizione verticale: capovolgi l'elicottero in posizione verticale quando viene attivato il soccorso.", "needs_translation": "false"}, "flip_upright": {"english": "Flip to upright", "translation": "Capovolgere in posizione verticale", "needs_translation": "false"}, "flip": {"english": "Flip", "translation": "Flip", "needs_translation": "false"}, "level_gain": {"english": "Level", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "Rescue", "translation": "Soccorso", "needs_translation": "false"}, "exit_time": {"english": "Exit time", "translation": "Tempo di Uscita", "needs_translation": "false"}, "help_p5": {"english": "Flip: How long to wait before aborting because the flip did not work.", "translation": "Flip:Quanto tempo aspettare prima di interrompere perché il ribaltamento non ha funzionato.", "needs_translation": "false"}, "help_p6": {"english": "Gains: How hard to fight to keep heli level when engaging rescue mode.", "translation": "G<PERSON>ag<PERSON>: Quanto e' arduo mantenere l'elicottero in piano quando si attiva la modalita' di salvataggio.", "needs_translation": "false"}, "fail_time": {"english": "Fail time", "translation": "Tempo di Fallimento", "needs_translation": "false"}, "pull_up": {"english": "Pull-up", "translation": "Pull-up", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "help_p7": {"english": "Rate and Accel: Max rotation and acceleration rates when leveling during rescue.", "translation": "Velocita' e accelerazione: velocita' massima di rotazione e accelerazione durante il livellamento durante il soccorso.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Tempo", "needs_translation": "false"}, "accel": {"english": "Accel", "translation": "Accel", "needs_translation": "false"}}, "trim": {"disable_mixer_message": {"english": "Return control of the servos to the flight controller.", "translation": "Riportare il controllo dei servocomandi al controller di volo.", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Motor idle  %", "translation": "Minimo rotore coda  %", "needs_translation": "false"}, "disable_mixer_override": {"english": "Disable mixer override", "translation": "Disabilita mixer override", "needs_translation": "false"}, "yaw_trim": {"english": "Yaw. trim %", "translation": "Yaw. trim %", "needs_translation": "false"}, "enable_mixer_message": {"english": "Set all servos to their configured center position. \r\n\r\nThis will result in all values on this page being saved when adjusting the servo trim.", "translation": "Impostare tutti i servocomandi nella posizione centrale configurata. In questo modo, tutti i valori di questa pagina verranno salvati quando si regola il trim del servocomando.", "needs_translation": "false"}, "mixer_override_disabling": {"english": "Disabling mixer override...", "translation": "Disabilita mixer override...", "needs_translation": "false"}, "roll_trim": {"english": "Roll trim %", "translation": "Roll trim %", "needs_translation": "false"}, "pitch_trim": {"english": "Pitch trim %", "translation": "Pitch trim %", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Motorised tail: If using a motorised tail, use this to set the minimum idle speed and zero yaw.", "translation": "Coda motorizzata: se si utilizza una coda motorizzata, impostarla per il minimo e l'imbardata zero.", "needs_translation": "false"}, "mixer_override": {"english": "Mixer Override", "translation": "Mixer Override", "needs_translation": "false"}, "mixer_override_enabling": {"english": "Enabling mixer override...", "translation": "Abilita mixer override...", "needs_translation": "false"}, "enable_mixer_override": {"english": "Enable mixer override", "translation": "Abilita mixer override", "needs_translation": "false"}, "collective_trim": {"english": "Col. trim %", "translation": "Col. trim %", "needs_translation": "false"}, "help_p1": {"english": "Link trims: Use to trim out small leveling issues in your swash plate. Typically only used if the swash links are non-adjustable.", "translation": "Link trims: <PERSON><PERSON><PERSON><PERSON><PERSON> per correggere piccoli problemi di livellamento nel piatto oscillante. In genere utilizzato solo se i link oscillanti non sono regolabili.", "needs_translation": "false"}}, "governor": {"help_p1": {"english": "These parameters apply globally to the governor regardless of the profile in use.", "translation": "Questi parametri si applicano globalmente al regolatore, indipendentemente dal profilo in uso.", "needs_translation": "false"}, "handover_throttle": {"english": "Handover throttle%", "translation": "Trasferimento acceleratore%", "needs_translation": "false"}, "spoolup_min_throttle": {"english": "Spoolup min throttle%", "translation": "Rampa di accelerazione % min", "needs_translation": "false"}, "recovery_time": {"english": "Recovery time", "translation": "Tempo Recupero", "needs_translation": "false"}, "mode": {"english": "Mode", "translation": "Modo", "needs_translation": "false"}, "help_p2": {"english": "Each parameter is simply a time value in seconds for each governor action.", "translation": "Ogni parametro e' semplicemente un valore temporale in secondi per ogni azione del regolatore.", "needs_translation": "false"}, "tracking_time": {"english": "Tracking time", "translation": "Tempo Tracciatura", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "startup_time": {"english": "Startup time", "translation": "Tempo Startup", "needs_translation": "false"}, "spoolup_time": {"english": "Spoolup time", "translation": "Tempo Rampa di accelerazione", "needs_translation": "false"}}, "accelerometer": {"help_p1": {"english": "The accelerometer is used to measure the angle of the flight controller in relation to the horizon. This data is used to stabilize the aircraft and provide self-leveling functionality.", "translation": "L'accelerometro e' utilizzato per misurare l'angolo del controller di volo rispetto all'orizzonte. Questi dati sono utilizzati per stabilizzare l'aeromobile e fornire la funzionalita' di autolivellamento.", "needs_translation": "false"}, "name": {"english": "Accelerometer", "translation": "Accelerometro", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "msg_calibrate": {"english": "Calibrate the accelerometer?", "translation": "Calibra accelerometro?", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}}, "rates": {"help_table_5_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Velocita' massima: velocita' di rotazione massima alla massima escursione dello stick in gradi al secondo.", "needs_translation": "false"}, "actual": {"english": "ACTUAL", "translation": "ATTUALE", "needs_translation": "false"}, "max_rate": {"english": "Max Rate", "translation": "Max Rate", "needs_translation": "false"}, "help_table_4_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: riduce la sensibilita' vicino al centro del joystick, dove sono necessari controlli precisi.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "help_table_5_p1": {"english": "RC Rate: Use to reduce sensitivity around center stick. RC Rate set to one half of the Max Rate is linear. A lower number will reduce sensitivity around center stick. Higher than one half of the Max Rate will also increase the Max Rate.", "translation": "Rate RC: riduce la sensibilita' intorno al joystick centrale. I rate RC impostata a meta' della velocita' massima e' lineare. Un numero piu' basso ridurra' la sensibilita' intorno al joystick centrale. Un numero superiore alla meta' della velocita' massima aumentera' anche la velocita' massima.", "needs_translation": "false"}, "help_table_4_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Max Rate: <PERSON><PERSON> massimo di rotazione alla deflessione piena dello stick in gradi secondo.", "needs_translation": "false"}, "center_sensitivity": {"english": "Cntr. Sens.", "translation": "Cntr. Sens.", "needs_translation": "false"}, "rc_curve": {"english": "RC Curve", "translation": "Curva RC", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "none": {"english": "NONE", "translation": "NIENTE", "needs_translation": "false"}, "betaflight": {"english": "BETAFLIGHT", "translation": "BETAFLIGHT", "needs_translation": "false"}, "kiss": {"english": "KISS", "translation": "KISS", "needs_translation": "false"}, "help_table_1_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: R<PERSON>uce la sensibilita' vicino al centro del joystick, dove sono necessari controlli precisi.", "needs_translation": "false"}, "help_table_3_p2": {"english": "Rate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "Rate: Aumenta la velocita' di rotazione massima riducendo la sensibilita' intorno alla meta' del joystick.", "needs_translation": "false"}, "help_table_2_p2": {"english": "Acro+: Increases the maximum rotation rate while reducing sensitivity around half stick.", "translation": "Acro+: Aumenta la velocita' di rotazione massima riducendo la sensibilita' intorno alla meta' del joystick.", "needs_translation": "false"}, "superrate": {"english": "SuperRate", "translation": "SuperRate", "needs_translation": "false"}, "help_table_2_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: R<PERSON>uce la sensibilita' vicino al centro del joystick, dove sono necessari controlli precisi.", "needs_translation": "false"}, "raceflight": {"english": "RACEFLIGHT", "translation": "RACEFLIGHT", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "collective": {"english": "Col", "translation": "Col", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "Rates", "needs_translation": "false"}, "help_table_5_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: R<PERSON>uce la sensibilita' vicino al centro del joystick, dove sono necessari controlli precisi.", "needs_translation": "false"}, "help_table_3_p3": {"english": "RC Curve: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "RC Curve: <PERSON><PERSON><PERSON> la sensibilita' vicino al centro del joystick, dove sono necessari controlli precisi.", "needs_translation": "false"}, "expo": {"english": "Expo", "translation": "Expo", "needs_translation": "false"}, "help_table_1_p2": {"english": "SuperRate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "SuperRate: Aumenta la velocita' di rotazione massima riducendo la sensibilita' intorno alla meta' del joystick.", "needs_translation": "false"}, "help_default_p2": {"english": "We will use the sub keys below.", "translation": "Useremo le sottoclassificazioni seguenti.", "needs_translation": "false"}, "help_default_p1": {"english": "Default: We keep this to make button appear for rates.", "translation": "Default: Manteniamo questo per far apparire il pulsante per le escursioni.", "needs_translation": "false"}, "quick": {"english": "QUICK", "translation": "VELOCE", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "acroplus": {"english": "Acro+", "translation": "Acro+", "needs_translation": "false"}, "help_table_1_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "RC Rate: Velocita' di rotazione massima a piena deflessione dello stick.", "needs_translation": "false"}, "rc_rate": {"english": "RC Rate", "translation": "RC Rate", "needs_translation": "false"}, "help_table_2_p1": {"english": "Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Rate: Velocita' di rotazione massima a piena deflessione dello stick in gradi secondo.", "needs_translation": "false"}, "help_table_4_p1": {"english": "Center Sensitivity: Use to reduce sensitivity around center stick. Set Center Sensitivity to the same as Max Rate for a linear response. A lower number than Max Rate will reduce sensitivity around center stick. Note that higher than Max Rate will increase the Max Rate - not recommended as it causes issues in the Blackbox log.", "translation": "Sensibilita' al centro: da utilizzare per ridurre la sensibilita' attorno alla leva centrale. Impostare la sensibilita' al centro allo stesso valore della velocita' massima per una risposta lineare. Un numero inferiore alla velocita' massima ridurra' la sensibilita' attorno alla leva centrale. Si noti che un valore superiore alla velocita' massima aumentera' la velocita' massima - non raccomandato in quanto causa problemi nel log della Blackbox.", "needs_translation": "false"}, "help_table_0_p1": {"english": "All values are set to zero because no RATE TABLE is in use.", "translation": "Tutti i valori sono impostati su zero perché non e' in uso alcuna TABELLA DI ESCURSIONI.", "needs_translation": "false"}, "help_table_3_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "RC Rate: Velocita' di rotazione massima a piena deflessione dello Stick.", "needs_translation": "false"}}, "mixer": {"help_p1": {"english": "Adust swash plate geometry, phase angles, and limits.", "translation": "Regolare la geometria del piatto oscillante, gli angoli di fase e i limiti.", "needs_translation": "false"}, "collective_tilt_correction_pos": {"english": "Positive", "translation": "Positivo", "needs_translation": "false"}, "geo_correction": {"english": "Geo Correction", "translation": "Correzione Geo", "needs_translation": "false"}, "swash_tta_precomp": {"english": "TTA Precomp", "translation": "TTA Precomp", "needs_translation": "false"}, "name": {"english": "Mixer", "translation": "Mixer", "needs_translation": "false"}, "collective_tilt_correction_neg": {"english": "Negative", "translation": "Negativo", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Idle Thr%", "translation": "Minimo Thr coda%", "needs_translation": "false"}, "swash_phase": {"english": "Phase Angle", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "collective_tilt_correction": {"english": "Collective Tilt Correction", "translation": "Correzione dell'inclinazione collettiva", "needs_translation": "false"}, "swash_pitch_limit": {"english": "Total Pitch Limit", "translation": "Limite Totale Pitch", "needs_translation": "false"}}, "about": {"help_p1": {"english": "This page provides some useful information that you may be asked for when requesting support.", "translation": "Questa pagina fornisce alcune informazioni utili che potrebbero esserti richieste quando richiedi assistenza.", "needs_translation": "false"}, "msgbox_credits": {"english": "Credits", "translation": "Crediti", "needs_translation": "false"}, "ethos_version": {"english": "Ethos Version", "translation": "Versione Ethos", "needs_translation": "false"}, "rf_version": {"english": "Rotorflight Version", "translation": "Versione Rotorflight", "needs_translation": "false"}, "fc_version": {"english": "FC Version", "translation": "Versione FC", "needs_translation": "false"}, "name": {"english": "About", "translation": "A Proposito", "needs_translation": "false"}, "supported_versions": {"english": "Supported MSP Versions", "translation": "Versioni MSP Supportate", "needs_translation": "false"}, "license": {"english": "You may copy, distribute, and modify the software as long as you track changes/dates in source files. Any modifications to or software including (via compiler) GPL-licensed code must also be made available under the GPL along with build & install instructions.", "translation": "e' possibile copiare, distribuire e modificare il software a condizione di tenere traccia delle modifiche/date nei file sorgente. Qualsiasi modifica o software che includa (tramite compilatore) codice con licenza GPL deve essere reso disponibile anche sotto la GPL insieme alle istruzioni di compilazione e installazione.", "needs_translation": "false"}, "simulation": {"english": "Simulation", "translation": "Simulazione", "needs_translation": "false"}, "help_p2": {"english": "For support, please first read the help pages on www.rotorflight.org", "translation": "<PERSON>, leggere prima le pagine di aiuto su www.rotorflight.org", "needs_translation": "false"}, "opener": {"english": "Rotorflight is an open source project. Contribution from other like minded people, keen to assist in making this software even better, is welcomed and encouraged. You do not have to be a hardcore programmer to help.", "translation": "Rotorflight e' un progetto open source. Il contributo di altre persone che condividono la stessa mentalita' e che desiderano contribuire a migliorare ulteriormente questo software e' ben accetto e incoraggiato. Non e' necessario essere un programmatore esperto per dare una mano.", "needs_translation": "false"}, "version": {"english": "Version", "translation": "Versione", "needs_translation": "false"}, "msp_version": {"english": "MSP Version", "translation": "Versione MSP", "needs_translation": "false"}, "credits": {"english": "Notable contributors to both the Rotorflight firmware and this software are: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... and many more who have spent hours testing and providing feedback!", "translation": "I principali contributori sia al firmware Rotorflight che a questo software sono: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... e molti altri che hanno trascorso ore a Rotorere e fornire feedback.!", "needs_translation": "false"}, "msp_transport": {"english": "MSP Transport", "translation": "Trasporto MSP", "needs_translation": "false"}}, "rates_advanced": {"dyn_ceiling_gain": {"english": "Dynamic ceiling gain", "translation": "Guadagno soglia Dinamico", "needs_translation": "false"}, "acc_limit": {"english": "Accelerometer Limit", "translation": "Limite Accelerometro", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "yaw_dynamics": {"english": "Yaw dynamics", "translation": "Dinamiche Yaw", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "col": {"english": "Col", "translation": "Col", "needs_translation": "false"}, "setpoint_boost_cutoff": {"english": "Setpoint boost cutoff", "translation": "Punto di taglio boost", "needs_translation": "false"}, "yaw_dynamic_deadband_gain": {"english": "D. <PERSON>", "translation": "D. <PERSON>", "needs_translation": "false"}, "rates_type": {"english": "Rates Type", "translation": "Tipo Rates", "needs_translation": "false"}, "setpoint_boost_gain": {"english": "Setpoint boost gain", "translation": "Guadagno punto boost", "needs_translation": "false"}, "msg_reset_to_defaults": {"english": "Rate type changed. Values will be reset to defaults.", "translation": "Tipo Rate variato. I Valori saranno riportati a Default.", "needs_translation": "false"}, "yaw_dynamic_ceiling_gain": {"english": "Ceiling", "translation": "Tetto/Soglia", "needs_translation": "false"}, "yaw_boost": {"english": "Yaw boost", "translation": "Yaw boost", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "Guadagno", "needs_translation": "false"}, "rate_table": {"english": "Rate Table", "translation": "Tavola Rate", "needs_translation": "false"}, "dynamics": {"english": "Dynamics", "translation": "Dinamiche", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "yaw_dynamic_deadband_filter": {"english": "Filter", "translation": "Filtro", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "Rates", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "help_rate_table": {"english": "Please select the rate you would like to use. Saving will apply the choice to the active profile.", "translation": "Selezionare la tariffa che si desidera utilizzare. Salvando si applichera' la scelta al profilo attivo.", "needs_translation": "false"}, "help_p1": {"english": "Rates type: Choose the rate type you prefer flying with. Raceflight and Actual are the most straightforward.", "translation": "Tipo di rateo: Scegli il tipo di rateo con cui preferisci volare. Raceflight e Actual sono i piu' semplici.", "needs_translation": "false"}, "pitch_boost": {"english": "Pitch boost", "translation": "Pitch boost", "needs_translation": "false"}, "help_p2": {"english": "Dynamics: Applied regardless of rates type. Typically left on defaults but can be adjusted to smooth heli movements, like with scale helis.", "translation": "Dinamica: applicata indipendentemente dal tipo di curve. In genere lasciata sui valori predefiniti, ma puo' essere regolata per smussare i movimenti dell'elicottero, come con gli elicotteri in scala.", "needs_translation": "false"}, "accel_limit": {"english": "Accel", "translation": "Accel", "needs_translation": "false"}, "dyn_deadband_filter": {"english": "Dynamic deadband filter", "translation": "Filtro Dinamico deadband", "needs_translation": "false"}, "roll_boost": {"english": "Roll boost", "translation": "Roll boost", "needs_translation": "false"}, "dyn_deadband_gain": {"english": "Dynamic deadband gain", "translation": "Guadagno deadband dinamica", "needs_translation": "false"}, "collective_dynamics": {"english": "Collective dynamics", "translation": "Dinamica Collettivo", "needs_translation": "false"}, "roll_dynamics": {"english": "Roll dynamics", "translation": "Dinamica Roll", "needs_translation": "false"}, "collective_boost": {"english": "Collective boost", "translation": "Boost Collettivo", "needs_translation": "false"}, "pitch_dynamics": {"english": "Pitch dynamics", "translation": "Dinamica Pitch", "needs_translation": "false"}, "response_time": {"english": "Response Time", "translation": "Tempoo Risposta", "needs_translation": "false"}}, "servos": {"tbl_yes": {"english": "YES", "translation": "SI", "needs_translation": "false"}, "enable_servo_override": {"english": "Enable servo override", "translation": "Abilita servo override", "needs_translation": "false"}, "disabling_servo_override": {"english": "Disabling servo override...", "translation": "Disabilita servo override...", "needs_translation": "false"}, "help_tool_p3": {"english": "Minimum/Maximum: Adjust the end points of the selected servo.", "translation": "Minimo/Massimo: Regola il finecorsa del servo selezionato.", "needs_translation": "false"}, "tail": {"english": "TAIL", "translation": "CODA", "needs_translation": "false"}, "scale_negative": {"english": "Scale Negative", "translation": "Scala Negativa", "needs_translation": "false"}, "help_tool_p1": {"english": "Override: [*] Enable override to allow real-time updates of servo center point.", "translation": "Override: [*] <PERSON><PERSON><PERSON> override per permettere aggiornamento in tempo reale del centro del servo.", "needs_translation": "false"}, "tbl_no": {"english": "NO", "translation": "NO", "needs_translation": "false"}, "maximum": {"english": "Maximum", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_tool_p6": {"english": "Speed: The speed the servo moves. Generally only used for the cyclic servos to help the swash move evenly. Optional - leave all at 0 if unsure.", "translation": "Velocita': La velocita' con cui si muove il servo. Generalmente viene usata solo per i servo ciclici per aiutare il piatto ciclico a muoversi in modo uniforme. Opzionale: lasciare tutto a 0 se non si e' sicuri.", "needs_translation": "false"}, "help_fields_rate": {"english": "Servo PWM rate.", "translation": "Curva PWM Servo.", "needs_translation": "false"}, "cyc_pitch": {"english": "CYC.PITCH", "translation": "CYC.PITCH", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Centro", "needs_translation": "false"}, "minimum": {"english": "Minimum", "translation": "Minimo", "needs_translation": "false"}, "speed": {"english": "Speed", "translation": "Velocita'", "needs_translation": "false"}, "help_fields_speed": {"english": "Servo motion speed in milliseconds.", "translation": "Velocita' movimento Servo in millisecondi.", "needs_translation": "false"}, "disable_servo_override": {"english": "Disable servo override", "translation": "Disabilita servo override", "needs_translation": "false"}, "help_fields_scale_pos": {"english": "Servo positive scaling.", "translation": "Scalatura Positiva Servo.", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "Salva dati...", "needs_translation": "false"}, "cyc_left": {"english": "CYC.LEFT", "translation": "CYC.LEFT", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "Servos", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_tool_p5": {"english": "Rate: The frequency the servo runs best at - check with manufacturer.", "translation": "Rateo: La frequenza alla quale il servo funziona meglio - verificare con il produttore.", "needs_translation": "false"}, "help_tool_p2": {"english": "Center: Adjust the center position of the servo.", "translation": "Centro: Regola la posizione della meta' corsa del Servo.", "needs_translation": "false"}, "enabling_servo_override": {"english": "Enabling servo override...", "translation": "Abilita servo override...", "needs_translation": "false"}, "servo_prefix": {"english": "SERVO ", "translation": "SERVO ", "needs_translation": "false"}, "reverse": {"english": "Reverse", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "enable_servo_override_msg": {"english": "Servo override allows you to 'trim' your servo center point in real time.", "translation": "Override del Servo permette di trimmare la meta' corsa in tempo reale.", "needs_translation": "false"}, "cyc_right": {"english": "CYC.RIGHT", "translation": "CYC.RIGHT", "needs_translation": "false"}, "help_default_p2": {"english": "Primary flight controls that use the rotorflight mixer will display in the section called 'mixer'.", "translation": "I controlli di volo primari che utilizzano il mixer di volo del rotore verranno visualizzati nella sezione denominata “mixer”.", "needs_translation": "false"}, "scale_positive": {"english": "Scale Positive", "translation": "Scala Positiva", "needs_translation": "false"}, "help_default_p1": {"english": "Please select the servo you would like to configure from the list below.", "translation": "Selezionare il servo che si desidera configurare dall'elenco sottostante.", "needs_translation": "false"}, "servo_override": {"english": "Servo Override", "translation": "Servo Override", "needs_translation": "false"}, "disable_servo_override_msg": {"english": "Return control of the servos to the flight controller.", "translation": "Riportare il controllo dei servocomandi al controller di volo.", "needs_translation": "false"}, "help_fields_min": {"english": "Servo negative travel limit.", "translation": "Limite corsa negativo del Servo.", "needs_translation": "false"}, "help_default_p3": {"english": "Any other servos that are not controlled by the primary flight mixer will be displayed in the section called 'Other servos'.", "translation": "<PERSON>tti gli altri servi che non sono controllati dal mixer di volo primario saranno visualizzati nella sezione denominata “Altri servi”.", "needs_translation": "false"}, "help_fields_mid": {"english": "Servo center position pulse width.", "translation": "Larghezza impulso posizione centrale servo.", "needs_translation": "false"}, "help_fields_scale_neg": {"english": "Servo negative scaling.", "translation": "Scalatura negativa Servo.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Valori", "needs_translation": "false"}, "help_tool_p4": {"english": "Scale: Adjust the amount the servo moves for a given input.", "translation": "Scala: regola la quantita' di movimento del servo per un dato input.", "needs_translation": "false"}, "help_fields_flags": {"english": "0 = <PERSON><PERSON><PERSON>, 1=Reverse, 2 = Geo Correction, 3 = Reverse + Geo Correction", "translation": "0 = <PERSON><PERSON><PERSON>, 1=Inverti, 2 = Correzione Geo, 3 = Inverti + Correzione Geo", "needs_translation": "false"}, "geometry": {"english": "Geometry", "translation": "Geometria", "needs_translation": "false"}, "help_fields_max": {"english": "Servo positive travel limit.", "translation": "Limite corsa Servo positiva.", "needs_translation": "false"}}, "profile_autolevel": {"acro_trainer": {"english": "Acro trainer", "translation": "Acro trainer", "needs_translation": "false"}, "angle_mode": {"english": "Angle mode", "translation": "Angle mode", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "name": {"english": "Autolevel", "translation": "Autolivello", "needs_translation": "false"}, "help_p1": {"english": "Acro Trainer: How aggressively the heli tilts back to level when flying in Acro Trainer Mode.", "translation": "Acro Trainer: Quanto aggressivamente l'elicottero quando si inclina all'indietro per livellarsi quando si vola in modalita' Acro Trainer.", "needs_translation": "false"}, "horizon_mode": {"english": "Horizon mode", "translation": "Horizon mode", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "Guadagno", "needs_translation": "false"}, "help_p2": {"english": "Angle Mode: How aggressively the heli tilts back to level when flying in Angle Mode.", "translation": "Angle Mode: Quanto aggressivamente l'elicottero quando si inclina all'indietro per livellarsi quando si vola in modalita' Angle", "needs_translation": "false"}, "help_p3": {"english": "Horizon Mode: How aggressively the heli tilts back to level when flying in Horizon Mode.", "translation": "Horizon Mode: Quanto aggressivamente l'elicottero quando si inclina all'indietro per livellarsi quando si vola in modalita' Horizon", "needs_translation": "false"}}, "filters": {"filter_type": {"english": "Filter type", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p4": {"english": "Dynamic Notch Filters: Automatically creates notch filters within the min and max frequency range.", "translation": "Filtro Notch Dinamico: Crea automaticamente un filtro notch tra il min e il massimo del range di frequenza.", "needs_translation": "false"}, "notch_c": {"english": "Notch Count", "translation": "<PERSON><PERSON>/notch", "needs_translation": "false"}, "rpm_preset": {"english": "Type", "translation": "Tipo", "needs_translation": "false"}, "lowpass_1": {"english": "Lowpass 1", "translation": "Passabasso 1", "needs_translation": "false"}, "rpm_min_hz": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Gyro lowpass: Lowpass filters for the gyro signal. Typically left at default.", "translation": "Passabasso Gyro: i filtri passabasso per il segnale gyro. normalmente lasciati default.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "notch_1": {"english": "Notch 1", "translation": "Notch 1", "needs_translation": "false"}, "max_cutoff": {"english": "Max cutoff", "translation": "Max cutoff", "needs_translation": "false"}, "help_p3": {"english": "Gyro notch filters: Use for filtering specific frequency ranges. Typically not needed in most helis.", "translation": "Filtro Notch Gyro: Utilizzare per filtrare specifiche gamme di frequenza. In genere non necessario nella maggior parte degli elicotteri.", "needs_translation": "false"}, "lowpass_2": {"english": "Lowpass 2", "translation": "Passabasso 2", "needs_translation": "false"}, "rpm_filter": {"english": "RPM filter", "translation": "Filtro RPM", "needs_translation": "false"}, "help_p1": {"english": "Typically you would not edit this page without checking your Blackbox logs!", "translation": "In genere non si modificerebbe questa pagina senza controllare i registri della scatola nera!", "needs_translation": "false"}, "dyn_notch": {"english": "Dynamic Filters", "translation": "Filtro Dinamico", "needs_translation": "false"}, "notch_q": {"english": "Notch Q", "translation": "Notch Q", "needs_translation": "false"}, "lowpass_1_dyn": {"english": "Lowpass 1 dyn.", "translation": "Passabasso 1 dyn.", "needs_translation": "false"}, "notch_min_hz": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "notch_max_hz": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "notch_2": {"english": "Notch 2", "translation": "Notch 2", "needs_translation": "false"}, "name": {"english": "Filters", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "min_cutoff": {"english": "Min cutoff", "translation": "Min cutoff", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Centro", "needs_translation": "false"}}, "status": {"arming_disable_flag_3": {"english": "Bad RX Recovery", "translation": "Recupero RX Fallata", "needs_translation": "false"}, "arming_disable_flag_20": {"english": "RPM Filter", "translation": "Filtro RPM", "needs_translation": "false"}, "arming_disable_flag_11": {"english": "Load", "translation": "Carica", "needs_translation": "false"}, "arming_disable_flag_22": {"english": "DSHOT Bitbang", "translation": "DSHOT Bitbang", "needs_translation": "false"}, "dataflash_free_space": {"english": "Dataflash Free Space", "translation": "Spazio Libero Dataflash", "needs_translation": "false"}, "arming_disable_flag_25": {"english": "Arm Switch", "translation": "Int. Armo", "needs_translation": "false"}, "erasing": {"english": "Erasing", "translation": "Cancellazione", "needs_translation": "false"}, "arming_disable_flag_9": {"english": "Boot <PERSON>", "translation": "Boot <PERSON>", "needs_translation": "false"}, "megabyte": {"english": "MB", "translation": "MB", "needs_translation": "false"}, "arming_disable_flag_17": {"english": "Paralyze", "translation": "Paralizza", "needs_translation": "false"}, "arming_disable_flag_5": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "arming_disable_flag_8": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_1": {"english": "Fail Safe", "translation": "Fail Safe", "needs_translation": "false"}, "cpu_load": {"english": "CPU Load", "translation": "Carico CPU", "needs_translation": "false"}, "arming_disable_flag_15": {"english": "BST", "translation": "BST", "needs_translation": "false"}, "arming_disable_flag_12": {"english": "Calibrating", "translation": "Calibrazione", "needs_translation": "false"}, "arming_disable_flag_19": {"english": "Resc", "translation": "Resc", "needs_translation": "false"}, "arming_disable_flag_4": {"english": "Box Fail Safe", "translation": "Box Fail Safe", "needs_translation": "false"}, "arming_disable_flag_24": {"english": "Motor Protocol", "translation": "Protocollo Motore", "needs_translation": "false"}, "real_time_load": {"english": "Real-time Load", "translation": "Caricamento in Tempo Reale", "needs_translation": "false"}, "help_p2": {"english": "To erase the dataflash for more log file storage, press the button on the menu denoted by a '*'.", "translation": "Per cancellare il dataflash e avere piu' spazio per i file di registro, premere il pulsante sul menu contrassegnato da un '*'.", "needs_translation": "false"}, "arming_disable_flag_2": {"english": "RX Fail Safe", "translation": "RX Fail Safe", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "arming_disable_flag_0": {"english": "No Gyro", "translation": "No Gyro", "needs_translation": "false"}, "arming_disable_flag_18": {"english": "GPS", "translation": "GPS", "needs_translation": "false"}, "help_p1": {"english": "Use this page to view your current flight controller status. This can be useful when determining why your heli will not arm.", "translation": "Utilizza questa pagina per visualizzare lo stato attuale del tuo controller di volo. Questo puo' essere utile per determinare perché il tuo elicottero non si arma.", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Flags di Armo", "needs_translation": "false"}, "unsupported": {"english": "Unsupported", "translation": "Non supportato", "needs_translation": "false"}, "erase_prompt": {"english": "Would you like to erase the dataflash?", "translation": "Vuoi Cancellare la dataflash?", "needs_translation": "false"}, "erase": {"english": "Erase", "translation": "Can<PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_10": {"english": "No Pre Arm", "translation": "No Pre Arm", "needs_translation": "false"}, "arming_disable_flag_21": {"english": "Reboot Required", "translation": "Reboot <PERSON>", "needs_translation": "false"}, "name": {"english": "Status", "translation": "Stato", "needs_translation": "false"}, "arming_disable_flag_13": {"english": "CLI", "translation": "CLI", "needs_translation": "false"}, "arming_disable_flag_14": {"english": "CMS Menu", "translation": "CMS Menu", "needs_translation": "false"}, "arming_disable_flag_16": {"english": "MSP", "translation": "MSP", "needs_translation": "false"}, "arming_disable_flag_7": {"english": "<PERSON>hrottle", "translation": "Gas-Accelleratore", "needs_translation": "false"}, "erasing_dataflash": {"english": "Erasing dataflash...", "translation": "Cancello dataflash...", "needs_translation": "false"}, "arming_disable_flag_23": {"english": "Acc Calibration", "translation": "Calibrazione Acc", "needs_translation": "false"}}, "profile_pidbandwidth": {"help_p1": {"english": "PID Bandwidth: Overall bandwidth in HZ used by the PID loop.", "translation": "Banda PID: <PERSON>rg<PERSON><PERSON> di banda complessiva in HZ utilizzata dal circuito PID", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "name": {"english": "PID Bandwidth", "translation": "PID Bandwidth", "needs_translation": "false"}, "bterm_cutoff": {"english": "B-term cut-off", "translation": "B-term cut-off", "needs_translation": "false"}, "help_p3": {"english": "B-term cutoff: B-term cutoff frequency in HZ.", "translation": "B-term cutoff: Frequenza B-term cutoff in HZ.", "needs_translation": "false"}, "dterm_cutoff": {"english": "D-term cut-off", "translation": "D-term cut-off", "needs_translation": "false"}, "help_p2": {"english": "D-term cutoff: D-term cutoff frequency in HZ.", "translation": "D-term cutoff: Frequenza D-term cutoff in HZ.", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}}}, "navigation_save": {"english": "SAVE", "translation": "SALVA", "needs_translation": "false"}, "menu_section_flight_tuning": {"english": "Flight Tuning", "translation": "Tuning Volo", "needs_translation": "false"}, "error_timed_out": {"english": "Error: timed out", "translation": "Errore: fuori tempo max", "needs_translation": "false"}, "check_rf_module_on": {"english": "Please check your rf module is turned on.", "translation": "Prego accertarsi di aver acceso il modulo rf.", "needs_translation": "false"}, "msg_saving": {"english": "Saving...", "translation": "Salva...", "needs_translation": "false"}, "msg_save_not_commited": {"english": "Save not committed to EEPROM", "translation": "Salva non commisionato alla EEPROM", "needs_translation": "false"}, "menu_section_advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "msg_loading_from_fbl": {"english": "Loading data from flight controller...", "translation": "<PERSON><PERSON> dati dal flight controller...", "needs_translation": "false"}, "msg_reload_settings": {"english": "Reload data from flight controller?", "translation": "<PERSON><PERSON><PERSON><PERSON> dati dal flight controller?", "needs_translation": "false"}, "menu_section_tools": {"english": "Tools", "translation": "Strumenti", "needs_translation": "false"}, "msg_connecting": {"english": "Connecting", "translation": "Connessione in corso", "needs_translation": "false"}, "msg_save_current_page": {"english": "Save current page to flight controller?", "translation": "<PERSON><PERSON> la pagina attuale nel flight controller?", "needs_translation": "false"}, "btn_ok_long": {"english": "                OK                ", "translation": "                OK                ", "needs_translation": "false"}, "check_discovered_sensors": {"english": "Please check you have discovered all sensors.", "translation": "Assicurati di aver trovato tutti i sensori.", "needs_translation": "false"}, "msg_loading": {"english": "Loading...", "translation": "Carico...", "needs_translation": "false"}, "check_heli_on": {"english": "Please check your heli is powered up and radio connected.", "translation": "Assicurati che la radiosia connessa e l'elicottero sia acceso.", "needs_translation": "false"}, "check_bg_task": {"english": "Please enable the background task.", "translation": "Abilita i Task Background per favore.", "needs_translation": "false"}, "navigation_tools": {"english": "*", "translation": "*", "needs_translation": "false"}, "check_supported_version": {"english": "This version of the Lua script \ncan't be used with the selected model", "translation": "Questa Versione del Lua Script \nnon puo' essere usata con il modello selezionato", "needs_translation": "false"}}