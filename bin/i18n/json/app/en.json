{"btn_ok": {"english": "          OK           ", "translation": "          OK           ", "needs_translation": "false"}, "btn_close": {"english": "CLOSE", "translation": "CLOSE", "needs_translation": "false"}, "navigation_menu": {"english": "MENU", "translation": "MENU", "needs_translation": "false"}, "menu_section_hardware": {"english": "Hardware", "translation": "Hardware", "needs_translation": "false"}, "msg_please_disarm_to_save_warning": {"english": "Settings will only be saved to e<PERSON>rom on disarm", "translation": "Settings will only be saved to e<PERSON>rom on disarm", "needs_translation": "false"}, "msg_saving_settings": {"english": "Saving settings...", "translation": "Saving settings...", "needs_translation": "false"}, "msg_saving_to_fbl": {"english": "Saving data to flight controller...", "translation": "Saving data to flight controller...", "needs_translation": "false"}, "navigation_reload": {"english": "RELOAD", "translation": "RELOAD", "needs_translation": "false"}, "menu_section_developer": {"english": "Developer", "translation": "Developer", "needs_translation": "false"}, "check_msp_version": {"english": "Unable to determine MSP version in use.", "translation": "Unable to determine MSP version in use.", "needs_translation": "false"}, "menu_section_about": {"english": "About", "translation": "About", "needs_translation": "false"}, "msg_please_disarm_to_save": {"english": "Please disarm to save", "translation": "Please disarm to save", "needs_translation": "false"}, "unit_hertz": {"english": "Hz", "translation": "Hz", "needs_translation": "false"}, "msg_rebooting": {"english": "Rebooting...", "translation": "Rebooting...", "needs_translation": "false"}, "msg_save_settings": {"english": "Save settings", "translation": "Save settings", "needs_translation": "false"}, "btn_cancel": {"english": "CANCEL", "translation": "CANCEL", "needs_translation": "false"}, "msg_connecting_to_fbl": {"english": "Connecting to flight controller...", "translation": "Connecting to flight controller...", "needs_translation": "false"}, "navigation_help": {"english": "?", "translation": "?", "needs_translation": "false"}, "modules": {"stats": {"name": {"english": "Stats", "translation": "Stats", "needs_translation": "false"}, "totalflighttime": {"english": "Total Flight Time", "translation": "Total Flight Time", "needs_translation": "false"}, "flightcount": {"english": "Flight Count", "translation": "Flight Count", "needs_translation": "false"}, "lastflighttime": {"english": "Last Flight Time", "translation": "Last Flight Time", "needs_translation": "false"}, "help_p1": {"english": "Use this module to update the recorded flight statistics on the flight controller.", "translation": "Use this module to update the recorded flight statistics on the flight controller.", "needs_translation": "false"}}, "settings": {"name": {"english": "Settings", "translation": "Settings", "needs_translation": "false"}, "no_themes_available_to_configure": {"english": "No configurable themes installed on this device", "translation": "No configurable themes installed on this device", "needs_translation": "true"}, "txt_audio_timer": {"english": "Timer", "translation": "Timer", "needs_translation": "false"}, "txt_audio_events": {"english": "Events", "translation": "Events", "needs_translation": "false"}, "txt_audio_switches": {"english": "Switches", "translation": "Switches", "needs_translation": "false"}, "txt_iconsize": {"english": "Icon Size", "translation": "Icon Size", "needs_translation": "false"}, "txt_general": {"english": "General", "translation": "General", "needs_translation": "false"}, "txt_text": {"english": "TEXT", "translation": "TEXT", "needs_translation": "false"}, "txt_small": {"english": "SMALL", "translation": "SMALL", "needs_translation": "false"}, "txt_large": {"english": "LARGE", "translation": "LARGE", "needs_translation": "false"}, "txt_syncname": {"english": "Sync model name", "translation": "Sync model name", "needs_translation": "false"}, "txt_devtools": {"english": "Developer Tools", "translation": "Developer Tools", "needs_translation": "false"}, "txt_apiversion": {"english": "API Version", "translation": "API Version", "needs_translation": "false"}, "txt_logging": {"english": "Logging", "translation": "Logging", "needs_translation": "false"}, "txt_compilation": {"english": "Compilation", "translation": "Compilation", "needs_translation": "false"}, "txt_loglocation": {"english": "Log location", "translation": "Log location", "needs_translation": "false"}, "txt_console": {"english": "CONSOLE", "translation": "CONSOLE", "needs_translation": "false"}, "txt_consolefile": {"english": "CONSOLE & FILE", "translation": "CONSOLE & FILE", "needs_translation": "false"}, "txt_loglevel": {"english": "Log level", "translation": "Log level", "needs_translation": "false"}, "txt_off": {"english": "OFF", "translation": "OFF", "needs_translation": "false"}, "txt_info": {"english": "INFO", "translation": "INFO", "needs_translation": "false"}, "txt_debug": {"english": "DEBUG", "translation": "DEBUG", "needs_translation": "false"}, "txt_mspdata": {"english": "Log msp data", "translation": "Log msp data", "needs_translation": "false"}, "txt_queuesize": {"english": "Log MSP queue size", "translation": "Log MSP queue size", "needs_translation": "false"}, "txt_memusage": {"english": "Log memory usage", "translation": "Log memory usage", "needs_translation": "false"}, "txt_batttype": {"english": "Tx Battery Options", "translation": "Tx Battery Options", "needs_translation": "false"}, "txt_battdef": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "txt_batttext": {"english": "Text", "translation": "Text", "needs_translation": "false"}, "txt_battdig": {"english": "Digital", "translation": "Digital", "needs_translation": "false"}, "dashboard": {"english": "Dashboard", "translation": "Dashboard", "needs_translation": "false"}, "dashboard_theme": {"english": "Theme", "translation": "Theme", "needs_translation": "false"}, "dashboard_theme_panel_global": {"english": "Default theme for all models", "translation": "Default theme for all models", "needs_translation": "false"}, "dashboard_theme_panel_model": {"english": "Optional theme for this model", "translation": "Optional theme for this model", "needs_translation": "false"}, "dashboard_theme_panel_model_disabled": {"english": "Disabled", "translation": "Disabled", "needs_translation": "false"}, "dashboard_settings": {"english": "Settings", "translation": "Settings", "needs_translation": "false"}, "dashboard_theme_preflight": {"english": "Preflight Theme", "translation": "Preflight Theme", "needs_translation": "false"}, "dashboard_theme_inflight": {"english": "Inflight Theme", "translation": "Inflight Theme", "needs_translation": "false"}, "dashboard_theme_postflight": {"english": "Postflight Theme", "translation": "Postflight Theme", "needs_translation": "false"}, "audio": {"english": "Audio", "translation": "Audio", "needs_translation": "false"}, "localizations": {"english": "Localization", "translation": "Localization", "needs_translation": "false"}, "txt_development": {"english": "Development", "translation": "Development", "needs_translation": "false"}, "temperature_unit": {"english": "Temperature Unit", "translation": "Temperature Unit", "needs_translation": "false"}, "altitude_unit": {"english": "Altitude Unit", "translation": "Altitude Unit", "needs_translation": "false"}, "celcius": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "fahrenheit": {"english": "Fahrenheit", "translation": "Fahrenheit", "needs_translation": "false"}, "meters": {"english": "Meters", "translation": "Meters", "needs_translation": "false"}, "feet": {"english": "Feet", "translation": "Feet", "needs_translation": "false"}, "warning": {"english": "Warning", "translation": "Warning", "needs_translation": "false"}, "governor_state": {"english": "Governor State", "translation": "Governor State", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Arming Flags", "needs_translation": "false"}, "voltage": {"english": "Voltage", "translation": "Voltage", "needs_translation": "false"}, "pid_rates_profile": {"english": "PID/Rates Profile", "translation": "PID/Rates Profile", "needs_translation": "false"}, "pid_profile": {"english": "PID Profile", "translation": "PID Profile", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "Rate Profile", "needs_translation": "false"}, "esc_temperature": {"english": "ESC Temperature", "translation": "ESC Temperature", "needs_translation": "false"}, "esc_threshold": {"english": "Thresh<PERSON> (°)", "translation": "Thresh<PERSON> (°)", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "BEC Voltage", "needs_translation": "false"}, "bec_threshold": {"english": "<PERSON><PERSON><PERSON><PERSON> (V)", "translation": "<PERSON><PERSON><PERSON><PERSON> (V)", "needs_translation": "false"}, "fuel": {"english": "Fuel", "translation": "Fuel", "needs_translation": "false"}, "fuel_callout_default": {"english": "Default (Only at 10%)", "translation": "Default (Only at 10%)", "needs_translation": "false"}, "fuel_callout_10": {"english": "Every 10%", "translation": "Every 10%", "needs_translation": "false"}, "fuel_callout_20": {"english": "Every 20%", "translation": "Every 20%", "needs_translation": "false"}, "fuel_callout_25": {"english": "Every 25%", "translation": "Every 25%", "needs_translation": "false"}, "fuel_callout_50": {"english": "Every 50%", "translation": "Every 50%", "needs_translation": "false"}, "fuel_callout_percent": {"english": "Callout %", "translation": "Callout %", "needs_translation": "false"}, "fuel_repeats_below": {"english": "Repeats below 0%", "translation": "Repeats below 0%", "needs_translation": "false"}, "fuel_haptic_below": {"english": "Haptic below 0%", "translation": "Haptic below 0%", "needs_translation": "false"}, "timer_alerting": {"english": "Timer Alerting", "translation": "Timer Alerting", "needs_translation": "false"}, "timer_elapsed_alert_mode": {"english": "Timer <PERSON><PERSON><PERSON>", "translation": "Timer <PERSON><PERSON><PERSON>", "needs_translation": "false"}, "timer_prealert_options": {"english": "Pre-timer <PERSON><PERSON>", "translation": "Pre-timer <PERSON><PERSON>", "needs_translation": "false"}, "timer_prealert": {"english": "Pre-timer <PERSON><PERSON>", "translation": "Pre-timer <PERSON><PERSON>", "needs_translation": "false"}, "timer_alert_period": {"english": "Alert <PERSON>", "translation": "Alert <PERSON>", "needs_translation": "false"}, "timer_postalert_options": {"english": "Post-timer <PERSON><PERSON>", "translation": "Post-timer <PERSON><PERSON>", "needs_translation": "false"}, "timer_postalert": {"english": "Post-timer <PERSON><PERSON>", "translation": "Post-timer <PERSON><PERSON>", "needs_translation": "false"}, "timer_postalert_period": {"english": "Alert <PERSON>", "translation": "Alert <PERSON>", "needs_translation": "false"}, "timer_postalert_interval": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "validate_sensors": {"help_p1": {"english": "This tool attempts to list all the sensors that you are not receiving in a concise list.", "translation": "This tool attempts to list all the sensors that you are not receiving in a concise list.", "needs_translation": "false"}, "invalid": {"english": "INVALID", "translation": "INVALID", "needs_translation": "false"}, "name": {"english": "Sensors", "translation": "Sensors", "needs_translation": "false"}, "msg_repair": {"english": "Enable required sensors on flight controller?", "translation": "Enable required sensors on flight controller?", "needs_translation": "false"}, "msg_repair_fin": {"english": "The flight controller has been configured? You may need to perform a discover sensors to see the changes.", "translation": "The flight controller has been configured? You may need to perform a discover sensors to see the changes.", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "Use this tool to ensure you are sending the correct sensors.", "translation": "Use this tool to ensure you are sending the correct sensors.", "needs_translation": "false"}}, "msp_exp": {"help_p1": {"english": "This tool provides the ability to send a custom byte string to the flight controller. It is useful for developers when debugging values.", "translation": "This tool provides the ability to send a custom byte string to the flight controller. It is useful for developers when debugging values.", "needs_translation": "false"}, "name": {"english": "MSP Expermental", "translation": "MSP Expermental", "needs_translation": "false"}, "help_p2": {"english": "If you do not understand what you are doing, do not use it as bad things can happen.", "translation": "If you do not understand what you are doing, do not use it as bad things can happen.", "needs_translation": "false"}}, "esc_tools": {"unknown": {"english": "UNKNOWN", "translation": "UNKNOWN", "needs_translation": "false"}, "name": {"english": "ESC Tools", "translation": "ESC Tools", "needs_translation": "false"}, "please_powercycle": {"english": "Please power cycle the ESC...", "translation": "Please power cycle the ESC...", "needs_translation": "false"}, "mfg": {"hw5": {"esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "brake_force": {"english": "Brake Force%", "translation": "Brake Force%", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Rotation", "needs_translation": "false"}, "soft_start": {"english": "Soft Start", "translation": "Soft Start", "needs_translation": "false"}, "name": {"english": "Hobbywing V5", "translation": "Hobbywing V5", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Limits", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "BEC Voltage", "needs_translation": "false"}, "gov_i_gain": {"english": "I-G<PERSON>", "translation": "I-G<PERSON>", "needs_translation": "false"}, "startup_time": {"english": "Startup Time", "translation": "Startup Time", "needs_translation": "false"}, "lipo_cell_count": {"english": "LiPo Cell Count", "translation": "LiPo Cell Count", "needs_translation": "false"}, "restart_time": {"english": "Restart Time", "translation": "Restart Time", "needs_translation": "false"}, "volt_cutoff_type": {"english": "Volt Cutoff Type", "translation": "Volt Cutoff Type", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "Motor", "needs_translation": "false"}, "brake_type": {"english": "Brake Type", "translation": "Brake Type", "needs_translation": "false"}, "brake": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Advanced", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basic", "needs_translation": "false"}, "flight_mode": {"english": "Flight Mode", "translation": "Flight Mode", "needs_translation": "false"}, "auto_restart": {"english": "Auto Restart", "translation": "Auto Restart", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Active Freewheel", "needs_translation": "false"}, "cutoff_voltage": {"english": "Cutoff Voltage", "translation": "Cutoff Voltage", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Startup Power", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Other", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Timing", "needs_translation": "false"}, "gov_p_gain": {"english": "P-Gain", "translation": "P-Gain", "needs_translation": "false"}}, "xdfly": {"hv_bec_voltage": {"english": "HV BEC Voltage", "translation": "HV BEC Voltage", "needs_translation": "false"}, "gov": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "brake_force": {"english": "Brake Force", "translation": "Brake Force", "needs_translation": "false"}, "sr_function": {"english": "SR Function", "translation": "SR Function", "needs_translation": "false"}, "name": {"english": "XDFLY", "translation": "XDFLY", "needs_translation": "false"}, "lv_bec_voltage": {"english": "LV BEC Voltage", "translation": "LV BEC Voltage", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Auto Restart Time", "needs_translation": "false"}, "acceleration": {"english": "Acceleration", "translation": "Acceleration", "needs_translation": "false"}, "motor_direction": {"english": "Motor Direction", "translation": "Motor Direction", "needs_translation": "false"}, "smart_fan": {"english": "Smart Fan", "translation": "Smart Fan", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Advanced", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "Cell Cutoff", "needs_translation": "false"}, "led_color": {"english": "LED Color", "translation": "LED Color", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basic", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Startup Power", "needs_translation": "false"}, "motor_poles": {"english": "Motor Poles", "translation": "Motor Poles", "needs_translation": "false"}, "capacity_correction": {"english": "Capacity Correction", "translation": "Capacity Correction", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Timing", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}, "flrtr": {"gov": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "motor_temp_sensor": {"english": "Motor temp sensor", "translation": "Motor temp sensor", "needs_translation": "false"}, "starting_torque": {"english": "Starting torque", "translation": "Starting torque", "needs_translation": "false"}, "cell_count": {"english": "Cell count", "translation": "Cell count", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}, "motor_erpm_max": {"english": "Motor ERPM max", "translation": "Motor ERPM max", "needs_translation": "false"}, "name": {"english": "FLYROTOR", "translation": "FLYROTOR", "needs_translation": "false"}, "low_voltage_protection": {"english": "Low voltage protection", "translation": "Low voltage protection", "needs_translation": "false"}, "gov_d": {"english": "Gov-D", "translation": "Gov-D", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry protocol", "translation": "Telemetry protocol", "needs_translation": "false"}, "motor_direction": {"english": "Motor direction", "translation": "Motor direction", "needs_translation": "false"}, "throttle_protocol": {"english": "Throttle protocol", "translation": "Throttle protocol", "needs_translation": "false"}, "soft_start": {"english": "Soft start", "translation": "Soft start", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Other", "needs_translation": "false"}, "temperature_protection": {"english": "Temperature protection", "translation": "Temperature protection", "needs_translation": "false"}, "buzzer_volume": {"english": "Buzzer volume", "translation": "Buzzer volume", "needs_translation": "false"}, "timing_angle": {"english": "Timing angle", "translation": "Timing angle", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Advanced", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "bec_voltage": {"english": "BEC voltage", "translation": "BEC voltage", "needs_translation": "false"}, "fan_control": {"english": "Fan control", "translation": "Fan control", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basic", "needs_translation": "false"}, "current_gain": {"english": "Current gain", "translation": "Current gain", "needs_translation": "false"}, "led_color": {"english": "LED color", "translation": "LED color", "needs_translation": "false"}, "motor_temp": {"english": "Motor temperture", "translation": "Motor temperture", "needs_translation": "false"}, "response_speed": {"english": "Response speed", "translation": "Response speed", "needs_translation": "false"}, "battery_capacity": {"english": "Battery capacity", "translation": "Battery capacity", "needs_translation": "false"}}, "scorp": {"esc_mode": {"english": "ESC Mode", "translation": "ESC Mode", "needs_translation": "false"}, "min_voltage": {"english": "Min Voltage", "translation": "Min Voltage", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Rotation", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry Protocol", "translation": "Telemetry Protocol", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "runup_time": {"english": "Runup Time", "translation": "Runup Time", "needs_translation": "false"}, "motor_startup_sound": {"english": "Motor Startup Sound", "translation": "Motor Startup Sound", "needs_translation": "false"}, "gov_integral": {"english": "Gov <PERSON><PERSON>", "translation": "Gov <PERSON><PERSON>", "needs_translation": "false"}, "gov_proportional": {"english": "Gov Proportional", "translation": "Gov Proportional", "needs_translation": "false"}, "cutoff_handling": {"english": "Cutoff Handling", "translation": "Cutoff Handling", "needs_translation": "false"}, "bailout": {"english": "Bailout", "translation": "Bailout", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Limits", "needs_translation": "false"}, "soft_start_time": {"english": "Soft Start Time", "translation": "Soft Start Time", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Advanced", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "BEC Voltage", "needs_translation": "false"}, "extra_msg_save": {"english": "Please reboot the ESC to apply the changes", "translation": "Please reboot the ESC to apply the changes", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basic", "needs_translation": "false"}, "max_current": {"english": "Max <PERSON>", "translation": "Max <PERSON>", "needs_translation": "false"}, "max_temperature": {"english": "Max Temperature", "translation": "Max Temperature", "needs_translation": "false"}, "protection_delay": {"english": "Protection Delay", "translation": "Protection Delay", "needs_translation": "false"}, "max_used": {"english": "Max Used", "translation": "Max Used", "needs_translation": "false"}}, "yge": {"esc_mode": {"english": "ESC Mode", "translation": "ESC Mode", "needs_translation": "false"}, "esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "current_limit": {"english": "Current Limit", "translation": "Current Limit", "needs_translation": "false"}, "f3c_auto": {"english": "F3C Autorotation", "translation": "F3C Autorotation", "needs_translation": "false"}, "name": {"english": "YGE", "translation": "YGE", "needs_translation": "false"}, "max_start_power": {"english": "Max Start Power", "translation": "Max Start Power", "needs_translation": "false"}, "lv_bec_voltage": {"english": "BEC", "translation": "BEC", "needs_translation": "false"}, "pinion_teeth": {"english": "Pinion Teeth", "translation": "Pinion Teeth", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Auto Restart Time", "needs_translation": "false"}, "main_teeth": {"english": "Main Teeth", "translation": "Main Teeth", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Other", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Limits", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "Cell Cutoff", "needs_translation": "false"}, "throttle_response": {"english": "Throttle Response", "translation": "Throttle Response", "needs_translation": "false"}, "stick_zero_us": {"english": "<PERSON>", "translation": "<PERSON>", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "Advanced", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "motor_pole_pairs": {"english": "Motor Pole Pairs", "translation": "Motor Pole Pairs", "needs_translation": "false"}, "stick_range_us": {"english": "Stick Range", "translation": "Stick Range", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Basic", "needs_translation": "false"}, "min_start_power": {"english": "Min Start Power", "translation": "Min Start Power", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Active Freewheel", "needs_translation": "false"}, "direction": {"english": "Direction", "translation": "Direction", "needs_translation": "false"}, "timing": {"english": "Motor Timing", "translation": "Motor Timing", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}}, "searching": {"english": "Searching", "translation": "Searching", "needs_translation": "false"}}, "pids": {"help_p1": {"english": "FeedForward (Roll/Pitch): Start at 70, increase until stops are sharp with no drift. Keep roll and pitch equal.", "translation": "FeedForward (Roll/Pitch): Start at 70, increase until stops are sharp with no drift. Keep roll and pitch equal.", "needs_translation": "false"}, "o": {"english": "O", "translation": "O", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "help_p5": {"english": "Test & Adjust: Fly, observe, and fine-tune for best performance in real conditions.", "translation": "Test & Adjust: Fly, observe, and fine-tune for best performance in real conditions.", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "PIDs", "translation": "PIDs", "needs_translation": "false"}, "help_p2": {"english": "<PERSON> Gain (Roll/Pitch): Raise gradually for stable piro pitch pumps. Too high causes wobbles; match roll/pitch values.", "translation": "<PERSON> Gain (Roll/Pitch): Raise gradually for stable piro pitch pumps. Too high causes wobbles; match roll/pitch values.", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "b": {"english": "B", "translation": "B", "needs_translation": "false"}, "help_p4": {"english": "Tail Stop Gain (CW/CCW): Adjust separately for clean, bounce-free stops in both directions.", "translation": "Tail Stop Gain (CW/CCW): Adjust separately for clean, bounce-free stops in both directions.", "needs_translation": "false"}, "help_p3": {"english": "Tail P/I/D Gains: Increase P until slight wobble in funnels, then back off slightly. Raise I until tail holds firm in hard moves (too high causes slow wag). Adjust D for smooth stops—higher for slow servos, lower for fast ones.", "translation": "Tail P/I/D Gains: Increase P until slight wobble in funnels, then back off slightly. Raise I until tail holds firm in hard moves (too high causes slow wag). Adjust D for smooth stops—higher for slow servos, lower for fast ones.", "needs_translation": "false"}}, "msp_speed": {"seconds_600": {"english": "  600S  ", "translation": "  600S  ", "needs_translation": "false"}, "avg_query_time": {"english": "Average query time", "translation": "Average query time", "needs_translation": "false"}, "seconds_30": {"english": "  30S  ", "translation": "  30S  ", "needs_translation": "false"}, "name": {"english": "MSP Speed", "translation": "MSP Speed", "needs_translation": "false"}, "max_query_time": {"english": "Maximum query time", "translation": "Maximum query time", "needs_translation": "false"}, "help_p1": {"english": "This tool attempts to determine the quality of your MSP data link by performing as many large MSP queries within 30 seconds as possible.", "translation": "This tool attempts to determine the quality of your MSP data link by performing as many large MSP queries within 30 seconds as possible.", "needs_translation": "false"}, "retries": {"english": "Retries", "translation": "Retries", "needs_translation": "false"}, "checksum_errors": {"english": "Checksum errors", "translation": "Checksum errors", "needs_translation": "false"}, "test_length": {"english": "Test length", "translation": "Test length", "needs_translation": "false"}, "start": {"english": "Start", "translation": "Start", "needs_translation": "false"}, "memory_free": {"english": "Memory free", "translation": "Memory free", "needs_translation": "false"}, "start_prompt": {"english": "Would you like to start the test? Choose the test run time below.", "translation": "Would you like to start the test? Choose the test run time below.", "needs_translation": "false"}, "rf_protocol": {"english": "RF protocol", "translation": "RF protocol", "needs_translation": "false"}, "min_query_time": {"english": "Minimum query time", "translation": "Minimum query time", "needs_translation": "false"}, "seconds_120": {"english": "  120S  ", "translation": "  120S  ", "needs_translation": "false"}, "seconds_300": {"english": "  300S  ", "translation": "  300S  ", "needs_translation": "false"}, "testing": {"english": "Testing", "translation": "Testing", "needs_translation": "false"}, "successful_queries": {"english": "Successful queries", "translation": "Successful queries", "needs_translation": "false"}, "timeouts": {"english": "Timeouts", "translation": "Timeouts", "needs_translation": "false"}, "testing_performance": {"english": "Testing MSP performance...", "translation": "Testing MSP performance...", "needs_translation": "false"}, "total_queries": {"english": "Total queries", "translation": "Total queries", "needs_translation": "false"}}, "copyprofiles": {"profile_type": {"english": "Profile Type", "translation": "Profile Type", "needs_translation": "false"}, "profile_type_pid": {"english": "PID", "translation": "PID", "needs_translation": "false"}, "profile_type_rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "msgbox_save": {"english": "Save settings", "translation": "Save settings", "needs_translation": "false"}, "name": {"english": "Copy Profiles", "translation": "Copy Profiles", "needs_translation": "false"}, "help_p1": {"english": "Copy PID profile or Rate profile from Source to Destination.", "translation": "Copy PID profile or Rate profile from Source to Destination.", "needs_translation": "false"}, "dest_profile": {"english": "Dest. Profile", "translation": "Dest. Profile", "needs_translation": "false"}, "source_profile": {"english": "Source Profile", "translation": "Source Profile", "needs_translation": "false"}, "msgbox_msg": {"english": "Save current page to flight controller?", "translation": "Save current page to flight controller?", "needs_translation": "false"}, "help_p2": {"english": "Choose the source and destinations and save to copy the profile.", "translation": "Choose the source and destinations and save to copy the profile.", "needs_translation": "false"}}, "esc_motors": {"min_throttle": {"english": "0% Throttle PWM Value", "translation": "0% Throttle PWM Value", "needs_translation": "false"}, "tail_motor_ratio": {"english": "Tail Motor Ratio", "translation": "Tail Motor Ratio", "needs_translation": "false"}, "max_throttle": {"english": "100% Throttle PWM value", "translation": "100% Throttle PWM value", "needs_translation": "false"}, "main_motor_ratio": {"english": "Main Motor Ratio", "translation": "Main Motor Ratio", "needs_translation": "false"}, "pinion": {"english": "Pinion", "translation": "Pinion", "needs_translation": "false"}, "main": {"english": "Main", "translation": "Main", "needs_translation": "false"}, "help_p1": {"english": "Configure the motor and speed controller features.", "translation": "Configure the motor and speed controller features.", "needs_translation": "false"}, "rear": {"english": "Rear", "translation": "Rear", "needs_translation": "false"}, "front": {"english": "Front", "translation": "Front", "needs_translation": "false"}, "voltage_correction": {"english": "Voltage Correction", "translation": "Voltage Correction", "needs_translation": "false"}, "mincommand": {"english": "Motor Stop PWM Value", "translation": "Motor Stop PWM Value", "needs_translation": "false"}, "name": {"english": "ESC/Motors", "translation": "ESC/Motors", "needs_translation": "false"}, "motor_pole_count": {"english": "Motor Pole Count", "translation": "Motor Pole Count", "needs_translation": "false"}, "current_correction": {"english": "Current Correction", "translation": "Current Correction", "needs_translation": "false"}, "consumption_correction": {"english": "Consumption Correction", "translation": "Consumption Correction", "needs_translation": "false"}}, "radio_config": {"deflection": {"english": "Deflection", "translation": "Deflection", "needs_translation": "false"}, "max_throttle": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "stick": {"english": "Stick", "translation": "Stick", "needs_translation": "false"}, "arming": {"english": "Arming", "translation": "Arming", "needs_translation": "false"}, "yaw_deadband": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "cyclic": {"english": "Cyclic", "translation": "Cyclic", "needs_translation": "false"}, "name": {"english": "Radio Config", "translation": "Radio Config", "needs_translation": "false"}, "help_p1": {"english": "Configure your radio settings. Stick center, arm, throttle hold, and throttle cut.", "translation": "Configure your radio settings. Stick center, arm, throttle hold, and throttle cut.", "needs_translation": "false"}, "min_throttle": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "throttle": {"english": "<PERSON>hrottle", "translation": "<PERSON>hrottle", "needs_translation": "false"}, "deadband": {"english": "Deadband", "translation": "Deadband", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Center", "needs_translation": "false"}}, "profile_select": {"help_p1": {"english": "Set the current flight profile or rate profile you would like to use.", "translation": "Set the current flight profile or rate profile you would like to use.", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "Rate Profile", "needs_translation": "false"}, "pid_profile": {"english": "PID profile", "translation": "PID profile", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "Save current page to flight controller?", "needs_translation": "false"}, "save_prompt_local": {"english": "Save current page to radio?", "translation": "Save current page to radio?", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "CANCEL", "needs_translation": "false"}, "name": {"english": "Select Profile", "translation": "Select Profile", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "Save settings", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "If you use a switch on your radio to change flight or rate modes, this will override this choice as soon as you toggle the switch.", "translation": "If you use a switch on your radio to change flight or rate modes, this will override this choice as soon as you toggle the switch.", "needs_translation": "false"}}, "profile_governor": {"tail_torque_assist": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "cyc": {"english": "Cyc", "translation": "Cyc", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "help_p1": {"english": "Full headspeed: Headspeed target when at 100% throttle input.", "translation": "Full headspeed: Headspeed target when at 100% throttle input.", "needs_translation": "false"}, "help_p6": {"english": "Tail Torque Assist: For motorized tails. Gain and limit of headspeed increase when using main rotor torque for yaw assist.", "translation": "Tail Torque Assist: For motorized tails. Gain and limit of headspeed increase when using main rotor torque for yaw assist.", "needs_translation": "false"}, "help_p4": {"english": "Precomp: Governor precomp gain for yaw, cyclic, and collective inputs.", "translation": "Precomp: Governor precomp gain for yaw, cyclic, and collective inputs.", "needs_translation": "false"}, "max_throttle": {"english": "<PERSON> throttle", "translation": "<PERSON> throttle", "needs_translation": "false"}, "full_headspeed": {"english": "Full headspeed", "translation": "Full headspeed", "needs_translation": "false"}, "precomp": {"english": "Precomp", "translation": "Precomp", "needs_translation": "false"}, "gain": {"english": "PID master gain", "translation": "PID master gain", "needs_translation": "false"}, "disabled_message": {"english": "Rotorflight governor is not enabled", "translation": "Rotorflight governor is not enabled", "needs_translation": "false"}, "help_p3": {"english": "Gains: Fine tuning of the governor.", "translation": "Gains: Fine tuning of the governor.", "needs_translation": "false"}, "col": {"english": "Col", "translation": "Col", "needs_translation": "false"}, "min_throttle": {"english": "<PERSON> throttle", "translation": "<PERSON> throttle", "needs_translation": "false"}, "tta_limit": {"english": "Limit", "translation": "Limit", "needs_translation": "false"}, "help_p2": {"english": "PID master gain: How hard the governor works to hold the RPM.", "translation": "PID master gain: How hard the governor works to hold the RPM.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p5": {"english": "Max throttle: The maximum throttle % the governor is allowed to use.", "translation": "Max throttle: The maximum throttle % the governor is allowed to use.", "needs_translation": "false"}, "tta_gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "profile_tailrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p4": {"english": "Collective FF Gain: Tail precompensation for collective inputs.", "translation": "Collective FF Gain: Tail precompensation for collective inputs.", "needs_translation": "false"}, "collective_impulse_ff": {"english": "Collective Impulse FF", "translation": "Collective Impulse FF", "needs_translation": "false"}, "help_p2": {"english": "Precomp Cutoff: Frequency limit for all yaw precompensation actions.", "translation": "Precomp Cutoff: Frequency limit for all yaw precompensation actions.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "help_p3": {"english": "Cyclic FF Gain: Tail precompensation for cyclic inputs.", "translation": "Cyclic FF Gain: Tail precompensation for cyclic inputs.", "needs_translation": "false"}, "help_p1": {"english": "Yaw Stop Gain: Higher stop gain will make the tail stop more aggressively but may cause oscillations if too high. Adjust CW or CCW to make the yaw stops even.", "translation": "Yaw Stop Gain: Higher stop gain will make the tail stop more aggressively but may cause oscillations if too high. Adjust CW or CCW to make the yaw stops even.", "needs_translation": "false"}, "inertia_precomp": {"english": "Inertia Precomp", "translation": "Inertia Precomp", "needs_translation": "false"}, "cyclic_ff_gain": {"english": "Cyclic FF gain", "translation": "Cyclic FF gain", "needs_translation": "false"}, "help_p5": {"english": "Collective Impulse FF: Impulse tail precompensation for collective inputs. If you need extra tail precompensation at the beginning of collective input.", "translation": "Collective Impulse FF: Impulse tail precompensation for collective inputs. If you need extra tail precompensation at the beginning of collective input.", "needs_translation": "false"}, "cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "yaw_stop_gain": {"english": "Yaw stop gain", "translation": "Yaw stop gain", "needs_translation": "false"}, "precomp_cutoff": {"english": "Precomp Cutoff", "translation": "Precomp Cutoff", "needs_translation": "false"}, "collective_ff_gain": {"english": "Collective FF gain", "translation": "Collective FF gain", "needs_translation": "false"}, "name": {"english": "Tail Rotor", "translation": "Tail Rotor", "needs_translation": "false"}, "decay": {"english": "Decay", "translation": "Decay", "needs_translation": "false"}}, "profile_pidcontroller": {"help_p4": {"english": "Error rotation: Allow errors to be shared between all axes.", "translation": "Error rotation: Allow errors to be shared between all axes.", "needs_translation": "false"}, "ground_error_decay": {"english": "Ground Error Decay", "translation": "Ground Error Decay", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "inflight_error_decay": {"english": "Inflight Error Decay", "translation": "Inflight Error Decay", "needs_translation": "false"}, "help_p2": {"english": "Error limit: Angle limit for I-term.", "translation": "Error limit: Angle limit for I-term.", "needs_translation": "false"}, "error_limit": {"english": "Error limit", "translation": "Error limit", "needs_translation": "false"}, "help_p3": {"english": "Offset limit: Angle limit for High Speed Integral (O-term).", "translation": "Offset limit: Angle limit for High Speed Integral (O-term).", "needs_translation": "false"}, "cutoff_point": {"english": "Cut-off point", "translation": "Cut-off point", "needs_translation": "false"}, "limit": {"english": "Limit", "translation": "Limit", "needs_translation": "false"}, "iterm_relax": {"english": "I-term relax", "translation": "I-term relax", "needs_translation": "false"}, "hsi_offset_limit": {"english": "HSI Offset limit", "translation": "HSI Offset limit", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "name": {"english": "PID Controller", "translation": "PID Controller", "needs_translation": "false"}, "error_rotation": {"english": "Error rotation", "translation": "Error rotation", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}, "help_p5": {"english": "I-term relax: Limit accumulation of I-term during fast movements - helps reduce bounce back after fast stick movements. Generally needs to be lower for large helis and can be higher for small helis. Best to only reduce as much as is needed for your flying style.", "translation": "I-term relax: Limit accumulation of I-term during fast movements - helps reduce bounce back after fast stick movements. Generally needs to be lower for large helis and can be higher for small helis. Best to only reduce as much as is needed for your flying style.", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Time", "needs_translation": "false"}, "help_p1": {"english": "Error decay ground: PID decay to help prevent heli from tipping over when on the ground.", "translation": "Error decay ground: PID decay to help prevent heli from tipping over when on the ground.", "needs_translation": "false"}}, "logs": {"help_logs_p2": {"english": "Note. To enable logging it is essential for you to have the following sensors enabled.", "translation": "Note. To enable logging it is essential for you to have the following sensors enabled.", "needs_translation": "false"}, "name": {"english": "Logs", "translation": "Logs", "needs_translation": "false"}, "help_logs_p1": {"english": "Please select a log file from the list below.", "translation": "Please select a log file from the list below.", "needs_translation": "false"}, "msg_no_logs_found": {"english": "NO LOG FILES FOUND", "translation": "NO LOG FILES FOUND", "needs_translation": "false"}, "help_logs_tool_p1": {"english": "Please use the slider to navigate the graph.", "translation": "Please use the slider to navigate the graph.", "needs_translation": "false"}, "help_logs_p3": {"english": "- arm status, voltage, headspeed, current, esc temperature", "translation": "- arm status, voltage, headspeed, current, esc temperature", "needs_translation": "false"}}, "battery": {"calcfuel_local": {"english": "Calculate Fuel Using", "translation": "Calculate Fuel Using", "needs_translation": "false"}, "max_cell_voltage": {"english": "Max Cell Voltage", "translation": "Max Cell Voltage", "needs_translation": "false"}, "full_cell_voltage": {"english": "Full Cell Voltage", "translation": "Full Cell Voltage", "needs_translation": "false"}, "name": {"english": "Battery", "translation": "Battery", "needs_translation": "false"}, "min_cell_voltage": {"english": "Min Cell Voltage", "translation": "Min Cell Voltage", "needs_translation": "false"}, "help_p1": {"english": "The battery settings are used to configure the flight controller to monitor the battery voltage and provide warnings when the voltage drops below a certain level.", "translation": "The battery settings are used to configure the flight controller to monitor the battery voltage and provide warnings when the voltage drops below a certain level.", "needs_translation": "false"}, "battery_capacity": {"english": "Battery Capacity", "translation": "Battery Capacity", "needs_translation": "false"}, "warn_cell_voltage": {"english": "Warn Cell Voltage", "translation": "Warn Cell Voltage", "needs_translation": "false"}, "cell_count": {"english": "Cell Count", "translation": "Cell Count", "needs_translation": "false"}, "consumption_warning_percentage": {"english": "Consumption Warning %", "translation": "Consumption Warning %", "needs_translation": "false"}, "timer": {"english": "Flight Time Alarm", "translation": "Flight Time Alarm", "needs_translation": "false"}, "voltage_multiplier": {"english": "Sag Compensation", "translation": "Sag Compensation", "needs_translation": "false"}, "kalman_multiplier": {"english": "Filter Compensation", "translation": "Filter Compensation", "needs_translation": "false"}, "alert_type": {"english": "Rx Voltage Alert", "translation": "Rx Voltage Alert", "needs_translation": "false"}, "bec_voltage_alert": {"english": "BEC Alert Value", "translation": "BEC Alert Value", "needs_translation": "false"}, "rx_voltage_alert": {"english": "RxBatt Alert Value", "translation": "RxBatt Alert Value", "needs_translation": "false"}}, "profile_mainrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p4": {"english": "Cross Coupling Freq. Limit: Frequency limit for the compensation, higher value will make the compensation action faster.", "translation": "Cross Coupling Freq. Limit: Frequency limit for the compensation, higher value will make the compensation action faster.", "needs_translation": "false"}, "collective_pitch_comp_short": {"english": "Col. Pitch Compensation", "translation": "Col. Pitch Compensation", "needs_translation": "false"}, "cyclic_cross_coupling": {"english": "Cyclic Cross coupling", "translation": "Cyclic Cross coupling", "needs_translation": "false"}, "collective_pitch_comp": {"english": "Collective Pitch Compensation", "translation": "Collective Pitch Compensation", "needs_translation": "false"}, "name": {"english": "Main Rotor", "translation": "Main Rotor", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "ratio": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Collective Pitch Compensation: Increasing will compensate for the pitching motion caused by tail drag when climbing.", "translation": "Collective Pitch Compensation: Increasing will compensate for the pitching motion caused by tail drag when climbing.", "needs_translation": "false"}, "help_p2": {"english": "Cross Coupling Gain: Removes roll coupling when only elevator is applied.", "translation": "Cross Coupling Gain: Removes roll coupling when only elevator is applied.", "needs_translation": "false"}, "help_p3": {"english": "Cross Coupling Ratio: Amount of compensation (pitch vs roll) to apply.", "translation": "Cross Coupling Ratio: Amount of compensation (pitch vs roll) to apply.", "needs_translation": "false"}}, "sbusout": {"title": {"english": "SBUS Output", "translation": "SBUS Output", "needs_translation": "false"}, "help_fields_source": {"english": "Source id for the mix, counting from 0-15.", "translation": "Source id for the mix, counting from 0-15.", "needs_translation": "false"}, "help_default_p4": {"english": "- For motors, use 0, 1000.", "translation": "- For motors, use 0, 1000.", "needs_translation": "false"}, "ch_prefix": {"english": "CH", "translation": "CH", "needs_translation": "false"}, "channel_prefix": {"english": "CHANNEL ", "translation": "CHANNEL ", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "Saving", "needs_translation": "false"}, "name": {"english": "SBUS Out", "translation": "SBUS Out", "needs_translation": "false"}, "channel_page": {"english": "Sbus out / CH", "translation": "Sbus out / CH", "needs_translation": "false"}, "receiver": {"english": "Receiver", "translation": "Receiver", "needs_translation": "false"}, "servo": {"english": "Servo", "translation": "Servo", "needs_translation": "false"}, "type": {"english": "Type", "translation": "Type", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "Saving data...", "needs_translation": "false"}, "help_fields_max": {"english": "The maximum pwm value to send", "translation": "The maximum pwm value to send", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "Motor", "needs_translation": "false"}, "help_default_p5": {"english": "- Or you can customize your own mapping.", "translation": "- Or you can customize your own mapping.", "needs_translation": "false"}, "help_default_p1": {"english": "Configure advanced mixing and channel mapping if you have SBUS Out enabled on a serial port.", "translation": "Configure advanced mixing and channel mapping if you have SBUS Out enabled on a serial port.", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "Save current page to flight controller?", "needs_translation": "false"}, "help_fields_min": {"english": "The minimum pwm value to send.", "translation": "The minimum pwm value to send.", "needs_translation": "false"}, "mixer": {"english": "Mixer", "translation": "Mixer", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "CANCEL", "needs_translation": "false"}, "help_default_p2": {"english": "- For RX channels or servos (wideband), use 1000, 2000 or 500,1000 for narrow band servos.", "translation": "- For RX channels or servos (wideband), use 1000, 2000 or 500,1000 for narrow band servos.", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "Save settings", "needs_translation": "false"}, "min": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "help_default_p3": {"english": "- For mixer rules, use -1000, 1000.", "translation": "- For mixer rules, use -1000, 1000.", "needs_translation": "false"}, "source": {"english": "Source", "translation": "Source", "needs_translation": "false"}}, "profile_rescue": {"help_p4": {"english": "Hover: How much collective to maintain a steady hover.", "translation": "Hover: How much collective to maintain a steady hover.", "needs_translation": "false"}, "hover": {"english": "Hover", "translation": "Hover", "needs_translation": "false"}, "collective": {"english": "Collective", "translation": "Collective", "needs_translation": "false"}, "help_p2": {"english": "Pull-up: How much collective and for how long to arrest the fall.", "translation": "Pull-up: How much collective and for how long to arrest the fall.", "needs_translation": "false"}, "climb": {"english": "Climb", "translation": "Climb", "needs_translation": "false"}, "mode_enable": {"english": "Rescue mode enable", "translation": "Rescue mode enable", "needs_translation": "false"}, "help_p3": {"english": "Climb: How much collective to maintain a steady climb - and how long.", "translation": "Climb: How much collective to maintain a steady climb - and how long.", "needs_translation": "false"}, "help_p1": {"english": "Flip to upright: Flip the heli upright when rescue is activated.", "translation": "Flip to upright: Flip the heli upright when rescue is activated.", "needs_translation": "false"}, "flip_upright": {"english": "Flip to upright", "translation": "Flip to upright", "needs_translation": "false"}, "flip": {"english": "Flip", "translation": "Flip", "needs_translation": "false"}, "level_gain": {"english": "Level", "translation": "Level", "needs_translation": "false"}, "name": {"english": "Rescue", "translation": "Rescue", "needs_translation": "false"}, "exit_time": {"english": "Exit time", "translation": "Exit time", "needs_translation": "false"}, "help_p5": {"english": "Flip: How long to wait before aborting because the flip did not work.", "translation": "Flip: How long to wait before aborting because the flip did not work.", "needs_translation": "false"}, "help_p6": {"english": "Gains: How hard to fight to keep heli level when engaging rescue mode.", "translation": "Gains: How hard to fight to keep heli level when engaging rescue mode.", "needs_translation": "false"}, "fail_time": {"english": "Fail time", "translation": "Fail time", "needs_translation": "false"}, "pull_up": {"english": "Pull-up", "translation": "Pull-up", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "help_p7": {"english": "Rate and Accel: Max rotation and acceleration rates when leveling during rescue.", "translation": "Rate and Accel: Max rotation and acceleration rates when leveling during rescue.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Time", "needs_translation": "false"}, "accel": {"english": "Accel", "translation": "Accel", "needs_translation": "false"}}, "trim": {"disable_mixer_message": {"english": "Return control of the servos to the flight controller.", "translation": "Return control of the servos to the flight controller.", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Motor idle  %", "translation": "Tail Motor idle  %", "needs_translation": "false"}, "disable_mixer_override": {"english": "Disable mixer override", "translation": "Disable mixer override", "needs_translation": "false"}, "yaw_trim": {"english": "Yaw. trim %", "translation": "Yaw. trim %", "needs_translation": "false"}, "enable_mixer_message": {"english": "Set all servos to their configured center position. \r\n\r\nThis will result in all values on this page being saved when adjusting the servo trim.", "translation": "Set all servos to their configured center position. \r\n\r\nThis will result in all values on this page being saved when adjusting the servo trim.", "needs_translation": "false"}, "mixer_override_disabling": {"english": "Disabling mixer override...", "translation": "Disabling mixer override...", "needs_translation": "false"}, "roll_trim": {"english": "Roll trim %", "translation": "Roll trim %", "needs_translation": "false"}, "pitch_trim": {"english": "Pitch trim %", "translation": "Pitch trim %", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Motorised tail: If using a motorised tail, use this to set the minimum idle speed and zero yaw.", "translation": "Motorised tail: If using a motorised tail, use this to set the minimum idle speed and zero yaw.", "needs_translation": "false"}, "mixer_override": {"english": "Mixer Override", "translation": "Mixer Override", "needs_translation": "false"}, "mixer_override_enabling": {"english": "Enabling mixer override...", "translation": "Enabling mixer override...", "needs_translation": "false"}, "enable_mixer_override": {"english": "Enable mixer override", "translation": "Enable mixer override", "needs_translation": "false"}, "collective_trim": {"english": "Col. trim %", "translation": "Col. trim %", "needs_translation": "false"}, "help_p1": {"english": "Link trims: Use to trim out small leveling issues in your swash plate. Typically only used if the swash links are non-adjustable.", "translation": "Link trims: Use to trim out small leveling issues in your swash plate. Typically only used if the swash links are non-adjustable.", "needs_translation": "false"}}, "governor": {"help_p1": {"english": "These parameters apply globally to the governor regardless of the profile in use.", "translation": "These parameters apply globally to the governor regardless of the profile in use.", "needs_translation": "false"}, "handover_throttle": {"english": "Handover throttle%", "translation": "Handover throttle%", "needs_translation": "false"}, "spoolup_min_throttle": {"english": "Spoolup min throttle%", "translation": "Spoolup min throttle%", "needs_translation": "false"}, "recovery_time": {"english": "Recovery time", "translation": "Recovery time", "needs_translation": "false"}, "mode": {"english": "Mode", "translation": "Mode", "needs_translation": "false"}, "help_p2": {"english": "Each parameter is simply a time value in seconds for each governor action.", "translation": "Each parameter is simply a time value in seconds for each governor action.", "needs_translation": "false"}, "tracking_time": {"english": "Tracking time", "translation": "Tracking time", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "startup_time": {"english": "Startup time", "translation": "Startup time", "needs_translation": "false"}, "spoolup_time": {"english": "Spoolup time", "translation": "Spoolup time", "needs_translation": "false"}}, "accelerometer": {"help_p1": {"english": "The accelerometer is used to measure the angle of the flight controller in relation to the horizon. This data is used to stabilize the aircraft and provide self-leveling functionality.", "translation": "The accelerometer is used to measure the angle of the flight controller in relation to the horizon. This data is used to stabilize the aircraft and provide self-leveling functionality.", "needs_translation": "false"}, "name": {"english": "Accelerometer", "translation": "Accelerometer", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "msg_calibrate": {"english": "Calibrate the accelerometer?", "translation": "Calibrate the accelerometer?", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}}, "rates": {"help_table_5_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "needs_translation": "false"}, "actual": {"english": "ACTUAL", "translation": "ACTUAL", "needs_translation": "false"}, "max_rate": {"english": "Max Rate", "translation": "Max Rate", "needs_translation": "false"}, "help_table_4_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "help_table_5_p1": {"english": "RC Rate: Use to reduce sensitivity around center stick. RC Rate set to one half of the Max Rate is linear. A lower number will reduce sensitivity around center stick. Higher than one half of the Max Rate will also increase the Max Rate.", "translation": "RC Rate: Use to reduce sensitivity around center stick. RC Rate set to one half of the Max Rate is linear. A lower number will reduce sensitivity around center stick. Higher than one half of the Max Rate will also increase the Max Rate.", "needs_translation": "false"}, "help_table_4_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "needs_translation": "false"}, "center_sensitivity": {"english": "Cntr. Sens.", "translation": "Cntr. Sens.", "needs_translation": "false"}, "rc_curve": {"english": "RC Curve", "translation": "RC Curve", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "none": {"english": "NONE", "translation": "NONE", "needs_translation": "false"}, "betaflight": {"english": "BETAFLIGHT", "translation": "BETAFLIGHT", "needs_translation": "false"}, "kiss": {"english": "KISS", "translation": "KISS", "needs_translation": "false"}, "help_table_1_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "needs_translation": "false"}, "help_table_3_p2": {"english": "Rate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "Rate: Increases maximum rotation rate while reducing sensitivity around half stick.", "needs_translation": "false"}, "help_table_2_p2": {"english": "Acro+: Increases the maximum rotation rate while reducing sensitivity around half stick.", "translation": "Acro+: Increases the maximum rotation rate while reducing sensitivity around half stick.", "needs_translation": "false"}, "superrate": {"english": "SuperRate", "translation": "SuperRate", "needs_translation": "false"}, "help_table_2_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "needs_translation": "false"}, "raceflight": {"english": "RACEFLIGHT", "translation": "RACEFLIGHT", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "collective": {"english": "Col", "translation": "Col", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "Rates", "needs_translation": "false"}, "help_table_5_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "needs_translation": "false"}, "help_table_3_p3": {"english": "RC Curve: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "RC Curve: Reduces sensitivity near the stick's center where fine controls are needed.", "needs_translation": "false"}, "expo": {"english": "Expo", "translation": "Expo", "needs_translation": "false"}, "help_table_1_p2": {"english": "SuperRate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "SuperRate: Increases maximum rotation rate while reducing sensitivity around half stick.", "needs_translation": "false"}, "help_default_p2": {"english": "We will use the sub keys below.", "translation": "We will use the sub keys below.", "needs_translation": "false"}, "help_default_p1": {"english": "Default: We keep this to make button appear for rates.", "translation": "Default: We keep this to make button appear for rates.", "needs_translation": "false"}, "quick": {"english": "QUICK", "translation": "QUICK", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "acroplus": {"english": "Acro+", "translation": "Acro+", "needs_translation": "false"}, "help_table_1_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "RC Rate: Maximum rotation rate at full stick deflection.", "needs_translation": "false"}, "rc_rate": {"english": "RC Rate", "translation": "RC Rate", "needs_translation": "false"}, "help_table_2_p1": {"english": "Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Rate: Maximum rotation rate at full stick deflection in degrees per second.", "needs_translation": "false"}, "help_table_4_p1": {"english": "Center Sensitivity: Use to reduce sensitivity around center stick. Set Center Sensitivity to the same as Max Rate for a linear response. A lower number than Max Rate will reduce sensitivity around center stick. Note that higher than Max Rate will increase the Max Rate - not recommended as it causes issues in the Blackbox log.", "translation": "Center Sensitivity: Use to reduce sensitivity around center stick. Set Center Sensitivity to the same as Max Rate for a linear response. A lower number than Max Rate will reduce sensitivity around center stick. Note that higher than Max Rate will increase the Max Rate - not recommended as it causes issues in the Blackbox log.", "needs_translation": "false"}, "help_table_0_p1": {"english": "All values are set to zero because no RATE TABLE is in use.", "translation": "All values are set to zero because no RATE TABLE is in use.", "needs_translation": "false"}, "help_table_3_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "RC Rate: Maximum rotation rate at full stick deflection.", "needs_translation": "false"}}, "mixer": {"help_p1": {"english": "Adust swash plate geometry, phase angles, and limits.", "translation": "Adust swash plate geometry, phase angles, and limits.", "needs_translation": "false"}, "collective_tilt_correction_pos": {"english": "Positive", "translation": "Positive", "needs_translation": "false"}, "geo_correction": {"english": "Geo Correction", "translation": "Geo Correction", "needs_translation": "false"}, "swash_tta_precomp": {"english": "TTA Precomp", "translation": "TTA Precomp", "needs_translation": "false"}, "name": {"english": "Mixer", "translation": "Mixer", "needs_translation": "false"}, "collective_tilt_correction_neg": {"english": "Negative", "translation": "Negative", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Idle Thr%", "translation": "Tail Idle Thr%", "needs_translation": "false"}, "swash_phase": {"english": "Phase Angle", "translation": "Phase Angle", "needs_translation": "false"}, "collective_tilt_correction": {"english": "Collective Tilt Correction", "translation": "Collective Tilt Correction", "needs_translation": "false"}, "swash_pitch_limit": {"english": "Total Pitch Limit", "translation": "Total Pitch Limit", "needs_translation": "false"}}, "about": {"help_p1": {"english": "This page provides some useful information that you may be asked for when requesting support.", "translation": "This page provides some useful information that you may be asked for when requesting support.", "needs_translation": "false"}, "msgbox_credits": {"english": "Credits", "translation": "Credits", "needs_translation": "false"}, "ethos_version": {"english": "Ethos Version", "translation": "Ethos Version", "needs_translation": "false"}, "rf_version": {"english": "Rotorflight Version", "translation": "Rotorflight Version", "needs_translation": "false"}, "fc_version": {"english": "FC Version", "translation": "FC Version", "needs_translation": "false"}, "name": {"english": "About", "translation": "About", "needs_translation": "false"}, "supported_versions": {"english": "Supported MSP Versions", "translation": "Supported MSP Versions", "needs_translation": "false"}, "license": {"english": "You may copy, distribute, and modify the software as long as you track changes/dates in source files. Any modifications to or software including (via compiler) GPL-licensed code must also be made available under the GPL along with build & install instructions.", "translation": "You may copy, distribute, and modify the software as long as you track changes/dates in source files. Any modifications to or software including (via compiler) GPL-licensed code must also be made available under the GPL along with build & install instructions.", "needs_translation": "false"}, "simulation": {"english": "Simulation", "translation": "Simulation", "needs_translation": "false"}, "help_p2": {"english": "For support, please first read the help pages on www.rotorflight.org", "translation": "For support, please first read the help pages on www.rotorflight.org", "needs_translation": "false"}, "opener": {"english": "Rotorflight is an open source project. Contribution from other like minded people, keen to assist in making this software even better, is welcomed and encouraged. You do not have to be a hardcore programmer to help.", "translation": "Rotorflight is an open source project. Contribution from other like minded people, keen to assist in making this software even better, is welcomed and encouraged. You do not have to be a hardcore programmer to help.", "needs_translation": "false"}, "version": {"english": "Version", "translation": "Version", "needs_translation": "false"}, "msp_version": {"english": "MSP Version", "translation": "MSP Version", "needs_translation": "false"}, "credits": {"english": "Notable contributors to both the Rotorflight firmware and this software are: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... and many more who have spent hours testing and providing feedback!", "translation": "Notable contributors to both the Rotorflight firmware and this software are: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... and many more who have spent hours testing and providing feedback!", "needs_translation": "false"}, "msp_transport": {"english": "MSP Transport", "translation": "MSP Transport", "needs_translation": "false"}}, "rates_advanced": {"dyn_ceiling_gain": {"english": "Dynamic ceiling gain", "translation": "Dynamic ceiling gain", "needs_translation": "false"}, "acc_limit": {"english": "Accelerometer Limit", "translation": "Accelerometer Limit", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "yaw_dynamics": {"english": "Yaw dynamics", "translation": "Yaw dynamics", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "Pitch", "needs_translation": "false"}, "col": {"english": "Col", "translation": "Col", "needs_translation": "false"}, "setpoint_boost_cutoff": {"english": "Setpoint boost cutoff", "translation": "Setpoint boost cutoff", "needs_translation": "false"}, "yaw_dynamic_deadband_gain": {"english": "D. <PERSON>", "translation": "D. <PERSON>", "needs_translation": "false"}, "rates_type": {"english": "Rates Type", "translation": "Rates Type", "needs_translation": "false"}, "setpoint_boost_gain": {"english": "Setpoint boost gain", "translation": "Setpoint boost gain", "needs_translation": "false"}, "msg_reset_to_defaults": {"english": "Rate type changed. Values will be reset to defaults.", "translation": "Rate type changed. Values will be reset to defaults.", "needs_translation": "false"}, "yaw_dynamic_ceiling_gain": {"english": "Ceiling", "translation": "Ceiling", "needs_translation": "false"}, "yaw_boost": {"english": "Yaw boost", "translation": "Yaw boost", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "rate_table": {"english": "Rate Table", "translation": "Rate Table", "needs_translation": "false"}, "dynamics": {"english": "Dynamics", "translation": "Dynamics", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "Yaw", "needs_translation": "false"}, "yaw_dynamic_deadband_filter": {"english": "Filter", "translation": "Filter", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "Rates", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "help_rate_table": {"english": "Please select the rate you would like to use. Saving will apply the choice to the active profile.", "translation": "Please select the rate you would like to use. Saving will apply the choice to the active profile.", "needs_translation": "false"}, "help_p1": {"english": "Rates type: Choose the rate type you prefer flying with. Raceflight and Actual are the most straightforward.", "translation": "Rates type: Choose the rate type you prefer flying with. Raceflight and Actual are the most straightforward.", "needs_translation": "false"}, "pitch_boost": {"english": "Pitch boost", "translation": "Pitch boost", "needs_translation": "false"}, "help_p2": {"english": "Dynamics: Applied regardless of rates type. Typically left on defaults but can be adjusted to smooth heli movements, like with scale helis.", "translation": "Dynamics: Applied regardless of rates type. Typically left on defaults but can be adjusted to smooth heli movements, like with scale helis.", "needs_translation": "false"}, "accel_limit": {"english": "Accel", "translation": "Accel", "needs_translation": "false"}, "dyn_deadband_filter": {"english": "Dynamic deadband filter", "translation": "Dynamic deadband filter", "needs_translation": "false"}, "roll_boost": {"english": "Roll boost", "translation": "Roll boost", "needs_translation": "false"}, "dyn_deadband_gain": {"english": "Dynamic deadband gain", "translation": "Dynamic deadband gain", "needs_translation": "false"}, "collective_dynamics": {"english": "Collective dynamics", "translation": "Collective dynamics", "needs_translation": "false"}, "roll_dynamics": {"english": "Roll dynamics", "translation": "Roll dynamics", "needs_translation": "false"}, "collective_boost": {"english": "Collective boost", "translation": "Collective boost", "needs_translation": "false"}, "pitch_dynamics": {"english": "Pitch dynamics", "translation": "Pitch dynamics", "needs_translation": "false"}, "response_time": {"english": "Response Time", "translation": "Response Time", "needs_translation": "false"}}, "servos": {"tbl_yes": {"english": "YES", "translation": "YES", "needs_translation": "false"}, "enable_servo_override": {"english": "Enable servo override", "translation": "Enable servo override", "needs_translation": "false"}, "disabling_servo_override": {"english": "Disabling servo override...", "translation": "Disabling servo override...", "needs_translation": "false"}, "help_tool_p3": {"english": "Minimum/Maximum: Adjust the end points of the selected servo.", "translation": "Minimum/Maximum: Adjust the end points of the selected servo.", "needs_translation": "false"}, "tail": {"english": "TAIL", "translation": "TAIL", "needs_translation": "false"}, "scale_negative": {"english": "Scale Negative", "translation": "Scale Negative", "needs_translation": "false"}, "help_tool_p1": {"english": "Override: [*] Enable override to allow real-time updates of servo center point.", "translation": "Override: [*] Enable override to allow real-time updates of servo center point.", "needs_translation": "false"}, "tbl_no": {"english": "NO", "translation": "NO", "needs_translation": "false"}, "maximum": {"english": "Maximum", "translation": "Maximum", "needs_translation": "false"}, "help_tool_p6": {"english": "Speed: The speed the servo moves. Generally only used for the cyclic servos to help the swash move evenly. Optional - leave all at 0 if unsure.", "translation": "Speed: The speed the servo moves. Generally only used for the cyclic servos to help the swash move evenly. Optional - leave all at 0 if unsure.", "needs_translation": "false"}, "help_fields_rate": {"english": "Servo PWM rate.", "translation": "Servo PWM rate.", "needs_translation": "false"}, "cyc_pitch": {"english": "CYC.PITCH", "translation": "CYC.PITCH", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Center", "needs_translation": "false"}, "minimum": {"english": "Minimum", "translation": "Minimum", "needs_translation": "false"}, "speed": {"english": "Speed", "translation": "Speed", "needs_translation": "false"}, "help_fields_speed": {"english": "Servo motion speed in milliseconds.", "translation": "Servo motion speed in milliseconds.", "needs_translation": "false"}, "disable_servo_override": {"english": "Disable servo override", "translation": "Disable servo override", "needs_translation": "false"}, "help_fields_scale_pos": {"english": "Servo positive scaling.", "translation": "Servo positive scaling.", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "Saving data...", "needs_translation": "false"}, "cyc_left": {"english": "CYC.LEFT", "translation": "CYC.LEFT", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "Saving", "needs_translation": "false"}, "name": {"english": "Servos", "translation": "Servos", "needs_translation": "false"}, "help_tool_p5": {"english": "Rate: The frequency the servo runs best at - check with manufacturer.", "translation": "Rate: The frequency the servo runs best at - check with manufacturer.", "needs_translation": "false"}, "help_tool_p2": {"english": "Center: Adjust the center position of the servo.", "translation": "Center: Adjust the center position of the servo.", "needs_translation": "false"}, "enabling_servo_override": {"english": "Enabling servo override...", "translation": "Enabling servo override...", "needs_translation": "false"}, "servo_prefix": {"english": "SERVO ", "translation": "SERVO ", "needs_translation": "false"}, "reverse": {"english": "Reverse", "translation": "Reverse", "needs_translation": "false"}, "enable_servo_override_msg": {"english": "Servo override allows you to 'trim' your servo center point in real time.", "translation": "Servo override allows you to 'trim' your servo center point in real time.", "needs_translation": "false"}, "cyc_right": {"english": "CYC.RIGHT", "translation": "CYC.RIGHT", "needs_translation": "false"}, "help_default_p2": {"english": "Primary flight controls that use the rotorflight mixer will display in the section called 'mixer'.", "translation": "Primary flight controls that use the rotorflight mixer will display in the section called 'mixer'.", "needs_translation": "false"}, "scale_positive": {"english": "Scale Positive", "translation": "Scale Positive", "needs_translation": "false"}, "help_default_p1": {"english": "Please select the servo you would like to configure from the list below.", "translation": "Please select the servo you would like to configure from the list below.", "needs_translation": "false"}, "servo_override": {"english": "Servo Override", "translation": "Servo Override", "needs_translation": "false"}, "disable_servo_override_msg": {"english": "Return control of the servos to the flight controller.", "translation": "Return control of the servos to the flight controller.", "needs_translation": "false"}, "help_fields_min": {"english": "Servo negative travel limit.", "translation": "Servo negative travel limit.", "needs_translation": "false"}, "help_default_p3": {"english": "Any other servos that are not controlled by the primary flight mixer will be displayed in the section called 'Other servos'.", "translation": "Any other servos that are not controlled by the primary flight mixer will be displayed in the section called 'Other servos'.", "needs_translation": "false"}, "help_fields_mid": {"english": "Servo center position pulse width.", "translation": "Servo center position pulse width.", "needs_translation": "false"}, "help_fields_scale_neg": {"english": "Servo negative scaling.", "translation": "Servo negative scaling.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "help_tool_p4": {"english": "Scale: Adjust the amount the servo moves for a given input.", "translation": "Scale: Adjust the amount the servo moves for a given input.", "needs_translation": "false"}, "help_fields_flags": {"english": "0 = <PERSON><PERSON><PERSON>, 1=Reverse, 2 = Geo Correction, 3 = Reverse + Geo Correction", "translation": "0 = <PERSON><PERSON><PERSON>, 1=Reverse, 2 = Geo Correction, 3 = Reverse + Geo Correction", "needs_translation": "false"}, "geometry": {"english": "Geometry", "translation": "Geometry", "needs_translation": "false"}, "help_fields_max": {"english": "Servo positive travel limit.", "translation": "Servo positive travel limit.", "needs_translation": "false"}}, "profile_autolevel": {"acro_trainer": {"english": "Acro trainer", "translation": "Acro trainer", "needs_translation": "false"}, "angle_mode": {"english": "Angle mode", "translation": "Angle mode", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "name": {"english": "Autolevel", "translation": "Autolevel", "needs_translation": "false"}, "help_p1": {"english": "Acro Trainer: How aggressively the heli tilts back to level when flying in Acro Trainer Mode.", "translation": "Acro Trainer: How aggressively the heli tilts back to level when flying in Acro Trainer Mode.", "needs_translation": "false"}, "horizon_mode": {"english": "Horizon mode", "translation": "Horizon mode", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Angle Mode: How aggressively the heli tilts back to level when flying in Angle Mode.", "translation": "Angle Mode: How aggressively the heli tilts back to level when flying in Angle Mode.", "needs_translation": "false"}, "help_p3": {"english": "Horizon Mode: How aggressively the heli tilts back to level when flying in Horizon Mode.", "translation": "Horizon Mode: How aggressively the heli tilts back to level when flying in Horizon Mode.", "needs_translation": "false"}}, "filters": {"filter_type": {"english": "Filter type", "translation": "Filter type", "needs_translation": "false"}, "help_p4": {"english": "Dynamic Notch Filters: Automatically creates notch filters within the min and max frequency range.", "translation": "Dynamic Notch Filters: Automatically creates notch filters within the min and max frequency range.", "needs_translation": "false"}, "notch_c": {"english": "Notch Count", "translation": "Notch Count", "needs_translation": "false"}, "rpm_preset": {"english": "Type", "translation": "Type", "needs_translation": "false"}, "lowpass_1": {"english": "Lowpass 1", "translation": "Lowpass 1", "needs_translation": "false"}, "rpm_min_hz": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "help_p2": {"english": "Gyro lowpass: Lowpass filters for the gyro signal. Typically left at default.", "translation": "Gyro lowpass: Lowpass filters for the gyro signal. Typically left at default.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Cutoff", "needs_translation": "false"}, "notch_1": {"english": "Notch 1", "translation": "Notch 1", "needs_translation": "false"}, "max_cutoff": {"english": "Max cutoff", "translation": "Max cutoff", "needs_translation": "false"}, "help_p3": {"english": "Gyro notch filters: Use for filtering specific frequency ranges. Typically not needed in most helis.", "translation": "Gyro notch filters: Use for filtering specific frequency ranges. Typically not needed in most helis.", "needs_translation": "false"}, "lowpass_2": {"english": "Lowpass 2", "translation": "Lowpass 2", "needs_translation": "false"}, "rpm_filter": {"english": "RPM filter", "translation": "RPM filter", "needs_translation": "false"}, "help_p1": {"english": "Typically you would not edit this page without checking your Blackbox logs!", "translation": "Typically you would not edit this page without checking your Blackbox logs!", "needs_translation": "false"}, "dyn_notch": {"english": "Dynamic Filters", "translation": "Dynamic Filters", "needs_translation": "false"}, "notch_q": {"english": "Notch Q", "translation": "Notch Q", "needs_translation": "false"}, "lowpass_1_dyn": {"english": "Lowpass 1 dyn.", "translation": "Lowpass 1 dyn.", "needs_translation": "false"}, "notch_min_hz": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "notch_max_hz": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "notch_2": {"english": "Notch 2", "translation": "Notch 2", "needs_translation": "false"}, "name": {"english": "Filters", "translation": "Filters", "needs_translation": "false"}, "min_cutoff": {"english": "Min cutoff", "translation": "Min cutoff", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Center", "needs_translation": "false"}}, "status": {"arming_disable_flag_3": {"english": "Bad RX Recovery", "translation": "Bad RX Recovery", "needs_translation": "false"}, "arming_disable_flag_20": {"english": "RPM Filter", "translation": "RPM Filter", "needs_translation": "false"}, "arming_disable_flag_11": {"english": "Load", "translation": "Load", "needs_translation": "false"}, "arming_disable_flag_22": {"english": "DSHOT Bitbang", "translation": "DSHOT Bitbang", "needs_translation": "false"}, "dataflash_free_space": {"english": "Dataflash Free Space", "translation": "Dataflash Free Space", "needs_translation": "false"}, "arming_disable_flag_25": {"english": "Arm Switch", "translation": "Arm Switch", "needs_translation": "false"}, "erasing": {"english": "Erasing", "translation": "Erasing", "needs_translation": "false"}, "arming_disable_flag_9": {"english": "Boot <PERSON>", "translation": "Boot <PERSON>", "needs_translation": "false"}, "megabyte": {"english": "MB", "translation": "MB", "needs_translation": "false"}, "arming_disable_flag_17": {"english": "Paralyze", "translation": "Paralyze", "needs_translation": "false"}, "arming_disable_flag_5": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "arming_disable_flag_8": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_1": {"english": "Fail Safe", "translation": "Fail Safe", "needs_translation": "false"}, "cpu_load": {"english": "CPU Load", "translation": "CPU Load", "needs_translation": "false"}, "arming_disable_flag_15": {"english": "BST", "translation": "BST", "needs_translation": "false"}, "arming_disable_flag_12": {"english": "Calibrating", "translation": "Calibrating", "needs_translation": "false"}, "arming_disable_flag_19": {"english": "Resc", "translation": "Resc", "needs_translation": "false"}, "arming_disable_flag_4": {"english": "Box Fail Safe", "translation": "Box Fail Safe", "needs_translation": "false"}, "arming_disable_flag_24": {"english": "Motor Protocol", "translation": "Motor Protocol", "needs_translation": "false"}, "real_time_load": {"english": "Real-time Load", "translation": "Real-time Load", "needs_translation": "false"}, "help_p2": {"english": "To erase the dataflash for more log file storage, press the button on the menu denoted by a '*'.", "translation": "To erase the dataflash for more log file storage, press the button on the menu denoted by a '*'.", "needs_translation": "false"}, "arming_disable_flag_2": {"english": "RX Fail Safe", "translation": "RX Fail Safe", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "arming_disable_flag_0": {"english": "No Gyro", "translation": "No Gyro", "needs_translation": "false"}, "arming_disable_flag_18": {"english": "GPS", "translation": "GPS", "needs_translation": "false"}, "help_p1": {"english": "Use this page to view your current flight controller status. This can be useful when determining why your heli will not arm.", "translation": "Use this page to view your current flight controller status. This can be useful when determining why your heli will not arm.", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Arming Flags", "needs_translation": "false"}, "unsupported": {"english": "Unsupported", "translation": "Unsupported", "needs_translation": "false"}, "erase_prompt": {"english": "Would you like to erase the dataflash?", "translation": "Would you like to erase the dataflash?", "needs_translation": "false"}, "erase": {"english": "Erase", "translation": "Erase", "needs_translation": "false"}, "arming_disable_flag_10": {"english": "No Pre Arm", "translation": "No Pre Arm", "needs_translation": "false"}, "arming_disable_flag_21": {"english": "Reboot Required", "translation": "Reboot Required", "needs_translation": "false"}, "name": {"english": "Status", "translation": "Status", "needs_translation": "false"}, "arming_disable_flag_13": {"english": "CLI", "translation": "CLI", "needs_translation": "false"}, "arming_disable_flag_14": {"english": "CMS Menu", "translation": "CMS Menu", "needs_translation": "false"}, "arming_disable_flag_16": {"english": "MSP", "translation": "MSP", "needs_translation": "false"}, "arming_disable_flag_7": {"english": "<PERSON>hrottle", "translation": "<PERSON>hrottle", "needs_translation": "false"}, "erasing_dataflash": {"english": "Erasing dataflash...", "translation": "Erasing dataflash...", "needs_translation": "false"}, "arming_disable_flag_23": {"english": "Acc Calibration", "translation": "Acc Calibration", "needs_translation": "false"}}, "profile_pidbandwidth": {"help_p1": {"english": "PID Bandwidth: Overall bandwidth in HZ used by the PID loop.", "translation": "PID Bandwidth: Overall bandwidth in HZ used by the PID loop.", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "name": {"english": "PID Bandwidth", "translation": "PID Bandwidth", "needs_translation": "false"}, "bterm_cutoff": {"english": "B-term cut-off", "translation": "B-term cut-off", "needs_translation": "false"}, "help_p3": {"english": "B-term cutoff: B-term cutoff frequency in HZ.", "translation": "B-term cutoff: B-term cutoff frequency in HZ.", "needs_translation": "false"}, "dterm_cutoff": {"english": "D-term cut-off", "translation": "D-term cut-off", "needs_translation": "false"}, "help_p2": {"english": "D-term cutoff: D-term cutoff frequency in HZ.", "translation": "D-term cutoff: D-term cutoff frequency in HZ.", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}}}, "navigation_save": {"english": "SAVE", "translation": "SAVE", "needs_translation": "false"}, "menu_section_flight_tuning": {"english": "Flight Tuning", "translation": "Flight Tuning", "needs_translation": "false"}, "error_timed_out": {"english": "Error: timed out", "translation": "Error: timed out", "needs_translation": "false"}, "check_rf_module_on": {"english": "Please check your rf module is turned on.", "translation": "Please check your rf module is turned on.", "needs_translation": "false"}, "msg_saving": {"english": "Saving...", "translation": "Saving...", "needs_translation": "false"}, "msg_save_not_commited": {"english": "Save not committed to EEPROM", "translation": "Save not committed to EEPROM", "needs_translation": "false"}, "menu_section_advanced": {"english": "Advanced", "translation": "Advanced", "needs_translation": "false"}, "msg_loading_from_fbl": {"english": "Loading data from flight controller...", "translation": "Loading data from flight controller...", "needs_translation": "false"}, "msg_reload_settings": {"english": "Reload data from flight controller?", "translation": "Reload data from flight controller?", "needs_translation": "false"}, "menu_section_tools": {"english": "Tools", "translation": "Tools", "needs_translation": "false"}, "msg_connecting": {"english": "Connecting", "translation": "Connecting", "needs_translation": "false"}, "msg_save_current_page": {"english": "Save current page to flight controller?", "translation": "Save current page to flight controller?", "needs_translation": "false"}, "btn_ok_long": {"english": "                OK                ", "translation": "                OK                ", "needs_translation": "false"}, "check_discovered_sensors": {"english": "Please check you have discovered all sensors.", "translation": "Please check you have discovered all sensors.", "needs_translation": "false"}, "msg_loading": {"english": "Loading...", "translation": "Loading...", "needs_translation": "false"}, "check_heli_on": {"english": "Please check your heli is powered up and radio connected.", "translation": "Please check your heli is powered up and radio connected.", "needs_translation": "false"}, "check_bg_task": {"english": "Please enable the background task.", "translation": "Please enable the background task.", "needs_translation": "false"}, "navigation_tools": {"english": "*", "translation": "*", "needs_translation": "false"}, "check_supported_version": {"english": "This version of the Lua script \ncan't be used with the selected model", "translation": "This version of the Lua script \ncan't be used with the selected model", "needs_translation": "false"}}