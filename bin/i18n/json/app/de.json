{"btn_ok": {"english": "          OK           ", "translation": "          OK           ", "needs_translation": "false"}, "btn_close": {"english": "CLOSE", "translation": "SCHLIESSEN", "needs_translation": "false"}, "navigation_menu": {"english": "MENU", "translation": "MENUE", "needs_translation": "false"}, "menu_section_hardware": {"english": "Hardware", "translation": "Hardware", "needs_translation": "false"}, "msg_please_disarm_to_save_warning": {"english": "Settings will only be saved to e<PERSON>rom on disarm", "translation": "Einstellungen werden nur beim Entschaerfen im EEPROM gespeichert", "needs_translation": "false"}, "msg_saving_settings": {"english": "Saving settings...", "translation": "Einstellungen werden gespeichert...", "needs_translation": "false"}, "msg_saving_to_fbl": {"english": "Saving data to flight controller...", "translation": "Daten werden auf dem Flugcontroller gespeichert...", "needs_translation": "false"}, "navigation_reload": {"english": "RELOAD", "translation": "NEU LADEN", "needs_translation": "false"}, "menu_section_developer": {"english": "Developer", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "check_msp_version": {"english": "Unable to determine MSP version in use.", "translation": "MSP-Version konnte nicht ermittelt werden.", "needs_translation": "false"}, "menu_section_about": {"english": "About", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "msg_please_disarm_to_save": {"english": "Please disarm to save", "translation": "Bitte disarmen, um die Datensicherheit zu gewaehrleisten.", "needs_translation": "false"}, "unit_hertz": {"english": "Hz", "translation": "Hz", "needs_translation": "false"}, "msg_rebooting": {"english": "Rebooting...", "translation": "Neustart...", "needs_translation": "false"}, "msg_save_settings": {"english": "Save settings", "translation": "Einstellungen speichern", "needs_translation": "false"}, "btn_cancel": {"english": "CANCEL", "translation": "ABBRECHEN", "needs_translation": "false"}, "msg_connecting_to_fbl": {"english": "Connecting to flight controller...", "translation": "Verbindung zum Flugcontroller wird hergestellt...", "needs_translation": "false"}, "navigation_help": {"english": "?", "translation": "?", "needs_translation": "false"}, "modules": {"stats": {"name": {"english": "Stats", "translation": "Statistiken", "needs_translation": "false"}, "totalflighttime": {"english": "Total Flight Time", "translation": "Gesamtflugzeit", "needs_translation": "false"}, "flightcount": {"english": "Flight Count", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "lastflighttime": {"english": "Last Flight Time", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Use this module to update the recorded flight statistics on the flight controller.", "translation": "Verwenden Sie dieses Modul, um die aufgezeichneten Flugstatistiken am Flugcontroller upzudaten.", "needs_translation": "false"}}, "settings": {"name": {"english": "Settings", "translation": "Einstellungen", "needs_translation": "false"}, "no_themes_available_to_configure": {"english": "No configurable themes installed on this device", "translation": "<PERSON>ine konfigurierbaren Designs auf dem Geraet installiert.", "needs_translation": "false"}, "txt_audio_timer": {"english": "Timer", "translation": "Timer", "needs_translation": "false"}, "txt_audio_events": {"english": "Events", "translation": "Events", "needs_translation": "false"}, "txt_audio_switches": {"english": "Switches", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "txt_iconsize": {"english": "Icon Size", "translation": "Icongroesse", "needs_translation": "false"}, "txt_general": {"english": "General", "translation": "Allgemein", "needs_translation": "false"}, "txt_text": {"english": "TEXT", "translation": "TEXT", "needs_translation": "false"}, "txt_small": {"english": "SMALL", "translation": "KLEIN", "needs_translation": "false"}, "txt_large": {"english": "LARGE", "translation": "GROSS", "needs_translation": "false"}, "txt_syncname": {"english": "Sync model name", "translation": "<PERSON><PERSON>ame synchronisieren", "needs_translation": "false"}, "txt_devtools": {"english": "Developer Tools", "translation": "Entwickler-Werkzeuge", "needs_translation": "false"}, "txt_apiversion": {"english": "API Version", "translation": "API Version", "needs_translation": "false"}, "txt_logging": {"english": "Logging", "translation": "Protokollierung", "needs_translation": "false"}, "txt_compilation": {"english": "Compilation", "translation": "Kompilation", "needs_translation": "false"}, "txt_loglocation": {"english": "Log location", "translation": "Protokoll-Speicherort", "needs_translation": "false"}, "txt_console": {"english": "CONSOLE", "translation": "KONSOLE", "needs_translation": "false"}, "txt_consolefile": {"english": "CONSOLE & FILE", "translation": "KONSOLE & DATEI", "needs_translation": "false"}, "txt_loglevel": {"english": "Log level", "translation": "Protokollierungsgrad", "needs_translation": "false"}, "txt_off": {"english": "OFF", "translation": "AUS", "needs_translation": "false"}, "txt_info": {"english": "INFO", "translation": "INFO", "needs_translation": "false"}, "txt_debug": {"english": "DEBUG", "translation": "DEBUG", "needs_translation": "false"}, "txt_mspdata": {"english": "Log msp data", "translation": "Protokolliere MSP Daten", "needs_translation": "false"}, "txt_queuesize": {"english": "Log MSP queue size", "translation": "Protokolliere MSP Warteschlangengroesse", "needs_translation": "false"}, "txt_memusage": {"english": "Log memory usage", "translation": "Protokolliere Speicherauslastung", "needs_translation": "false"}, "txt_batttype": {"english": "Tx Battery Options", "translation": "Tx Battery Options", "needs_translation": "true"}, "txt_battdef": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "true"}, "txt_batttext": {"english": "Text", "translation": "Text", "needs_translation": "true"}, "txt_battdig": {"english": "Digital", "translation": "Digital", "needs_translation": "true"}, "dashboard": {"english": "Dashboard", "translation": "Dashboard", "needs_translation": "false"}, "dashboard_theme": {"english": "Theme", "translation": "Design", "needs_translation": "false"}, "dashboard_theme_panel_global": {"english": "Default theme for all models", "translation": "Standard-Design fuer alle Modelle", "needs_translation": "false"}, "dashboard_theme_panel_model": {"english": "Optional theme for this model", "translation": "Optionales Design fuer dieses Modell", "needs_translation": "false"}, "dashboard_theme_panel_model_disabled": {"english": "Disabled", "translation": "Ausgeschalten", "needs_translation": "false"}, "dashboard_settings": {"english": "Settings", "translation": "Einstellungen", "needs_translation": "false"}, "dashboard_theme_preflight": {"english": "Preflight Theme", "translation": "Vorflug-Design", "needs_translation": "false"}, "dashboard_theme_inflight": {"english": "Inflight Theme", "translation": "Flug-Design", "needs_translation": "false"}, "dashboard_theme_postflight": {"english": "Postflight Theme", "translation": "Nachflug-Design", "needs_translation": "false"}, "audio": {"english": "Audio", "translation": "Audio", "needs_translation": "false"}, "localizations": {"english": "Localization", "translation": "Lokalisierung", "needs_translation": "false"}, "txt_development": {"english": "Development", "translation": "Entwicklung", "needs_translation": "false"}, "temperature_unit": {"english": "Temperature Unit", "translation": "Temperatureinheit", "needs_translation": "false"}, "altitude_unit": {"english": "Altitude Unit", "translation": "Hoeheneinheit", "needs_translation": "false"}, "celcius": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "fahrenheit": {"english": "Fahrenheit", "translation": "Fahrenheit", "needs_translation": "false"}, "meters": {"english": "Meters", "translation": "<PERSON>er", "needs_translation": "false"}, "feet": {"english": "Feet", "translation": "Fuss", "needs_translation": "false"}, "warning": {"english": "Warning", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "governor_state": {"english": "Governor State", "translation": "Governor Status", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Arming Flags", "needs_translation": "false"}, "voltage": {"english": "Voltage", "translation": "Spannung", "needs_translation": "false"}, "pid_rates_profile": {"english": "PID/Rates Profile", "translation": "PID/Raten Profil", "needs_translation": "false"}, "pid_profile": {"english": "PID Profile", "translation": "PID Profil", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "Raten Profil", "needs_translation": "false"}, "esc_temperature": {"english": "ESC Temperature", "translation": "ESC Temperatur", "needs_translation": "false"}, "esc_threshold": {"english": "Thresh<PERSON> (°)", "translation": "Schwellwert (°)", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "BEC Spannung", "needs_translation": "false"}, "bec_threshold": {"english": "<PERSON><PERSON><PERSON><PERSON> (V)", "translation": "Schwellwert (V)", "needs_translation": "false"}, "fuel": {"english": "Fuel", "translation": "Kraftstoff", "needs_translation": "false"}, "fuel_callout_default": {"english": "Default (Only at 10%)", "translation": "Standard (<PERSON><PERSON> wenn 10%)", "needs_translation": "false"}, "fuel_callout_10": {"english": "Every 10%", "translation": "Alle 10%", "needs_translation": "false"}, "fuel_callout_20": {"english": "Every 20%", "translation": "Alle 20%", "needs_translation": "false"}, "fuel_callout_25": {"english": "Every 25%", "translation": "Alle 25%", "needs_translation": "false"}, "fuel_callout_50": {"english": "Every 50%", "translation": "Alle 50%", "needs_translation": "false"}, "fuel_callout_percent": {"english": "Callout %", "translation": "Ansage %", "needs_translation": "false"}, "fuel_repeats_below": {"english": "Repeats below 0%", "translation": "Wiederholungen unter 0%", "needs_translation": "false"}, "fuel_haptic_below": {"english": "Haptic below 0%", "translation": "Haptisch unter 0%", "needs_translation": "false"}, "timer_alerting": {"english": "Timer Alerting", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "timer_elapsed_alert_mode": {"english": "Timer <PERSON><PERSON><PERSON>", "translation": "Alarmierung bei Timer-Ablauf", "needs_translation": "false"}, "timer_prealert_options": {"english": "Pre-timer <PERSON><PERSON>", "translation": "Vor-timer Alarmierungsoption<PERSON>", "needs_translation": "false"}, "timer_prealert": {"english": "Pre-timer <PERSON><PERSON>", "translation": "Vor-timer <PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "timer_alert_period": {"english": "Alert <PERSON>", "translation": "Alarmierungsperiode", "needs_translation": "false"}, "timer_postalert_options": {"english": "Post-timer <PERSON><PERSON>", "translation": "Nach-timer Alarmierungs Optionen", "needs_translation": "false"}, "timer_postalert": {"english": "Post-timer <PERSON><PERSON>", "translation": "Nach-timer <PERSON><PERSON><PERSON>", "needs_translation": "false"}, "timer_postalert_period": {"english": "Alert <PERSON>", "translation": "Alarmierungs Periode", "needs_translation": "false"}, "timer_postalert_interval": {"english": "<PERSON><PERSON>", "translation": "Alarmierungs Interval", "needs_translation": "false"}}, "validate_sensors": {"help_p1": {"english": "This tool attempts to list all the sensors that you are not receiving in a concise list.", "translation": "<PERSON><PERSON>, eine kurze Liste aller Sensoren zu erstellen, die nicht empfangen werden.", "needs_translation": "false"}, "invalid": {"english": "INVALID", "translation": "UNGUELTIG", "needs_translation": "false"}, "name": {"english": "Sensors", "translation": "Sensoren", "needs_translation": "false"}, "msg_repair": {"english": "Enable required sensors on flight controller?", "translation": "Erforderliche Sensoren auf dem Flugcontroller aktivieren?", "needs_translation": "false"}, "msg_repair_fin": {"english": "The flight controller has been configured? You may need to perform a discover sensors to see the changes.", "translation": "Der Flugcontroller wurde konfiguriert? Moeglicherweise muessen Sie Sensoren neu suchen, um die Aenderungen zu sehen.", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "Use this tool to ensure you are sending the correct sensors.", "translation": "Verwenden Sie dieses Tool, um sicherzustellen, dass Sie die richtigen Sensoren senden.", "needs_translation": "false"}}, "msp_exp": {"help_p1": {"english": "This tool provides the ability to send a custom byte string to the flight controller. It is useful for developers when debugging values.", "translation": "Dieses <PERSON>l ermoeglicht das Senden einer benutzerdefinierten Byte-Zeichenfolge an den Flugcontroller. Es ist nuetzlich fuer Entwickler beim Debuggen von <PERSON>rten.", "needs_translation": "false"}, "name": {"english": "MSP Expermental", "translation": "MSP Experimentell", "needs_translation": "false"}, "help_p2": {"english": "If you do not understand what you are doing, do not use it as bad things can happen.", "translation": "<PERSON><PERSON> <PERSON>e nicht verstehen, was <PERSON><PERSON> t<PERSON>, verwenden Si<PERSON> es nicht, da dies zu <PERSON>en fuehren kann.", "needs_translation": "false"}}, "esc_tools": {"unknown": {"english": "UNKNOWN", "translation": "UNBEKANNT", "needs_translation": "false"}, "name": {"english": "ESC Tools", "translation": "ESC-Werkzeuge", "needs_translation": "false"}, "please_powercycle": {"english": "Please power cycle the ESC...", "translation": "<PERSON>te schalten Sie den ESC aus und wieder ein...", "needs_translation": "false"}, "mfg": {"hw5": {"esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "brake_force": {"english": "Brake Force%", "translation": "Bremskraft%", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Drehrichtung", "needs_translation": "false"}, "soft_start": {"english": "Soft Start", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "Hobbywing V5", "translation": "Hobbywing V5", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Grenzwerte", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "BEC-Spannung", "needs_translation": "false"}, "gov_i_gain": {"english": "I-G<PERSON>", "translation": "I-Verst.", "needs_translation": "false"}, "startup_time": {"english": "Startup Time", "translation": "Anlaufzeit", "needs_translation": "false"}, "lipo_cell_count": {"english": "LiPo Cell Count", "translation": "LiPo-Zelle<PERSON>hl", "needs_translation": "false"}, "restart_time": {"english": "Restart Time", "translation": "Neustartzeit", "needs_translation": "false"}, "volt_cutoff_type": {"english": "Volt Cutoff Type", "translation": "Spannungsabschaltmodus", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "Motor", "needs_translation": "false"}, "brake_type": {"english": "Brake Type", "translation": "Bremstyp", "needs_translation": "false"}, "brake": {"english": "<PERSON><PERSON><PERSON>", "translation": "Bremse", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Grundlegend", "needs_translation": "false"}, "flight_mode": {"english": "Flight Mode", "translation": "Flugmodus", "needs_translation": "false"}, "auto_restart": {"english": "Auto Restart", "translation": "Automatischer Neustart", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Aktive Freilaufsteuerung", "needs_translation": "false"}, "cutoff_voltage": {"english": "Cutoff Voltage", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Anlaufleistung", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Sonstiges", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Timing", "needs_translation": "false"}, "gov_p_gain": {"english": "P-Gain", "translation": "P-Verst.", "needs_translation": "false"}}, "xdfly": {"hv_bec_voltage": {"english": "HV BEC Voltage", "translation": "HV BEC-Spannung", "needs_translation": "false"}, "gov": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "brake_force": {"english": "Brake Force", "translation": "Bremskraft", "needs_translation": "false"}, "sr_function": {"english": "SR Function", "translation": "SR-Funktion", "needs_translation": "false"}, "name": {"english": "XDFLY", "translation": "XDFLY", "needs_translation": "false"}, "lv_bec_voltage": {"english": "LV BEC Voltage", "translation": "LV BEC-Spannung", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Neustartzeit", "needs_translation": "false"}, "acceleration": {"english": "Acceleration", "translation": "Beschleunigung", "needs_translation": "false"}, "motor_direction": {"english": "Motor Direction", "translation": "Motordrehrichtung", "needs_translation": "false"}, "smart_fan": {"english": "Smart Fan", "translation": "Intelligent<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "Zellenabschaltung", "needs_translation": "false"}, "led_color": {"english": "LED Color", "translation": "LED-Farbe", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Grundlegend", "needs_translation": "false"}, "startup_power": {"english": "Startup Power", "translation": "Anlaufleistung", "needs_translation": "false"}, "motor_poles": {"english": "Motor Poles", "translation": "Motorpole", "needs_translation": "false"}, "capacity_correction": {"english": "Capacity Correction", "translation": "Kapazitaetskorrektur", "needs_translation": "false"}, "timing": {"english": "Timing", "translation": "Timing", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}, "flrtr": {"gov": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "motor_temp_sensor": {"english": "Motor temp sensor", "translation": "Motortemperatursensor", "needs_translation": "false"}, "starting_torque": {"english": "Starting torque", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "cell_count": {"english": "Cell count", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}, "motor_erpm_max": {"english": "Motor ERPM max", "translation": "Max. Motor-ERPM", "needs_translation": "false"}, "name": {"english": "FLYROTOR", "translation": "FLYROTOR", "needs_translation": "false"}, "low_voltage_protection": {"english": "Low voltage protection", "translation": "Unterspannungsschutz", "needs_translation": "false"}, "gov_d": {"english": "Gov-D", "translation": "Gov-D", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry protocol", "translation": "Telemetrieprotokoll", "needs_translation": "false"}, "motor_direction": {"english": "Motor direction", "translation": "Motordrehrichtung", "needs_translation": "false"}, "throttle_protocol": {"english": "Throttle protocol", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "soft_start": {"english": "Soft start", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Sonstiges", "needs_translation": "false"}, "temperature_protection": {"english": "Temperature protection", "translation": "Temperaturschutz", "needs_translation": "false"}, "buzzer_volume": {"english": "Buzzer volume", "translation": "Summerlautstaerke", "needs_translation": "false"}, "timing_angle": {"english": "Timing angle", "translation": "Timing-<PERSON><PERSON>", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "bec_voltage": {"english": "BEC voltage", "translation": "BEC-Spannung", "needs_translation": "false"}, "fan_control": {"english": "Fan control", "translation": "Lueftersteuerung", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Grundlegend", "needs_translation": "false"}, "current_gain": {"english": "Current gain", "translation": "Stromverstaerkung", "needs_translation": "false"}, "led_color": {"english": "LED color", "translation": "LED-Farbe", "needs_translation": "false"}, "motor_temp": {"english": "Motor temperture", "translation": "Motortemperatur", "needs_translation": "false"}, "response_speed": {"english": "Response speed", "translation": "Reaktionsgeschwindigkeit", "needs_translation": "false"}, "battery_capacity": {"english": "Battery capacity", "translation": "Batteriekapazitaet", "needs_translation": "false"}}, "scorp": {"esc_mode": {"english": "ESC Mode", "translation": "ESC-Modus", "needs_translation": "false"}, "min_voltage": {"english": "Min Voltage", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "rotation": {"english": "Rotation", "translation": "Drehrichtung", "needs_translation": "false"}, "telemetry_protocol": {"english": "Telemetry Protocol", "translation": "Telemetrieprotokoll", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "runup_time": {"english": "Runup Time", "translation": "Hochlaufzeit", "needs_translation": "false"}, "motor_startup_sound": {"english": "Motor Startup Sound", "translation": "Motor-Startton", "needs_translation": "false"}, "gov_integral": {"english": "Gov <PERSON><PERSON>", "translation": "Gov <PERSON><PERSON>", "needs_translation": "false"}, "gov_proportional": {"english": "Gov Proportional", "translation": "Gov Proportional", "needs_translation": "false"}, "cutoff_handling": {"english": "Cutoff Handling", "translation": "Abschaltverhalten", "needs_translation": "false"}, "bailout": {"english": "Bailout", "translation": "Bailout", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Grenzwerte", "needs_translation": "false"}, "soft_start_time": {"english": "Soft Start Time", "translation": "Sanftanlauf-Zeit", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "BEC-Spannung", "needs_translation": "false"}, "extra_msg_save": {"english": "Please reboot the ESC to apply the changes", "translation": "Bitte starten Sie den ESC neu, um die Aenderungen zu uebernehmen", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Grundlegend", "needs_translation": "false"}, "max_current": {"english": "Max <PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "max_temperature": {"english": "Max Temperature", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "protection_delay": {"english": "Protection Delay", "translation": "Schutzverzoegerung", "needs_translation": "false"}, "max_used": {"english": "Max Used", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "yge": {"esc_mode": {"english": "ESC Mode", "translation": "ESC-Modus", "needs_translation": "false"}, "esc": {"english": "ESC", "translation": "ESC", "needs_translation": "false"}, "current_limit": {"english": "Current Limit", "translation": "Strombegrenzung", "needs_translation": "false"}, "f3c_auto": {"english": "F3C Autorotation", "translation": "F3C-Autorotation", "needs_translation": "false"}, "name": {"english": "YGE", "translation": "YGE", "needs_translation": "false"}, "max_start_power": {"english": "Max Start Power", "translation": "Maximale Startleistung", "needs_translation": "false"}, "lv_bec_voltage": {"english": "BEC", "translation": "BEC", "needs_translation": "false"}, "pinion_teeth": {"english": "Pinion Teeth", "translation": "Ritzel-<PERSON><PERSON><PERSON>e", "needs_translation": "false"}, "auto_restart_time": {"english": "Auto Restart Time", "translation": "Neustartzeit", "needs_translation": "false"}, "main_teeth": {"english": "Main Teeth", "translation": "Hauptzahnrad-Zaehne", "needs_translation": "false"}, "other": {"english": "Other", "translation": "Sonstiges", "needs_translation": "false"}, "limits": {"english": "Limits", "translation": "Grenzwerte", "needs_translation": "false"}, "cell_cutoff": {"english": "Cell Cutoff", "translation": "Zellenabschaltung", "needs_translation": "false"}, "throttle_response": {"english": "Throttle Response", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "stick_zero_us": {"english": "<PERSON>", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "gov_i": {"english": "Gov-I", "translation": "Gov-I", "needs_translation": "false"}, "motor_pole_pairs": {"english": "Motor Pole Pairs", "translation": "Motor-Polpaare", "needs_translation": "false"}, "stick_range_us": {"english": "Stick Range", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "basic": {"english": "Basic", "translation": "Grundlegend", "needs_translation": "false"}, "min_start_power": {"english": "Min Start Power", "translation": "Minimale Startleistung", "needs_translation": "false"}, "active_freewheel": {"english": "Active Freewheel", "translation": "Aktive Freilaufsteuerung", "needs_translation": "false"}, "direction": {"english": "Direction", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "timing": {"english": "Motor Timing", "translation": "Motortiming", "needs_translation": "false"}, "gov_p": {"english": "Gov-P", "translation": "Gov-P", "needs_translation": "false"}}}, "searching": {"english": "Searching", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "pids": {"help_p1": {"english": "FeedForward (Roll/Pitch): Start at 70, increase until stops are sharp with no drift. Keep roll and pitch equal.", "translation": "Feed<PERSON><PERSON><PERSON> (<PERSON>/Nick): <PERSON>ginnen Sie bei 70, <PERSON><PERSON><PERSON>rt, bis <PERSON><PERSON> scharf sind und kein Driften auftritt. Halten Sie Roll und <PERSON> gleich.", "needs_translation": "false"}, "o": {"english": "O", "translation": "O", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "<PERSON>", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "help_p5": {"english": "Test & Adjust: Fly, observe, and fine-tune for best performance in real conditions.", "translation": "Testen & Anpassen: Fliegen, beobachten und feinjustieren, um die beste Leistung unter realen Bedingungen zu erreichen.", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "PIDs", "translation": "PIDs", "needs_translation": "false"}, "help_p2": {"english": "<PERSON> Gain (Roll/Pitch): Raise gradually for stable piro pitch pumps. Too high causes wobbles; match roll/pitch values.", "translation": "<PERSON><PERSON><PERSON><PERSON> (Roll/Nick): <PERSON><PERSON><PERSON><PERSON> den Wert schrittweise fuer stabile Piro-Pitch-Pumps. Ein zu hoher Wert verursacht Wackeln; Roll- und Nick-Werte sollten uebereinstimmen.", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "b": {"english": "B", "translation": "B", "needs_translation": "false"}, "help_p4": {"english": "Tail Stop Gain (CW/CCW): Adjust separately for clean, bounce-free stops in both directions.", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (CW/CCW): <PERSON><PERSON><PERSON> anpassen fuer saubere, <PERSON><PERSON> ohne Pendeln in beide Richtungen.", "needs_translation": "false"}, "help_p3": {"english": "Tail P/I/D Gains: Increase P until slight wobble in funnels, then back off slightly. Raise I until tail holds firm in hard moves (too high causes slow wag). Adjust D for smooth stops—higher for slow servos, lower for fast ones.", "translation": "Heck P/I/D-Gains: <PERSON><PERSON><PERSON><PERSON>, bis leichtes Wackeln in Funnels auftritt, dann leicht reduzieren. <PERSON><PERSON><PERSON><PERSON> Si<PERSON> I, bis das <PERSON> in harten Manoevern stabil bleibt (zu hoch verursacht langsames Schwingen). D anpassen fuer sanfte Stopps – hoeher fuer langsame Servos, niedriger fuer schnelle.", "needs_translation": "false"}}, "msp_speed": {"seconds_600": {"english": "  600S  ", "translation": "  600S  ", "needs_translation": "false"}, "avg_query_time": {"english": "Average query time", "translation": "Durchschnittliche Anfragezeit", "needs_translation": "false"}, "seconds_30": {"english": "  30S  ", "translation": "  30S  ", "needs_translation": "false"}, "name": {"english": "MSP Speed", "translation": "MSP-Geschwindigkeit", "needs_translation": "false"}, "max_query_time": {"english": "Maximum query time", "translation": "Maximale Anfragezeit", "needs_translation": "false"}, "help_p1": {"english": "This tool attempts to determine the quality of your MSP data link by performing as many large MSP queries within 30 seconds as possible.", "translation": "<PERSON><PERSON>, die Qualitaet Ihrer MSP-Datenverbindung zu bestimmen, indem es innerhalb von 30 Sekunden so viele grosse MSP-Abfragen wie moeglich durchfuehrt.", "needs_translation": "false"}, "retries": {"english": "Retries", "translation": "Wiederholungen", "needs_translation": "false"}, "checksum_errors": {"english": "Checksum errors", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "test_length": {"english": "Test length", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "start": {"english": "Start", "translation": "Start", "needs_translation": "false"}, "memory_free": {"english": "Memory free", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "start_prompt": {"english": "Would you like to start the test? Choose the test run time below.", "translation": "Moechten Sie den Test starten? Waehlen Sie unten die Testlaufzeit aus.", "needs_translation": "false"}, "rf_protocol": {"english": "RF protocol", "translation": "RF-Protokoll", "needs_translation": "false"}, "min_query_time": {"english": "Minimum query time", "translation": "Minimale Anfragezeit", "needs_translation": "false"}, "seconds_120": {"english": "  120S  ", "translation": "  120S  ", "needs_translation": "false"}, "seconds_300": {"english": "  300S  ", "translation": "  300S  ", "needs_translation": "false"}, "testing": {"english": "Testing", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "successful_queries": {"english": "Successful queries", "translation": "Erfolgreiche Anfragen", "needs_translation": "false"}, "timeouts": {"english": "Timeouts", "translation": "Zeitueberschreitungen", "needs_translation": "false"}, "testing_performance": {"english": "Testing MSP performance...", "translation": "MSP-Leistung wird getestet...", "needs_translation": "false"}, "total_queries": {"english": "Total queries", "translation": "Gesamtanzahl <PERSON>gen", "needs_translation": "false"}}, "copyprofiles": {"profile_type": {"english": "Profile Type", "translation": "Profiltyp", "needs_translation": "false"}, "profile_type_pid": {"english": "PID", "translation": "PID", "needs_translation": "false"}, "profile_type_rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "msgbox_save": {"english": "Save settings", "translation": "Einstellungen speichern", "needs_translation": "false"}, "name": {"english": "Copy Profiles", "translation": "Profile kopieren", "needs_translation": "false"}, "help_p1": {"english": "Copy PID profile or Rate profile from Source to Destination.", "translation": "PID-Profil oder Rate-Profil von der Quelle zum Ziel kopieren.", "needs_translation": "false"}, "dest_profile": {"english": "Dest. Profile", "translation": "Zielprofil", "needs_translation": "false"}, "source_profile": {"english": "Source Profile", "translation": "Quellprofil", "needs_translation": "false"}, "msgbox_msg": {"english": "Save current page to flight controller?", "translation": "Aktuelle Seite auf dem Flugcontroller speichern?", "needs_translation": "false"}, "help_p2": {"english": "Choose the source and destinations and save to copy the profile.", "translation": "Waehlen Sie die Quelle und das Ziel aus und speichern Sie, um das Profil zu kopieren.", "needs_translation": "false"}}, "esc_motors": {"min_throttle": {"english": "0% Throttle PWM Value", "translation": "0% Gas PWM-Wert", "needs_translation": "false"}, "tail_motor_ratio": {"english": "Tail Motor Ratio", "translation": "<PERSON>ck<PERSON><PERSON>", "needs_translation": "false"}, "max_throttle": {"english": "100% Throttle PWM value", "translation": "100% Gas PWM-Wert", "needs_translation": "false"}, "main_motor_ratio": {"english": "Main Motor Ratio", "translation": "Hauptmotor", "needs_translation": "false"}, "pinion": {"english": "Pinion", "translation": "R<PERSON>el", "needs_translation": "false"}, "main": {"english": "Main", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Configure the motor and speed controller features.", "translation": "Konfigurieren Sie die Motor- und Governor-Einstellungen.", "needs_translation": "false"}, "rear": {"english": "Rear", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "front": {"english": "Front", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "voltage_correction": {"english": "Voltage Correction", "translation": "Spannungskorrektur", "needs_translation": "false"}, "mincommand": {"english": "Motor Stop PWM Value", "translation": "Motor-Stopp PWM-Wert", "needs_translation": "false"}, "name": {"english": "ESC/Motors", "translation": "ESC/Motoren", "needs_translation": "false"}, "motor_pole_count": {"english": "Motor Pole Count", "translation": "Anzahl der Motorpole", "needs_translation": "false"}, "current_correction": {"english": "Current Correction", "translation": "Stromkorrektur", "needs_translation": "false"}, "consumption_correction": {"english": "Consumption Correction", "translation": "Verbrauchskorrektur", "needs_translation": "false"}}, "radio_config": {"deflection": {"english": "Deflection", "translation": "Ausschlag", "needs_translation": "false"}, "max_throttle": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "stick": {"english": "Stick", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "arming": {"english": "Arming", "translation": "Arm", "needs_translation": "false"}, "yaw_deadband": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "cyclic": {"english": "Cyclic", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "Radio Config", "translation": "Fernsteuerung", "needs_translation": "false"}, "help_p1": {"english": "Configure your radio settings. Stick center, arm, throttle hold, and throttle cut.", "translation": "Konfigurieren Sie Ihre Fernsteuerungseinstellungen: Knueppelzentrum, Arm, Gas-Hold und Gas-Abschaltung.", "needs_translation": "false"}, "min_throttle": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "throttle": {"english": "<PERSON>hrottle", "translation": "Gas", "needs_translation": "false"}, "deadband": {"english": "Deadband", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Zentrum", "needs_translation": "false"}}, "profile_select": {"help_p1": {"english": "Set the current flight profile or rate profile you would like to use.", "translation": "Waehlen Sie das aktuelle Flugprofil oder Rate-Profil aus, das Si<PERSON> verwenden moechten.", "needs_translation": "false"}, "rate_profile": {"english": "Rate Profile", "translation": "Rate-Profil", "needs_translation": "false"}, "pid_profile": {"english": "PID profile", "translation": "PID-Profil", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "Aktuelle Seite auf dem Flugcontroller speichern?", "needs_translation": "false"}, "save_prompt_local": {"english": "Save current page to radio?", "translation": "Aktuelle Seite auf der Fernsteuerung speichern?", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "ABBRECHEN", "needs_translation": "false"}, "name": {"english": "Select Profile", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "Einstellungen speichern", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "help_p2": {"english": "If you use a switch on your radio to change flight or rate modes, this will override this choice as soon as you toggle the switch.", "translation": "<PERSON>n Si<PERSON> einen Schalter an Ihrer Fernsteuerung verwenden, um Flug- oder Rate-Modi zu aendern, wird diese Auswahl ueberschrieben, sobald <PERSON> den Schalter umlegen.", "needs_translation": "false"}}, "profile_governor": {"tail_torque_assist": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "p": {"english": "P", "translation": "P", "needs_translation": "false"}, "i": {"english": "I", "translation": "I", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "cyc": {"english": "Cyc", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "f": {"english": "F", "translation": "F", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "d": {"english": "D", "translation": "D", "needs_translation": "false"}, "help_p1": {"english": "Full headspeed: Headspeed target when at 100% throttle input.", "translation": "Volle Drehzahl: Ziel-Drehzahl bei 100 % Gas-Eingang.", "needs_translation": "false"}, "help_p6": {"english": "Tail Torque Assist: For motorized tails. Gain and limit of headspeed increase when using main rotor torque for yaw assist.", "translation": "Heck-Drehmoment-Unterstuetzung: Fuer motorisierte Heckrotoren. Verstaerkung und Begrenzung der Drehzahlerhoehung beim Gierausgleich durch das Hauptrotordrehmoment.", "needs_translation": "false"}, "help_p4": {"english": "Precomp: Governor precomp gain for yaw, cyclic, and collective inputs.", "translation": "Vorkompensation: Governor-Vorkompensation fuer Gier-, zyklische und kollektive Eingaben.", "needs_translation": "false"}, "max_throttle": {"english": "<PERSON> throttle", "translation": "Max. Gas", "needs_translation": "false"}, "full_headspeed": {"english": "Full headspeed", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "precomp": {"english": "Precomp", "translation": "Vorkompensation", "needs_translation": "false"}, "gain": {"english": "PID master gain", "translation": "PID-Masterverstaerkung", "needs_translation": "false"}, "disabled_message": {"english": "Rotorflight governor is not enabled", "translation": "Rotorflight-Governor ist nicht aktiviert", "needs_translation": "false"}, "help_p3": {"english": "Gains: Fine tuning of the governor.", "translation": "Verstaerkungen: Feineinstellung des Governors.", "needs_translation": "false"}, "col": {"english": "Col", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "min_throttle": {"english": "<PERSON> throttle", "translation": "Min. Gas", "needs_translation": "false"}, "tta_limit": {"english": "Limit", "translation": "Limit", "needs_translation": "false"}, "help_p2": {"english": "PID master gain: How hard the governor works to hold the RPM.", "translation": "PID-Masterverstaerkung: Wie stark der Regler arbeitet, um die Drehzahl zu halten.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "Verstaerkungen", "needs_translation": "false"}, "help_p5": {"english": "Max throttle: The maximum throttle % the governor is allowed to use.", "translation": "Max. Gas: Der maximale Gas-%-<PERSON><PERSON>, den der Governor verwenden darf.", "needs_translation": "false"}, "tta_gain": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "profile_tailrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "Verst.", "needs_translation": "false"}, "help_p4": {"english": "Collective FF Gain: Tail precompensation for collective inputs.", "translation": "Kollektive FF-Verstaerkung: Heck-Vorkompensation fuer kollektive Eingaben.", "needs_translation": "false"}, "collective_impulse_ff": {"english": "Collective Impulse FF", "translation": "Kollektiv-Impuls-FF", "needs_translation": "false"}, "help_p2": {"english": "Precomp Cutoff: Frequency limit for all yaw precompensation actions.", "translation": "Vorkompensation Grenzwert: Frequenzgrenze fuer alle Gier-Vorkompensationsaktionen.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Grenzw.", "needs_translation": "false"}, "help_p3": {"english": "Cyclic FF Gain: Tail precompensation for cyclic inputs.", "translation": "Zyklische FF-Verstaerkung: Heck-Vorkompensation fuer zyklische Eingaben.", "needs_translation": "false"}, "help_p1": {"english": "Yaw Stop Gain: Higher stop gain will make the tail stop more aggressively but may cause oscillations if too high. Adjust CW or CCW to make the yaw stops even.", "translation": "Gier-Stopp-Verstaerkung: Eine hoehere Stopp-Verstaerkung fuehrt zu aggressiveren He<PERSON>ps, kann aber bei zu hohen Werten zu Oszillationen fuehren. Passen Sie CW oder CCW an, um gleichmaessige Gierstopps zu erzielen.", "needs_translation": "false"}, "inertia_precomp": {"english": "Inertia Precomp", "translation": "Traegheits-Vorkomp.", "needs_translation": "false"}, "cyclic_ff_gain": {"english": "Cyclic FF gain", "translation": "Zyklische FF-Verstaerkung", "needs_translation": "false"}, "help_p5": {"english": "Collective Impulse FF: Impulse tail precompensation for collective inputs. If you need extra tail precompensation at the beginning of collective input.", "translation": "Kollektiv-Impuls-FF: Impulsartige Heck-Vorkompensation fuer kollektive Eingaben. Falls zusaetzliche Heck-Vorkompensation zu Beginn einer kollektiven Eingabe erforderlich ist.", "needs_translation": "false"}, "cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "yaw_stop_gain": {"english": "Yaw stop gain", "translation": "Gier-Stopp-Verstaerkung", "needs_translation": "false"}, "precomp_cutoff": {"english": "Precomp Cutoff", "translation": "Vorkompensation Grenzwert", "needs_translation": "false"}, "collective_ff_gain": {"english": "Collective FF gain", "translation": "Kollektive FF-Verstaerkung", "needs_translation": "false"}, "name": {"english": "Tail Rotor", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "decay": {"english": "Decay", "translation": "Abfall", "needs_translation": "false"}}, "profile_pidcontroller": {"help_p4": {"english": "Error rotation: Allow errors to be shared between all axes.", "translation": "Fehlerrotation: Ermoeglicht das Teilen von Fehlern zwischen allen Achsen.", "needs_translation": "false"}, "ground_error_decay": {"english": "Ground Error Decay", "translation": "Fehlerrueckgang am Boden", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "inflight_error_decay": {"english": "Inflight Error Decay", "translation": "Fehlerrueckgang im Flug", "needs_translation": "false"}, "help_p2": {"english": "Error limit: Angle limit for I-term.", "translation": "Fehlergrenze: Winkelbegrenzung fuer den I-Term.", "needs_translation": "false"}, "error_limit": {"english": "Error limit", "translation": "Fehlergrenze", "needs_translation": "false"}, "help_p3": {"english": "Offset limit: Angle limit for High Speed Integral (O-term).", "translation": "Offset-Grenze: Winkelbegrenzung fuer High Speed Integral (O-Term).", "needs_translation": "false"}, "cutoff_point": {"english": "Cut-off point", "translation": "Grenzpunkt", "needs_translation": "false"}, "limit": {"english": "Limit", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "iterm_relax": {"english": "I-term relax", "translation": "I-Term-Entspannung", "needs_translation": "false"}, "hsi_offset_limit": {"english": "HSI Offset limit", "translation": "HSI-Offset-Grenze", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "name": {"english": "PID Controller", "translation": "PID-<PERSON><PERSON>", "needs_translation": "false"}, "error_rotation": {"english": "Error rotation", "translation": "Fehlerrotation", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}, "help_p5": {"english": "I-term relax: Limit accumulation of I-term during fast movements - helps reduce bounce back after fast stick movements. Generally needs to be lower for large helis and can be higher for small helis. Best to only reduce as much as is needed for your flying style.", "translation": "I-Term-Entspannung: Begrenzung der I-Term-Akkumulation bei schnellen Bewegungen – hilf<PERSON>, das Nachschwingen nach schnellen Steuerbewegungen zu reduzieren. Sollte fuer grosse Helis nied<PERSON>er und fuer kleine Helis hoeher sein. Am besten nur so weit reduzieren, wie es fuer den eigenen Flugstil erforderlich ist.", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Zeit", "needs_translation": "false"}, "help_p1": {"english": "Error decay ground: PID decay to help prevent heli from tipping over when on the ground.", "translation": "Fehlerrueckgang am Boden: PID-Abfall zur Vermeidung eines Kippens des Helis am Boden.", "needs_translation": "false"}}, "logs": {"help_logs_p2": {"english": "Note. To enable logging it is essential for you to have the following sensors enabled.", "translation": "Hinweis: Um das Logging zu aktivieren, muessen Sie die folgenden Sensoren aktiviert haben.", "needs_translation": "false"}, "name": {"english": "Logs", "translation": "Logs", "needs_translation": "false"}, "help_logs_p1": {"english": "Please select a log file from the list below.", "translation": "Bitte waehlen Sie eine Log-Datei aus der untenstehenden Liste aus.", "needs_translation": "false"}, "msg_no_logs_found": {"english": "NO LOG FILES FOUND", "translation": "KEINE LOG-DATEIEN GEFUNDEN", "needs_translation": "false"}, "help_logs_tool_p1": {"english": "Please use the slider to navigate the graph.", "translation": "Bitte verwenden Sie den Schieberegler, um im Diagramm zu navigieren.", "needs_translation": "false"}, "help_logs_p3": {"english": "- arm status, voltage, headspeed, current, esc temperature", "translation": "- Arm-Status, Spannung, Drehzahl, Strom, ESC-Temperatur", "needs_translation": "false"}}, "battery": {"calcfuel_local": {"english": "Calculate Fuel Using", "translation": "Calculate fuel using", "needs_translation": "true"}, "max_cell_voltage": {"english": "Max Cell Voltage", "translation": "Maximale Zellenspannung", "needs_translation": "false"}, "full_cell_voltage": {"english": "Full Cell Voltage", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "Battery", "translation": "<PERSON><PERSON>ie", "needs_translation": "false"}, "min_cell_voltage": {"english": "Min Cell Voltage", "translation": "Minimale Zellenspannung", "needs_translation": "false"}, "help_p1": {"english": "The battery settings are used to configure the flight controller to monitor the battery voltage and provide warnings when the voltage drops below a certain level.", "translation": "Die Batterieeinstellungen werden verwendet, um den Flugcontroller so zu konfigurieren, dass er die Batteriespannung ueberwacht und Warnungen ausgibt, wenn die Spannung unter ein bestimmtes Niveau faellt.", "needs_translation": "false"}, "battery_capacity": {"english": "Battery Capacity", "translation": "Batteriekapazitaet", "needs_translation": "false"}, "warn_cell_voltage": {"english": "Warn Cell Voltage", "translation": "<PERSON>nung Zelle<PERSON>pannung", "needs_translation": "false"}, "cell_count": {"english": "Cell Count", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "consumption_warning_percentage": {"english": "Consumption Warning %", "translation": "Verbrauchswarnung %", "needs_translation": "false"}, "timer": {"english": "Flight Time Alarm", "translation": "Flugzeitalarm", "needs_translation": "false"}, "voltage_multiplier": {"english": "Sag Compensation", "translation": "Spannungsausgleich", "needs_translation": "false"}, "kalman_multiplier": {"english": "Filter Compensation", "translation": "Filter compensation", "needs_translation": "true"}, "alert_type": {"english": "Rx Voltage Alert", "translation": "BEC or Rx Batt Voltage Alert", "needs_translation": "true"}, "bec_voltage_alert": {"english": "BEC Alert Value", "translation": "BEC Alert Value", "needs_translation": "true"}, "rx_voltage_alert": {"english": "RxBatt Alert Value", "translation": "RX Batt Alert Value", "needs_translation": "true"}}, "profile_mainrotor": {"gain": {"english": "<PERSON><PERSON>", "translation": "Verstaerkung", "needs_translation": "false"}, "help_p4": {"english": "Cross Coupling Freq. Limit: Frequency limit for the compensation, higher value will make the compensation action faster.", "translation": "Kreuzkopplungs-Frequenzgrenze: Frequenzgrenze fuer die Kompensation – ein hoeherer Wert fuehrt zu einer schnelleren Kompensationsreaktion.", "needs_translation": "false"}, "collective_pitch_comp_short": {"english": "Col. Pitch Compensation", "translation": "<PERSON><PERSON><PERSON>-Kompensation", "needs_translation": "false"}, "cyclic_cross_coupling": {"english": "Cyclic Cross coupling", "translation": "Zyklische Kreuzkopplung", "needs_translation": "false"}, "collective_pitch_comp": {"english": "Collective Pitch Compensation", "translation": "Kollektive Pitch-Kompensation", "needs_translation": "false"}, "name": {"english": "Main Rotor", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Grenzwert", "needs_translation": "false"}, "ratio": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Collective Pitch Compensation: Increasing will compensate for the pitching motion caused by tail drag when climbing.", "translation": "Kollektive Pitch-Kompensation: <PERSON><PERSON><PERSON><PERSON> diesen Wert, um das Kippmoment durch den Heckrotorzug beim Steigen auszugleichen.", "needs_translation": "false"}, "help_p2": {"english": "Cross Coupling Gain: Removes roll coupling when only elevator is applied.", "translation": "Kreuzkopplungs-Verstaerkung: <PERSON>tfernt Roll-Kopplung, wenn nur <PERSON> (Elevator) angewendet wird.", "needs_translation": "false"}, "help_p3": {"english": "Cross Coupling Ratio: Amount of compensation (pitch vs roll) to apply.", "translation": "Kreuzkopplungs-Verhaeltnis: Menge der angewandten Kompensation (<PERSON> vs. <PERSON>).", "needs_translation": "false"}}, "sbusout": {"title": {"english": "SBUS Output", "translation": "SBUS-Ausgang", "needs_translation": "false"}, "help_fields_source": {"english": "Source id for the mix, counting from 0-15.", "translation": "Quellen-<PERSON> fuer den Mix, <PERSON><PERSON><PERSON><PERSON> von 0-15.", "needs_translation": "false"}, "help_default_p4": {"english": "- For motors, use 0, 1000.", "translation": "- Fuer Motoren 0, 1000 verwenden.", "needs_translation": "false"}, "ch_prefix": {"english": "CH", "translation": "CH", "needs_translation": "false"}, "channel_prefix": {"english": "CHANNEL ", "translation": "KANAL ", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "Speichern", "needs_translation": "false"}, "name": {"english": "SBUS Out", "translation": "SBUS-Ausgang", "needs_translation": "false"}, "channel_page": {"english": "Sbus out / CH", "translation": "SBUS-Ausgang / CH", "needs_translation": "false"}, "receiver": {"english": "Receiver", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "servo": {"english": "Servo", "translation": "Servo", "needs_translation": "false"}, "type": {"english": "Type", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "<PERSON><PERSON><PERSON><PERSON> Daten...", "needs_translation": "false"}, "help_fields_max": {"english": "The maximum pwm value to send", "translation": "Der maximale PWM-Wert, der gesendet wird.", "needs_translation": "false"}, "motor": {"english": "Motor", "translation": "Motor", "needs_translation": "false"}, "help_default_p5": {"english": "- Or you can customize your own mapping.", "translation": "- Oder Si<PERSON> koennen Ihre eigene Zuordnung anpassen.", "needs_translation": "false"}, "help_default_p1": {"english": "Configure advanced mixing and channel mapping if you have SBUS Out enabled on a serial port.", "translation": "Erweiterte Misch- und Kanalzuordnung konfigurieren, wenn SBUS-Ausgang auf einem seriellen Port aktiviert ist.", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "save_prompt": {"english": "Save current page to flight controller?", "translation": "Aktuelle Seite zum Flugcontroller speichern?", "needs_translation": "false"}, "help_fields_min": {"english": "The minimum pwm value to send.", "translation": "Der minimale PWM-We<PERSON>, der gesendet wird.", "needs_translation": "false"}, "mixer": {"english": "Mixer", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "cancel": {"english": "CANCEL", "translation": "ABBRECHEN", "needs_translation": "false"}, "help_default_p2": {"english": "- For RX channels or servos (wideband), use 1000, 2000 or 500,1000 for narrow band servos.", "translation": "- Fuer RX-<PERSON><PERSON>le oder Servos (Breitband) 1000, 2000 oder 500,1000 fuer Schmalband-Servos verwenden.", "needs_translation": "false"}, "save_settings": {"english": "Save settings", "translation": "Einstellungen speichern", "needs_translation": "false"}, "min": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "help_default_p3": {"english": "- For mixer rules, use -1000, 1000.", "translation": "- <PERSON><PERSON> -1000, 1000 verwenden.", "needs_translation": "false"}, "source": {"english": "Source", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "profile_rescue": {"help_p4": {"english": "Hover: How much collective to maintain a steady hover.", "translation": "Schweben: Wie viel Kollektiv benoetigt wird, um ein stabiles Schweben zu halten.", "needs_translation": "false"}, "hover": {"english": "Hover", "translation": "Schweben", "needs_translation": "false"}, "collective": {"english": "Collective", "translation": "Kollektiv", "needs_translation": "false"}, "help_p2": {"english": "Pull-up: How much collective and for how long to arrest the fall.", "translation": "Abfangen: Wie viel Kollektiv und wie lange zum Stoppen des Sinkflugs verwendet wird.", "needs_translation": "false"}, "climb": {"english": "Climb", "translation": "Steigen", "needs_translation": "false"}, "mode_enable": {"english": "Rescue mode enable", "translation": "Rettungsmodus aktivieren", "needs_translation": "false"}, "help_p3": {"english": "Climb: How much collective to maintain a steady climb - and how long.", "translation": "Steigen: Wie viel Kollektiv benoetigt wird, um einen gleichmaessigen Steigflug zu halten – und wie lange.", "needs_translation": "false"}, "help_p1": {"english": "Flip to upright: Flip the heli upright when rescue is activated.", "translation": "Aufrichten: Rich<PERSON>t den Heli aus dem Rueckenflug auf, wenn der Rettungsmodus aktiviert wird.", "needs_translation": "false"}, "flip_upright": {"english": "Flip to upright", "translation": "Aufrichten", "needs_translation": "false"}, "flip": {"english": "Flip", "translation": "Flip", "needs_translation": "false"}, "level_gain": {"english": "Level", "translation": "Neigung", "needs_translation": "false"}, "name": {"english": "Rescue", "translation": "Rettung", "needs_translation": "false"}, "exit_time": {"english": "Exit time", "translation": "Abbruch", "needs_translation": "false"}, "help_p5": {"english": "Flip: How long to wait before aborting because the flip did not work.", "translation": "Flip: Wie lange gewartet wird, bevor der Rettungsmodus abgebrochen wird, falls der Flip (Aufrichten) nicht funktioniert.", "needs_translation": "false"}, "help_p6": {"english": "Gains: How hard to fight to keep heli level when engaging rescue mode.", "translation": "Verstaerkungen: Wie stark der Heli kaempft, um in der Waagerechten zu bleiben, wenn der Rettungsmodus aktiviert wird.", "needs_translation": "false"}, "fail_time": {"english": "Fail time", "translation": "Fehlzeit", "needs_translation": "false"}, "pull_up": {"english": "Pull-up", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "help_p7": {"english": "Rate and Accel: Max rotation and acceleration rates when leveling during rescue.", "translation": "Rate und Beschleunigung: Maximale Rotations- und Beschleunigungswerte beim Aufrichten waehrend der Rettung.", "needs_translation": "false"}, "gains": {"english": "<PERSON><PERSON><PERSON>", "translation": "Verstaerkungen", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Zeit", "needs_translation": "false"}, "accel": {"english": "Accel", "translation": "Beschleunigung", "needs_translation": "false"}}, "trim": {"disable_mixer_message": {"english": "Return control of the servos to the flight controller.", "translation": "Gibt die Steuerung der Servos an den Flugcontroller zurueck.", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Motor idle  %", "translation": "Heckmotor-Leerlauf %", "needs_translation": "false"}, "disable_mixer_override": {"english": "Disable mixer override", "translation": "Mischer-Ueberschreibung deaktivieren", "needs_translation": "false"}, "yaw_trim": {"english": "Yaw. trim %", "translation": "Gier-Trimmung %", "needs_translation": "false"}, "enable_mixer_message": {"english": "Set all servos to their configured center position. \r\n\r\nThis will result in all values on this page being saved when adjusting the servo trim.", "translation": "Alle Servos auf ihre konfigurierte Mittelposition setzen. \r\n\r\nAlle Werte auf dieser Seite werden gespeichert, wenn die Servo-Trimmung angepasst wird.", "needs_translation": "false"}, "mixer_override_disabling": {"english": "Disabling mixer override...", "translation": "Mischer-Ueberschreibung wird deaktiviert...", "needs_translation": "false"}, "roll_trim": {"english": "Roll trim %", "translation": "Roll-Trimmung %", "needs_translation": "false"}, "pitch_trim": {"english": "Pitch trim %", "translation": "Nick-Tri<PERSON>ung %", "needs_translation": "false"}, "name": {"english": "<PERSON><PERSON>", "translation": "Trimmung", "needs_translation": "false"}, "help_p2": {"english": "Motorised tail: If using a motorised tail, use this to set the minimum idle speed and zero yaw.", "translation": "Motorisiertes Heck: Falls ein motorisiertes Heck verwendet wird, koennen Sie hier die minimale Leerlauf<PERSON>hl und den Null-Gierpunkt einstellen.", "needs_translation": "false"}, "mixer_override": {"english": "Mixer Override", "translation": "Mischer-Ueberschreibung", "needs_translation": "false"}, "mixer_override_enabling": {"english": "Enabling mixer override...", "translation": "Mischer-Ueberschreibung wird aktiviert...", "needs_translation": "false"}, "enable_mixer_override": {"english": "Enable mixer override", "translation": "Mischer-Ueberschreibung aktivieren", "needs_translation": "false"}, "collective_trim": {"english": "Col. trim %", "translation": "Kollektiv-Trimmung %", "needs_translation": "false"}, "help_p1": {"english": "Link trims: Use to trim out small leveling issues in your swash plate. Typically only used if the swash links are non-adjustable.", "translation": "Trimmungen verknuepfen: Verwenden Si<PERSON> dies, um kleine Nivellierungsprobleme in Ihrer Taumelscheibe zu korrigieren. Normalerweise nur notwendig, wenn die Taumelscheibenanlenkungen nicht verstellbar sind.", "needs_translation": "false"}}, "governor": {"help_p1": {"english": "These parameters apply globally to the governor regardless of the profile in use.", "translation": "<PERSON>se Parameter gelten global fuer den Dr<PERSON>ger (Governor), unabhaengig vom verwendeten Profil.", "needs_translation": "false"}, "handover_throttle": {"english": "Handover throttle%", "translation": "Uebergabe-Gas%", "needs_translation": "false"}, "spoolup_min_throttle": {"english": "Spoolup min throttle%", "translation": "<PERSON><PERSON>-Gas%", "needs_translation": "false"}, "recovery_time": {"english": "Recovery time", "translation": "Erholungszeit", "needs_translation": "false"}, "mode": {"english": "Mode", "translation": "Modus", "needs_translation": "false"}, "help_p2": {"english": "Each parameter is simply a time value in seconds for each governor action.", "translation": "Jeder Parameter ist einfach ein Zeitwert in Sekunden fuer jede Regleraktion.", "needs_translation": "false"}, "tracking_time": {"english": "Tracking time", "translation": "Tracking-Zeit", "needs_translation": "false"}, "name": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "startup_time": {"english": "Startup time", "translation": "Startzeit", "needs_translation": "false"}, "spoolup_time": {"english": "Spoolup time", "translation": "Spoolup-Zeit", "needs_translation": "false"}}, "accelerometer": {"help_p1": {"english": "The accelerometer is used to measure the angle of the flight controller in relation to the horizon. This data is used to stabilize the aircraft and provide self-leveling functionality.", "translation": "Der Beschleunigungssensor wird verwendet, um den Winkel des Flugcontrollers in Bezug auf den Horizont zu messen. Diese Daten werden zur Stabilisierung des Fluggeraets und zur Bereitstellung der Selbstnivellierungsfunktion verwendet.", "needs_translation": "false"}, "name": {"english": "Accelerometer", "translation": "Beschleunigungssensor", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "<PERSON>", "needs_translation": "false"}, "msg_calibrate": {"english": "Calibrate the accelerometer?", "translation": "Beschleunigungssensor kalibrieren?", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}}, "rates": {"help_table_5_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Maximalrate: Maximale Rotationsgeschwindigkeit bei vollem Knueppelausschlag in Grad pro Sekunde.", "needs_translation": "false"}, "actual": {"english": "ACTUAL", "translation": "ACTUAL", "needs_translation": "false"}, "max_rate": {"english": "Max Rate", "translation": "Max. Rate", "needs_translation": "false"}, "help_table_4_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Verringert die Empfindlichkeit nahe der Knueppelmitte fuer feinere Steuerung.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "help_table_5_p1": {"english": "RC Rate: Use to reduce sensitivity around center stick. RC Rate set to one half of the Max Rate is linear. A lower number will reduce sensitivity around center stick. Higher than one half of the Max Rate will also increase the Max Rate.", "translation": "RC-Rate: Reduziert die Empfindlichkeit um die Knueppelmitte. Eine RC-Rate, die auf die Haelfte der Maximalrate eingestellt ist, ist linear. Ein niedrigerer Wert reduziert die Empfindlichkeit um die Knueppelmitte. Ein hoeherer Wert als die Haelfte der Maximalrate erhoeht auch die Maximalrate.", "needs_translation": "false"}, "help_table_4_p2": {"english": "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Maximalrate: Maximale Rotationsgeschwindigkeit bei vollem Knueppelausschlag in Grad pro Sekunde.", "needs_translation": "false"}, "center_sensitivity": {"english": "Cntr. Sens.", "translation": "Zentr. Empfindl.", "needs_translation": "false"}, "rc_curve": {"english": "RC Curve", "translation": "RC<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "none": {"english": "NONE", "translation": "KEINE", "needs_translation": "false"}, "betaflight": {"english": "BETAFLIGHT", "translation": "BETAFLIGHT", "needs_translation": "false"}, "kiss": {"english": "KISS", "translation": "KISS", "needs_translation": "false"}, "help_table_1_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Verringert die Empfindlichkeit nahe der Knueppelmitte fuer feinere Steuerung.", "needs_translation": "false"}, "help_table_3_p2": {"english": "Rate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "Rate: Er<PERSON><PERSON> die maximale Rotationsgeschwindigkeit und verringert die Empfindlichkeit in der Mitte des Knueppelwegs.", "needs_translation": "false"}, "help_table_2_p2": {"english": "Acro+: Increases the maximum rotation rate while reducing sensitivity around half stick.", "translation": "Acro+: <PERSON><PERSON><PERSON><PERSON> die maximale Rotationsgeschwindigkeit und verringert die Empfindlichkeit in der Mitte des Knueppelwegs.", "needs_translation": "false"}, "superrate": {"english": "SuperRate", "translation": "SuperRate", "needs_translation": "false"}, "help_table_2_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Verringert die Empfindlichkeit nahe der Knueppelmitte fuer feinere Steuerung.", "needs_translation": "false"}, "raceflight": {"english": "RACEFLIGHT", "translation": "RACEFLIGHT", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "collective": {"english": "Col", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "Rates", "needs_translation": "false"}, "help_table_5_p3": {"english": "Expo: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "Expo: Verringert die Empfindlichkeit nahe der Knueppelmitte fuer feinere Steuerung.", "needs_translation": "false"}, "help_table_3_p3": {"english": "RC Curve: Reduces sensitivity near the stick's center where fine controls are needed.", "translation": "RC-Kurve: Verringert die Empfindlichkeit nahe der Knueppelmitte fuer feinere Steuerung.", "needs_translation": "false"}, "expo": {"english": "Expo", "translation": "Expo", "needs_translation": "false"}, "help_table_1_p2": {"english": "SuperRate: Increases maximum rotation rate while reducing sensitivity around half stick.", "translation": "SuperRate: <PERSON><PERSON><PERSON><PERSON> die maximale Rotationsgeschwindigkeit und verringert gleichzeitig die Empfindlichkeit in der Mitte des Knueppelwegs.", "needs_translation": "false"}, "help_default_p2": {"english": "We will use the sub keys below.", "translation": "Wir verwenden die folgenden Unteroptionen.", "needs_translation": "false"}, "help_default_p1": {"english": "Default: We keep this to make button appear for rates.", "translation": "Standard: <PERSON><PERSON> bleibt erhalten, damit der Button fuer die Rates erscheint.", "needs_translation": "false"}, "quick": {"english": "QUICK", "translation": "SCHNELL", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "<PERSON>", "needs_translation": "false"}, "acroplus": {"english": "Acro+", "translation": "Acro+", "needs_translation": "false"}, "help_table_1_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "RC-Rate: Maximale Rotationsgeschwindigkeit bei vollem Knueppelausschlag.", "needs_translation": "false"}, "rc_rate": {"english": "RC Rate", "translation": "RC-Rate", "needs_translation": "false"}, "help_table_2_p1": {"english": "Rate: Maximum rotation rate at full stick deflection in degrees per second.", "translation": "Rate: Maximale Rotationsgeschwindigkeit bei vollem Knueppelausschlag in Grad pro Sekunde.", "needs_translation": "false"}, "help_table_4_p1": {"english": "Center Sensitivity: Use to reduce sensitivity around center stick. Set Center Sensitivity to the same as Max Rate for a linear response. A lower number than Max Rate will reduce sensitivity around center stick. Note that higher than Max Rate will increase the Max Rate - not recommended as it causes issues in the Blackbox log.", "translation": "Zentrale Empfindlichkeit: Reduziert die Empfindlichkeit um die Knueppelmitte. Wenn die zentrale Empfindlichkeit auf denselben Wert wie die Maximalrate gesetzt wird, ist die Reaktion linear. Ein niedrigerer Wert als die Maximalrate reduziert die Empfindlichkeit um die Knueppelmitte. Ein hoeherer Wert als die Maximalrate erhoeht die Maximalrate – nicht empfohlen, da dies zu Problemen in den Blackbox-Logs fuehrt.", "needs_translation": "false"}, "help_table_0_p1": {"english": "All values are set to zero because no RATE TABLE is in use.", "translation": "Alle Werte sind auf null gesetzt, da keine RATE-TABELLE verwendet wird.", "needs_translation": "false"}, "help_table_3_p1": {"english": "RC Rate: Maximum rotation rate at full stick deflection.", "translation": "RC-Rate: Maximale Rotationsgeschwindigkeit bei vollem Knueppelausschlag.", "needs_translation": "false"}}, "mixer": {"help_p1": {"english": "Adust swash plate geometry, phase angles, and limits.", "translation": "Passen Sie die Taumelscheibengeometrie, Phasenwinkel und Begrenzungen an.", "needs_translation": "false"}, "collective_tilt_correction_pos": {"english": "Positive", "translation": "Positiv", "needs_translation": "false"}, "geo_correction": {"english": "Geo Correction", "translation": "Geo-Korrektur", "needs_translation": "false"}, "swash_tta_precomp": {"english": "TTA Precomp", "translation": "TTA-Vorkompensation", "needs_translation": "false"}, "name": {"english": "Mixer", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "collective_tilt_correction_neg": {"english": "Negative", "translation": "Negativ", "needs_translation": "false"}, "tail_motor_idle": {"english": "Tail Idle Thr%", "translation": "Heckleerlauf-Gas%", "needs_translation": "false"}, "swash_phase": {"english": "Phase Angle", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "collective_tilt_correction": {"english": "Collective Tilt Correction", "translation": "Kollektive Neigungskorrektur", "needs_translation": "false"}, "swash_pitch_limit": {"english": "Total Pitch Limit", "translation": "Gesamter Pitch-Limit", "needs_translation": "false"}}, "about": {"help_p1": {"english": "This page provides some useful information that you may be asked for when requesting support.", "translation": "Diese Seite bietet einige nuetzliche Informationen, die Sie moeglicherweise angeben muessen, wenn <PERSON><PERSON> Unterstuetzung anfordern.", "needs_translation": "false"}, "msgbox_credits": {"english": "Credits", "translation": "Danksagungen", "needs_translation": "false"}, "ethos_version": {"english": "Ethos Version", "translation": "Ethos-Version", "needs_translation": "false"}, "rf_version": {"english": "Rotorflight Version", "translation": "Rotorflight-Version", "needs_translation": "false"}, "fc_version": {"english": "FC Version", "translation": "FC-Version", "needs_translation": "false"}, "name": {"english": "About", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "supported_versions": {"english": "Supported MSP Versions", "translation": "MSP-Versionen", "needs_translation": "false"}, "license": {"english": "You may copy, distribute, and modify the software as long as you track changes/dates in source files. Any modifications to or software including (via compiler) GPL-licensed code must also be made available under the GPL along with build & install instructions.", "translation": "Sie duerfen die Software kopieren, verbreiten und modifizieren, solange Sie Aenderungen und Daten in den Quelldateien nachverfolgen. Jegliche Aenderungen oder Software, die GPL-lizenzierte Codebestandteile enthaelt (ueber den Compiler), muessen ebenfalls unter der GPL verfuegbar gemacht werden, zusammen mit Anleitungen zur Erstellung und Installation.", "needs_translation": "false"}, "simulation": {"english": "Simulation", "translation": "Simulation", "needs_translation": "false"}, "help_p2": {"english": "For support, please first read the help pages on www.rotorflight.org", "translation": "Fuer Unterstuetzung lesen Si<PERSON> bitte zu<PERSON>t die Hilfeseiten auf www.rotorflight.org", "needs_translation": "false"}, "opener": {"english": "Rotorflight is an open source project. Contribution from other like minded people, keen to assist in making this software even better, is welcomed and encouraged. You do not have to be a hardcore programmer to help.", "translation": "Rotorflight ist ein Open-Source-Projekt. <PERSON>it<PERSON><PERSON> von Gleichgesinnten, die daran interessiert sind, diese Software weiter zu verbessern, sind willkommen und werden ermutigt. Man muss kein Hardcore-<PERSON><PERSON><PERSON> sein, um zu helfen.", "needs_translation": "false"}, "version": {"english": "Version", "translation": "Version", "needs_translation": "false"}, "msp_version": {"english": "MSP Version", "translation": "MSP-Version", "needs_translation": "false"}, "credits": {"english": "Notable contributors to both the Rotorflight firmware and this software are: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... and many more who have spent hours testing and providing feedback!", "translation": "<PERSON><PERSON><PERSON><PERSON> Mitwirkende, sowohl an der Rotorflight-Firmware als auch an dieser Software sind: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>... und viele weitere, die Stunden mit Tests und Feedback verbracht haben!", "needs_translation": "false"}, "msp_transport": {"english": "MSP Transport", "translation": "MSP-Transport", "needs_translation": "false"}}, "rates_advanced": {"dyn_ceiling_gain": {"english": "Dynamic ceiling gain", "translation": "DynMaxVerst", "needs_translation": "false"}, "acc_limit": {"english": "Accelerometer Limit", "translation": "BeschlGrenze", "needs_translation": "false"}, "roll": {"english": "Roll", "translation": "Roll", "needs_translation": "false"}, "yaw_dynamics": {"english": "Yaw dynamics", "translation": "Gier-Dynamik", "needs_translation": "false"}, "pitch": {"english": "Pitch", "translation": "<PERSON>", "needs_translation": "false"}, "col": {"english": "Col", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "setpoint_boost_cutoff": {"english": "Setpoint boost cutoff", "translation": "SollwertBoostGrenze", "needs_translation": "false"}, "yaw_dynamic_deadband_gain": {"english": "D. <PERSON>", "translation": "To<PERSON>band", "needs_translation": "false"}, "rates_type": {"english": "Rates Type", "translation": "Raten-Typ", "needs_translation": "false"}, "setpoint_boost_gain": {"english": "Setpoint boost gain", "translation": "SollwertBoostVerst", "needs_translation": "false"}, "msg_reset_to_defaults": {"english": "Rate type changed. Values will be reset to defaults.", "translation": "Rate-Typ geaendert. Werte werden auf Standardwerte zurueckgesetzt.", "needs_translation": "false"}, "yaw_dynamic_ceiling_gain": {"english": "Ceiling", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "yaw_boost": {"english": "Yaw boost", "translation": "Gier-Verstärkung", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "Verst.", "needs_translation": "false"}, "rate_table": {"english": "Rate Table", "translation": "Rate Tabelle", "needs_translation": "false"}, "dynamics": {"english": "Dynamics", "translation": "Dynamik", "needs_translation": "false"}, "yaw": {"english": "Yaw", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "yaw_dynamic_deadband_filter": {"english": "Filter", "translation": "Filter", "needs_translation": "false"}, "name": {"english": "Rates", "translation": "Rates", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "Grenz.", "needs_translation": "false"}, "help_rate_table": {"english": "Please select the rate you would like to use. Saving will apply the choice to the active profile.", "translation": "Bitte waehlen Sie die Rates aus, den Si<PERSON> verwenden moechten. Durch das Speichern wird die Auswahl auf das aktive Profil angewendet.", "needs_translation": "false"}, "help_p1": {"english": "Rates type: Choose the rate type you prefer flying with. Raceflight and Actual are the most straightforward.", "translation": "Raten-Typ: <PERSON><PERSON><PERSON><PERSON> Sie den Rate-Typ aus, mit dem Si<PERSON> fliegen moechten. Raceflight und Actual sind die einfachsten.", "needs_translation": "false"}, "pitch_boost": {"english": "Pitch boost", "translation": "Nick-Verstärkung", "needs_translation": "false"}, "help_p2": {"english": "Dynamics: Applied regardless of rates type. Typically left on defaults but can be adjusted to smooth heli movements, like with scale helis.", "translation": "Dynamik: W<PERSON>d unabhaengig vom Rate-Typ angewendet. Anpassen, um Heli-Bewegungen weicher zu machen, z. B. fuer Scale-Helis.", "needs_translation": "false"}, "accel_limit": {"english": "Accel", "translation": "Beschl.", "needs_translation": "false"}, "dyn_deadband_filter": {"english": "Dynamic deadband filter", "translation": "DynTotzeitFilter", "needs_translation": "false"}, "roll_boost": {"english": "Roll boost", "translation": "Roll-Verstärkung", "needs_translation": "false"}, "dyn_deadband_gain": {"english": "Dynamic deadband gain", "translation": "DynTotzeitVerst", "needs_translation": "false"}, "collective_dynamics": {"english": "Collective dynamics", "translation": "Kollektiv-Dynamik", "needs_translation": "false"}, "roll_dynamics": {"english": "Roll dynamics", "translation": "Roll-Dynamik", "needs_translation": "false"}, "collective_boost": {"english": "Collective boost", "translation": "Kollektiv-Verstärkung", "needs_translation": "false"}, "pitch_dynamics": {"english": "Pitch dynamics", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "response_time": {"english": "Response Time", "translation": "Reaktionszeit", "needs_translation": "false"}}, "servos": {"tbl_yes": {"english": "YES", "translation": "JA", "needs_translation": "false"}, "enable_servo_override": {"english": "Enable servo override", "translation": "Servo-Ueberschreibung aktivieren", "needs_translation": "false"}, "disabling_servo_override": {"english": "Disabling servo override...", "translation": "Deaktiviere Servo-Ueberschreibung...", "needs_translation": "false"}, "help_tool_p3": {"english": "Minimum/Maximum: Adjust the end points of the selected servo.", "translation": "Minimum/Maximum: Passen Sie die Endpunkte des ausgewaehlten Servos an.", "needs_translation": "false"}, "tail": {"english": "TAIL", "translation": "HECK", "needs_translation": "false"}, "scale_negative": {"english": "Scale Negative", "translation": "Negatives <PERSON>", "needs_translation": "false"}, "help_tool_p1": {"english": "Override: [*] Enable override to allow real-time updates of servo center point.", "translation": "Ueberschreiben: [*] Aktivieren Sie die Ueberschreibung, um Echtzeitaktualisierungen der Servo-Mittelposition zu ermoeglichen.", "needs_translation": "false"}, "tbl_no": {"english": "NO", "translation": "NEIN", "needs_translation": "false"}, "maximum": {"english": "Maximum", "translation": "Maximum", "needs_translation": "false"}, "help_tool_p6": {"english": "Speed: The speed the servo moves. Generally only used for the cyclic servos to help the swash move evenly. Optional - leave all at 0 if unsure.", "translation": "Geschwindigkeit: Die Geschwindigkeit, mit der sich das Servo bewegt. Wird normalerweise nur fuer zyklische Servos verwendet, um eine gleichmaessige Bewegung der Taumelscheibe zu gewaehrleisten. Optional – alle Werte auf 0 lassen, wenn Si<PERSON> unsicher sind.", "needs_translation": "false"}, "help_fields_rate": {"english": "Servo PWM rate.", "translation": "Servo-PWM-Frequenz.", "needs_translation": "false"}, "cyc_pitch": {"english": "CYC.PITCH", "translation": "ZYK. PITCH", "needs_translation": "false"}, "center": {"english": "Center", "translation": "Zentrum", "needs_translation": "false"}, "minimum": {"english": "Minimum", "translation": "Minimum", "needs_translation": "false"}, "speed": {"english": "Speed", "translation": "Geschwindigkeit", "needs_translation": "false"}, "help_fields_speed": {"english": "Servo motion speed in milliseconds.", "translation": "Servo-Geschwindigkeit in Millisekunden.", "needs_translation": "false"}, "disable_servo_override": {"english": "Disable servo override", "translation": "Servo-Ueberschreibung deaktivieren", "needs_translation": "false"}, "help_fields_scale_pos": {"english": "Servo positive scaling.", "translation": "Positives Servo-Scaling.", "needs_translation": "false"}, "saving_data": {"english": "Saving data...", "translation": "<PERSON><PERSON><PERSON><PERSON> Daten...", "needs_translation": "false"}, "cyc_left": {"english": "CYC.LEFT", "translation": "ZYK. LINKS", "needs_translation": "false"}, "saving": {"english": "Saving", "translation": "Speichern", "needs_translation": "false"}, "name": {"english": "Servos", "translation": "Servos", "needs_translation": "false"}, "help_tool_p5": {"english": "Rate: The frequency the servo runs best at - check with manufacturer.", "translation": "Rate: <PERSON> Frequenz, mit der das Servo am besten arbeitet – prue<PERSON> Si<PERSON> dies beim Hersteller.", "needs_translation": "false"}, "help_tool_p2": {"english": "Center: Adjust the center position of the servo.", "translation": "Zentrum: Passen Sie die Mittelposition des Servos an.", "needs_translation": "false"}, "enabling_servo_override": {"english": "Enabling servo override...", "translation": "Aktiviere Servo-Ueberschreibung...", "needs_translation": "false"}, "servo_prefix": {"english": "SERVO ", "translation": "SERVO ", "needs_translation": "false"}, "reverse": {"english": "Reverse", "translation": "Umkehren", "needs_translation": "false"}, "enable_servo_override_msg": {"english": "Servo override allows you to 'trim' your servo center point in real time.", "translation": "Die Servo-Ueberschreibung ermoeglicht es Ihnen, den Servo-Mittelpunkt in Echtzeit zu 'trimmen'.", "needs_translation": "false"}, "cyc_right": {"english": "CYC.RIGHT", "translation": "ZYK. RECHTS", "needs_translation": "false"}, "help_default_p2": {"english": "Primary flight controls that use the rotorflight mixer will display in the section called 'mixer'.", "translation": "Primaere Flugsteuerungen, die den Rotorflight-Mischer verwenden, werden im Abschnitt 'Mischer' angezeigt.", "needs_translation": "false"}, "scale_positive": {"english": "Scale Positive", "translation": "Po<PERSON><PERSON>", "needs_translation": "false"}, "help_default_p1": {"english": "Please select the servo you would like to configure from the list below.", "translation": "<PERSON>te waehlen Sie das Servo aus, das Sie konfigurieren moechten, aus der untenstehenden Liste.", "needs_translation": "false"}, "servo_override": {"english": "Servo Override", "translation": "Servo-Ueberschreibung", "needs_translation": "false"}, "disable_servo_override_msg": {"english": "Return control of the servos to the flight controller.", "translation": "Gibt die Steuerung der Servos an den Flugcontroller zurueck.", "needs_translation": "false"}, "help_fields_min": {"english": "Servo negative travel limit.", "translation": "Negativer Reiseweg des Servos.", "needs_translation": "false"}, "help_default_p3": {"english": "Any other servos that are not controlled by the primary flight mixer will be displayed in the section called 'Other servos'.", "translation": "<PERSON>e andere<PERSON>, die nicht vom primaeren Flugmischer gesteuert werden, werden im Abschnitt 'Andere Servos' angezeigt.", "needs_translation": "false"}, "help_fields_mid": {"english": "Servo center position pulse width.", "translation": "Servo-Mittelposition als Pulsbreite.", "needs_translation": "false"}, "help_fields_scale_neg": {"english": "Servo negative scaling.", "translation": "Negatives Servo-Scaling.", "needs_translation": "false"}, "rate": {"english": "Rate", "translation": "Rate", "needs_translation": "false"}, "help_tool_p4": {"english": "Scale: Adjust the amount the servo moves for a given input.", "translation": "Scaling: Passen Sie den Bewegungsbereich des Servos fuer eine bestimmte Eingabe an.", "needs_translation": "false"}, "help_fields_flags": {"english": "0 = <PERSON><PERSON><PERSON>, 1=Reverse, 2 = Geo Correction, 3 = Reverse + Geo Correction", "translation": "0 = Standard, 1 = Umkehren, 2 = Geo-Korrektur, 3 = Umkehren + Geo-Korrektur", "needs_translation": "false"}, "geometry": {"english": "Geometry", "translation": "Geometrie", "needs_translation": "false"}, "help_fields_max": {"english": "Servo positive travel limit.", "translation": "Positiver Reiseweg des Servos.", "needs_translation": "false"}}, "profile_autolevel": {"acro_trainer": {"english": "Acro trainer", "translation": "Acro-Trainer", "needs_translation": "false"}, "angle_mode": {"english": "Angle mode", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "name": {"english": "Autolevel", "translation": "Autonivellierung", "needs_translation": "false"}, "help_p1": {"english": "Acro Trainer: How aggressively the heli tilts back to level when flying in Acro Trainer Mode.", "translation": "Acro-Trainer: Rueckstellkraft des Helikopters zurueck zur Waagerechten im Acro-Trainer-Modus .", "needs_translation": "false"}, "horizon_mode": {"english": "Horizon mode", "translation": "Horizontmodus", "needs_translation": "false"}, "gain": {"english": "<PERSON><PERSON>", "translation": "Verstaerkung", "needs_translation": "false"}, "help_p2": {"english": "Angle Mode: How aggressively the heli tilts back to level when flying in Angle Mode.", "translation": "Winkelmodus: Rueckstellkraft des Helikopters zurueck zur Waagerechten im Winkelmodus.", "needs_translation": "false"}, "help_p3": {"english": "Horizon Mode: How aggressively the heli tilts back to level when flying in Horizon Mode.", "translation": "Horizontmodus: Rueckstellkraft des Helikopters zurueck zur Waagerechten im Horizontmodus.", "needs_translation": "false"}}, "filters": {"filter_type": {"english": "Filter type", "translation": "Filtertyp", "needs_translation": "false"}, "help_p4": {"english": "Dynamic Notch Filters: Automatically creates notch filters within the min and max frequency range.", "translation": "Dynamische Notch-Filter: Erstellt automatisch Notch-Filter innerhalb des minimalen und maximalen Frequenzbereichs.", "needs_translation": "false"}, "notch_c": {"english": "Notch Count", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "rpm_preset": {"english": "Type", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "lowpass_1": {"english": "Lowpass 1", "translation": "TP 1", "needs_translation": "false"}, "rpm_min_hz": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>.", "needs_translation": "false"}, "help_p2": {"english": "Gyro lowpass: Lowpass filters for the gyro signal. Typically left at default.", "translation": "Gyro-Tiefpass: Tiefpassfilter fuer das Gyro-Signal. In der Regel auf Standard belassen.", "needs_translation": "false"}, "cutoff": {"english": "Cutoff", "translation": "G-freq.", "needs_translation": "false"}, "notch_1": {"english": "Notch 1", "translation": "Notch 1", "needs_translation": "false"}, "max_cutoff": {"english": "Max cutoff", "translation": "<PERSON><PERSON>.", "needs_translation": "false"}, "help_p3": {"english": "Gyro notch filters: Use for filtering specific frequency ranges. Typically not needed in most helis.", "translation": "Gyro-Notch-Filter: W<PERSON><PERSON> ver<PERSON>, um bestimmte Frequenzbereiche zu filtern. In den meisten Helis normalerweise nicht erforderlich.", "needs_translation": "false"}, "lowpass_2": {"english": "Lowpass 2", "translation": "TP 2", "needs_translation": "false"}, "rpm_filter": {"english": "RPM filter", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "help_p1": {"english": "Typically you would not edit this page without checking your Blackbox logs!", "translation": "Normalerweise sollten Sie diese Seite nicht bearbeiten, ohne Ihre Blackbox-Logs zu ueberpruefen!", "needs_translation": "false"}, "dyn_notch": {"english": "Dynamic Filters", "translation": "Dynamische Filter", "needs_translation": "false"}, "notch_q": {"english": "Notch Q", "translation": "Notch Q", "needs_translation": "false"}, "lowpass_1_dyn": {"english": "Lowpass 1 dyn.", "translation": "TP 1 dyn.", "needs_translation": "false"}, "notch_min_hz": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "notch_max_hz": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "notch_2": {"english": "Notch 2", "translation": "Notch 2", "needs_translation": "false"}, "name": {"english": "Filters", "translation": "Filter", "needs_translation": "false"}, "min_cutoff": {"english": "Min cutoff", "translation": "<PERSON><PERSON>.", "needs_translation": "false"}, "center": {"english": "Center", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "status": {"arming_disable_flag_3": {"english": "Bad RX Recovery", "translation": "RX-Wiederherstellung fehlgeschlagen", "needs_translation": "false"}, "arming_disable_flag_20": {"english": "RPM Filter", "translation": "RPM-Filter", "needs_translation": "false"}, "arming_disable_flag_11": {"english": "Load", "translation": "Auslastung", "needs_translation": "false"}, "arming_disable_flag_22": {"english": "DSHOT Bitbang", "translation": "DSHOT Bitbang", "needs_translation": "false"}, "dataflash_free_space": {"english": "Dataflash Free Space", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_25": {"english": "Arm Switch", "translation": "Arming <PERSON>er", "needs_translation": "false"}, "erasing": {"english": "Erasing", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_9": {"english": "Boot <PERSON>", "translation": "Boot-Grace-Zeit", "needs_translation": "false"}, "megabyte": {"english": "MB", "translation": "MB", "needs_translation": "false"}, "arming_disable_flag_17": {"english": "Paralyze", "translation": "Paralyse", "needs_translation": "false"}, "arming_disable_flag_5": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "arming_disable_flag_8": {"english": "<PERSON><PERSON>", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_1": {"english": "Fail Safe", "translation": "Fail Safe", "needs_translation": "false"}, "cpu_load": {"english": "CPU Load", "translation": "CPU-Auslastung", "needs_translation": "false"}, "arming_disable_flag_15": {"english": "BST", "translation": "BST", "needs_translation": "false"}, "arming_disable_flag_12": {"english": "Calibrating", "translation": "Kalibrierung", "needs_translation": "false"}, "arming_disable_flag_19": {"english": "Resc", "translation": "Rettung", "needs_translation": "false"}, "arming_disable_flag_4": {"english": "Box Fail Safe", "translation": "Box Fail Safe", "needs_translation": "false"}, "arming_disable_flag_24": {"english": "Motor Protocol", "translation": "Motor-Protokoll", "needs_translation": "false"}, "real_time_load": {"english": "Real-time Load", "translation": "Echtzeitauslastung", "needs_translation": "false"}, "help_p2": {"english": "To erase the dataflash for more log file storage, press the button on the menu denoted by a '*'.", "translation": "Um den Dataflash fuer mehr Speicherplatz fuer Log-<PERSON><PERSON> zu loeschen, druecken Sie die mit '*' markierte Schaltflaeche im Menue.", "needs_translation": "false"}, "arming_disable_flag_2": {"english": "RX Fail Safe", "translation": "RX Fail Safe", "needs_translation": "false"}, "ok": {"english": "OK", "translation": "OK", "needs_translation": "false"}, "arming_disable_flag_0": {"english": "No Gyro", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_18": {"english": "GPS", "translation": "GPS", "needs_translation": "false"}, "help_p1": {"english": "Use this page to view your current flight controller status. This can be useful when determining why your heli will not arm.", "translation": "<PERSON><PERSON> dieser Seite koennen Sie den aktuellen Status Ihres Flugcontrollers anzeigen. Dies kann hilfreich sein, um herauszu<PERSON>den, warum Ihr He<PERSON> nicht schaerft.", "needs_translation": "false"}, "arming_flags": {"english": "Arming Flags", "translation": "Arming-Flags", "needs_translation": "false"}, "unsupported": {"english": "Unsupported", "translation": "Nicht unterstuetzt", "needs_translation": "false"}, "erase_prompt": {"english": "Would you like to erase the dataflash?", "translation": "Moechten Sie den Datenspeicher loeschen?", "needs_translation": "false"}, "erase": {"english": "Erase", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "arming_disable_flag_10": {"english": "No Pre Arm", "translation": "<PERSON>in Pre-Arm", "needs_translation": "false"}, "arming_disable_flag_21": {"english": "Reboot Required", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "name": {"english": "Status", "translation": "Status", "needs_translation": "false"}, "arming_disable_flag_13": {"english": "CLI", "translation": "CLI", "needs_translation": "false"}, "arming_disable_flag_14": {"english": "CMS Menu", "translation": "CMS-Menue", "needs_translation": "false"}, "arming_disable_flag_16": {"english": "MSP", "translation": "MSP", "needs_translation": "false"}, "arming_disable_flag_7": {"english": "<PERSON>hrottle", "translation": "Gas", "needs_translation": "false"}, "erasing_dataflash": {"english": "Erasing dataflash...", "translation": "Datenspeicher wird geloescht...", "needs_translation": "false"}, "arming_disable_flag_23": {"english": "Acc Calibration", "translation": "ACC-Kalibrierung", "needs_translation": "false"}}, "profile_pidbandwidth": {"help_p1": {"english": "PID Bandwidth: Overall bandwidth in HZ used by the PID loop.", "translation": "PID-Bandbreite: Gesamtbandbreite in Hz, die von der PID-Regler verwendet wird.", "needs_translation": "false"}, "pitch": {"english": "P", "translation": "P", "needs_translation": "false"}, "yaw": {"english": "Y", "translation": "Y", "needs_translation": "false"}, "name": {"english": "PID Bandwidth", "translation": "PID-Bandbreite", "needs_translation": "false"}, "bterm_cutoff": {"english": "B-term cut-off", "translation": "B-Term Grenzfrequenz", "needs_translation": "false"}, "help_p3": {"english": "B-term cutoff: B-term cutoff frequency in HZ.", "translation": "B-Term Grenzfrequenz: Grenzfrequenz des B-Terms in Hz.", "needs_translation": "false"}, "dterm_cutoff": {"english": "D-term cut-off", "translation": "D-Term Grenzfrequenz", "needs_translation": "false"}, "help_p2": {"english": "D-term cutoff: D-term cutoff frequency in HZ.", "translation": "D-Term Grenzfrequenz: Grenzfrequenz des D-Terms in Hz.", "needs_translation": "false"}, "roll": {"english": "R", "translation": "R", "needs_translation": "false"}}}, "navigation_save": {"english": "SAVE", "translation": "SPEICHERN", "needs_translation": "false"}, "menu_section_flight_tuning": {"english": "Flight Tuning", "translation": "Flugabstimmung", "needs_translation": "false"}, "error_timed_out": {"english": "Error: timed out", "translation": "Fehler: Zeitueberschreitung", "needs_translation": "false"}, "check_rf_module_on": {"english": "Please check your rf module is turned on.", "translation": "Bitte ueberpruefen Sie, ob Ihr RF-Modul eingeschaltet ist.", "needs_translation": "false"}, "msg_saving": {"english": "Saving...", "translation": "Speichern...", "needs_translation": "false"}, "msg_save_not_commited": {"english": "Save not committed to EEPROM", "translation": "Speicherung nicht im EEPROM uebernommen", "needs_translation": "false"}, "menu_section_advanced": {"english": "Advanced", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "msg_loading_from_fbl": {"english": "Loading data from flight controller...", "translation": "Daten vom Flugcontroller werden geladen...", "needs_translation": "false"}, "msg_reload_settings": {"english": "Reload data from flight controller?", "translation": "Daten vom Flugcontroller neu laden?", "needs_translation": "false"}, "menu_section_tools": {"english": "Tools", "translation": "Werkzeuge", "needs_translation": "false"}, "msg_connecting": {"english": "Connecting", "translation": "Verbindung wird hergestellt", "needs_translation": "false"}, "msg_save_current_page": {"english": "Save current page to flight controller?", "translation": "Aktuelle Seite auf dem Flugcontroller speichern?", "needs_translation": "false"}, "btn_ok_long": {"english": "                OK                ", "translation": "                OK                ", "needs_translation": "false"}, "check_discovered_sensors": {"english": "Please check you have discovered all sensors.", "translation": "<PERSON>te stellen <PERSON> sicher, dass alle Sensoren erkannt wurden.", "needs_translation": "false"}, "msg_loading": {"english": "Loading...", "translation": "Laedt...", "needs_translation": "false"}, "check_heli_on": {"english": "Please check your heli is powered up and radio connected.", "translation": "Bitte ueberpruefen Si<PERSON>, ob Ihr Heli eingeschaltet und der Empfaenger verbunden ist.", "needs_translation": "false"}, "check_bg_task": {"english": "Please enable the background task.", "translation": "Bitte aktivieren Sie die Background-Task.", "needs_translation": "false"}, "navigation_tools": {"english": "*", "translation": "*", "needs_translation": "false"}, "check_supported_version": {"english": "This version of the Lua script \ncan't be used with the selected model", "translation": "Diese Version des Lua-Skripts\nkann nicht mit dem ausgewaehlten Modell verwendet werden.", "needs_translation": "false"}}