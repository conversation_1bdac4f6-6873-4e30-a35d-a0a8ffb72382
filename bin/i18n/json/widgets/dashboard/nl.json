{"theme_load_error": {"english": "Your theme did not load correctly. Falling back to default theme.", "translation": "Je Thema werd niet juist geladen. Standaard thema wordt gebruikt.", "needs_translation": "false"}, "validate_sensors": {"english": "PLEASE CHECK SENSORS", "translation": "CONTROLEER SENSORS", "needs_translation": "false"}, "unsupported_resolution": {"english": "TO SMALL", "translation": "TE KLEIN", "needs_translation": "false"}, "loading": {"english": "ROTORFLIGHT", "translation": "ROTORFLIGHT", "needs_translation": "false"}, "waiting_for_connection": {"english": "CONNECTING", "translation": "VERBINDEN", "needs_translation": "false"}, "check_bg_task": {"english": "BG TASK", "translation": "BG TASK", "needs_translation": "false"}, "check_rf_module_on": {"english": "RF MODULE", "translation": "RF MODULE", "needs_translation": "false"}, "check_discovered_sensors": {"english": "SENSORS", "translation": "SENSOREN", "needs_translation": "false"}, "no_link": {"english": "NO LINK", "translation": "Geen link", "needs_translation": "false"}, "reset_flight": {"english": "Reset flight", "translation": "Reset flight", "needs_translation": "false"}, "reset_flight_ask_title": {"english": "Reset flight", "translation": "Reset flight", "needs_translation": "false"}, "reset_flight_ask_text": {"english": "Are you sure you want to reset the flight?", "translation": "Weet u zeker dat je flight wilt resetten?", "needs_translation": "false"}, "voltage": {"english": "Voltage", "translation": "Voltage", "needs_translation": "false"}, "fuel": {"english": "Fuel", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "headspeed": {"english": "Headspeed", "translation": "Rotortoerental", "needs_translation": "false"}, "max": {"english": "Max", "translation": "Max", "needs_translation": "false"}, "min": {"english": "Min", "translation": "Min", "needs_translation": "false"}, "bec_voltage": {"english": "BEC Voltage", "translation": "BEC Voltage", "needs_translation": "false"}, "esc_temp": {"english": "ESC Temp", "translation": "ESC Temp", "needs_translation": "false"}, "flight_duration": {"english": "Flight Duration", "translation": "Flight Duur", "needs_translation": "false"}, "total_flight_duration": {"english": "Total Model Flight Duration", "translation": "Totaal model flight duur", "needs_translation": "false"}, "rpm_min": {"english": "RPM Min", "translation": "RPM Min", "needs_translation": "false"}, "rpm_max": {"english": "RPM Max", "translation": "RPM Max", "needs_translation": "false"}, "throttle_max": {"english": "Throttle Max", "translation": "Throttle Max", "needs_translation": "false"}, "current_max": {"english": "Current Max", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "esc_max_temp": {"english": "ESC Temp Max", "translation": "ESC Temp Max", "needs_translation": "false"}, "watts_max": {"english": "<PERSON>", "translation": "<PERSON>", "needs_translation": "false"}, "consumed_mah": {"english": "Consumed mAh", "translation": "Verbruikt mAh", "needs_translation": "false"}, "fuel_remaining": {"english": "Fuel Remaining", "translation": "<PERSON><PERSON><PERSON> over", "needs_translation": "false"}, "min_volts_cell": {"english": "Min Volts per cell", "translation": "Min volts per cel", "needs_translation": "false"}, "link_min": {"english": "<PERSON>", "translation": "<PERSON> min", "needs_translation": "false"}, "governor": {"english": "Governor", "translation": "Governor", "needs_translation": "false"}, "profile": {"english": "Profile", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "rates": {"english": "Rates", "translation": "Rates", "needs_translation": "false"}, "flights": {"english": "Flights", "translation": "Flights", "needs_translation": "false"}, "lq": {"english": "LQ", "translation": "LQ", "needs_translation": "false"}, "time": {"english": "Time", "translation": "Tijd", "needs_translation": "false"}, "blackbox": {"english": "Blackbox", "translation": "Blackbox", "needs_translation": "false"}, "throttle": {"english": "<PERSON>hrottle", "translation": "<PERSON>hrottle", "needs_translation": "false"}, "flight_time": {"english": "Flight Time", "translation": "Flight Tijd", "needs_translation": "false"}, "rssi_min": {"english": "RSSI Min", "translation": "RSSI min", "needs_translation": "false"}, "current": {"english": "Current", "translation": "Stroom", "needs_translation": "false"}, "timer": {"english": "Timer", "translation": "Timer", "needs_translation": "false"}, "rpm": {"english": "RPM", "translation": "RPM", "needs_translation": "false"}, "min_voltage": {"english": "Min Voltage", "translation": "Min voltage", "needs_translation": "false"}, "max_voltage": {"english": "Max Voltage", "translation": "Max voltage", "needs_translation": "false"}, "min_current": {"english": "Min Current", "translation": "<PERSON> stroom", "needs_translation": "false"}, "max_current": {"english": "Max <PERSON>", "translation": "<PERSON> stroom", "needs_translation": "false"}, "max_tmcu": {"english": "Max T.MCU", "translation": "Max T.MCU", "needs_translation": "false"}, "max_emcu": {"english": "<PERSON>", "translation": "<PERSON>", "needs_translation": "false"}, "altitude": {"english": "Altitude", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "altitude_max": {"english": "Altitude Max", "translation": "<PERSON><PERSON><PERSON> max", "needs_translation": "false"}, "power": {"english": "Power", "translation": "Vermogen", "needs_translation": "false"}, "cell_voltage": {"english": "Cell Voltage", "translation": "Cel voltage", "needs_translation": "false"}, "volts_per_cell": {"english": "Volts per cell", "translation": "Voltage per cel", "needs_translation": "false"}, "warning": {"english": "Warning", "translation": "Waarschuwing", "needs_translation": "false"}, "tx_batt": {"english": "TX Battery", "translation": "TX Battery", "needs_translation": "true"}, "link_max": {"english": "<PERSON>", "translation": "<PERSON>", "needs_translation": "true"}}