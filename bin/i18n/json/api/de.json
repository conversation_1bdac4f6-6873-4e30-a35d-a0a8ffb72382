{"RC_TUNING": {"setpoint_boost_cutoff_2": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Boost-Grenzwert fuer den Sollwert.", "needs_translation": "false"}, "response_time_3": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "<PERSON><PERSON><PERSON><PERSON> oder verringert die Reaktionszeit der Rate, um die Heli-Bewegungen zu glaetten.", "needs_translation": "false"}, "accel_limit_4": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Maximale Beschleunigung des Modells als Reaktion auf eine Knueppelbewegung.", "needs_translation": "false"}, "setpoint_boost_gain_4": {"english": "Boost gain for the setpoint.", "translation": "Boost-Verstaerkung fuer den Sollwert.", "needs_translation": "false"}, "yaw_dynamic_deadband_filter": {"english": "The maximum filter applied to the yaw dynamic deadband.", "translation": "Der maximale Filter, der auf die dynamische Yaw-Totzone angewendet wird.", "needs_translation": "false"}, "setpoint_boost_gain_3": {"english": "Boost gain for the setpoint.", "translation": "Boost-Verstaerkung fuer den Sollwert.", "needs_translation": "false"}, "response_time_2": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "<PERSON><PERSON><PERSON><PERSON> oder verringert die Reaktionszeit der Rate, um die Heli-Bewegungen zu glaetten.", "needs_translation": "false"}, "accel_limit_1": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Maximale Beschleunigung des Modells als Reaktion auf eine Knueppelbewegung.", "needs_translation": "false"}, "setpoint_boost_cutoff_1": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Boost-Grenzwert fuer den Sollwert.", "needs_translation": "false"}, "setpoint_boost_cutoff_4": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Boost-Grenzwert fuer den Sollwert.", "needs_translation": "false"}, "setpoint_boost_gain_2": {"english": "Boost gain for the setpoint.", "translation": "Boost-Verstaerkung fuer den Sollwert.", "needs_translation": "false"}, "accel_limit_2": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Maximale Beschleunigung des Modells als Reaktion auf eine Knueppelbewegung.", "needs_translation": "false"}, "yaw_dynamic_deadband_gain": {"english": "The maximum gain applied to the yaw dynamic deadband.", "translation": "Der maximale Gewinn, der auf die dynamische Yaw-Totzone angewendet wird.", "needs_translation": "false"}, "accel_limit_3": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Maximale Beschleunigung des Modells als Reaktion auf eine Knueppelbewegung.", "needs_translation": "false"}, "response_time_4": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "<PERSON><PERSON><PERSON><PERSON> oder verringert die Reaktionszeit der Rate, um die Heli-Bewegungen zu glaetten.", "needs_translation": "false"}, "setpoint_boost_cutoff_3": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Boost-Grenzwert fuer den Sollwert.", "needs_translation": "false"}, "setpoint_boost_gain_1": {"english": "Boost gain for the setpoint.", "translation": "Boost-Verstaerkung fuer den Sollwert.", "needs_translation": "false"}, "yaw_dynamic_ceiling_gain": {"english": "The maximum gain applied to the yaw dynamic ceiling.", "translation": "Der maximale Gewinn, der auf die dynamische Yaw-Obergrenze angewendet wird.", "needs_translation": "false"}, "response_time_1": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "<PERSON><PERSON><PERSON><PERSON> oder verringert die Reaktionszeit der Rate, um die Heli-Bewegungen zu glaetten.", "needs_translation": "false"}}, "RESCUE_PROFILE": {"rescue_flip_gain": {"english": "Determine how aggressively the heli flips during inverted rescue.", "translation": "<PERSON><PERSON><PERSON><PERSON>, wie aggressiv der Heli waehrend der invertierten Rettung flippt.", "needs_translation": "false"}, "rescue_level_gain": {"english": "Determine how aggressively the heli levels during rescue.", "translation": "<PERSON><PERSON><PERSON><PERSON>, wie aggressiv der Heli sich waehrend der Rettung ausrichtet.", "needs_translation": "false"}, "tbl_off": {"english": "OFF", "translation": "AUS", "needs_translation": "false"}, "rescue_hover_collective": {"english": "Collective value for hover.", "translation": "Kollektivwert fuer das Schweben.", "needs_translation": "false"}, "rescue_max_setpoint_rate": {"english": "Limit rescue roll/pitch rate. Larger helicopters may need slower rotation rates.", "translation": "Begrenzt die Roll-/Nick-Rate waehrend der Rettung. Groessere Helis benoetigen moeglicherweise langsamere Drehgeschwindigkeiten.", "needs_translation": "false"}, "tbl_flip": {"english": "FLIP", "translation": "FLIP", "needs_translation": "false"}, "rescue_flip_mode": {"english": "If rescue is activated while inverted, flip to upright - or remain inverted.", "translation": "Falls die Rettung im invertierten Zustand aktiviert wird: Aufrichten oder invertiert bleiben.", "needs_translation": "false"}, "rescue_pull_up_time": {"english": "When rescue is activated, helicopter will apply pull-up collective for this time period before moving to flip or climb stage.", "translation": "Wenn die Rettung aktiviert wird, wird fuer diese Dauer kollektives Steigen angewendet, bevor zum Flip- oder Steigstadium uebergegangen wird.", "needs_translation": "false"}, "tbl_noflip": {"english": "NO FLIP", "translation": "KEIN FLIP", "needs_translation": "false"}, "rescue_exit_time": {"english": "This limits rapid application of negative collective if the helicopter has rolled during rescue.", "translation": "Begrenzt die schnelle Anwendung von negativem Kollektiv, falls der Heli sich waehrend der Rettung gedreht hat.", "needs_translation": "false"}, "rescue_pull_up_collective": {"english": "Collective value for pull-up climb.", "translation": "Kollektivwert fuer das Hochziehen.", "needs_translation": "false"}, "rescue_max_setpoint_accel": {"english": "Limit how fast the helicopter accelerates into a roll/pitch. Larger helicopters may need slower acceleration.", "translation": "<PERSON><PERSON><PERSON><PERSON>, wie schnell der Heli in eine Roll-/Nick-Bewegung beschleunigt. Groessere Helis benoetigen moeglicherweise langsamere Beschleunigungswerte.", "needs_translation": "false"}, "tbl_on": {"english": "ON", "translation": "EIN", "needs_translation": "false"}, "rescue_climb_collective": {"english": "Collective value for rescue climb.", "translation": "Kollektivwert fuer den Steigflug waehrend der Rettung.", "needs_translation": "false"}, "rescue_flip_time": {"english": "If the helicopter is in rescue and is trying to flip to upright and does not within this time, rescue will be aborted.", "translation": "Falls der Heli in der Rettung nicht innerhalb dieser Zeit aufrichtet, wird die Rettung abgebrochen.", "needs_translation": "false"}, "rescue_climb_time": {"english": "Length of time the climb collective is applied before switching to hover.", "translation": "<PERSON>uer des Steigflugs, bevor in den Schwebezustand gewechselt wird.", "needs_translation": "false"}}, "ESC_SENSOR_CONFIG": {"hw4_current_offset": {"english": "Hobbywing v4 current offset adjustment", "translation": "Hobbywing v4 Strom-Offset-Anpassung", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "Aus", "needs_translation": "false"}, "update_hz": {"english": "ESC telemetry update rate", "translation": "ESC-Telemetrie-Aktualisierungsrate", "needs_translation": "false"}, "half_duplex": {"english": "Half duplex mode for ESC telemetry", "translation": "Halbduplex-Modus fuer ESC-Telemetrie", "needs_translation": "false"}, "consumption_correction": {"english": "Adjust the consumption correction", "translation": "Anpassen der Verbrauchskorrektur", "needs_translation": "false"}, "current_offset": {"english": "Current sensor offset adjustment", "translation": "Stromsensor-Offset-Anpassung", "needs_translation": "false"}, "voltage_correction": {"english": "Adjust the voltage correction", "translation": "Anpassen der Spannungskorrektur", "needs_translation": "false"}, "hw4_voltage_gain": {"english": "Hobbywing v4 voltage gain adjustment", "translation": "Hobbywing v4 Spannungsverstaerkung-Anpassung", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "Ein", "needs_translation": "false"}, "hw4_current_gain": {"english": "Hobbywing v4 current gain adjustment", "translation": "Hobbywing v4 Stromverstaerkung-Anpassung", "needs_translation": "false"}, "current_correction": {"english": "Adjust current correction", "translation": "Anpassen der Stromkorrektur", "needs_translation": "false"}, "pin_swap": {"english": "Swap the TX and RX pins for the ESC telemetry", "translation": "Tauschen der TX- und RX-Pins fuer die ESC-Telemetrie", "needs_translation": "false"}}, "ESC_PARAMETERS_FLYROTOR": {"tbl_alwayson": {"english": "Always On", "translation": "Immer an", "needs_translation": "false"}, "throttle_min": {"english": "Minimum throttle value", "translation": "Minimaler Gaswert", "needs_translation": "false"}, "tbl_disabled": {"english": "Disabled", "translation": "Deaktiviert", "needs_translation": "false"}, "tbl_auto": {"english": "Auto", "translation": "Auto", "needs_translation": "false"}, "starting_torque": {"english": "Starting torque for the motor", "translation": "Anlaufmoment fuer den Motor", "needs_translation": "false"}, "cell_count": {"english": "Number of cells in the battery", "translation": "Anzahl der Zellen in der Batterie", "needs_translation": "false"}, "motor_erpm_max": {"english": "Maximum RPM", "translation": "Maximale RPM", "needs_translation": "false"}, "throttle_max": {"english": "Maximum throttle value", "translation": "Maximaler Gaswert", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "tbl_escgov": {"english": "Esc Governor", "translation": "ESC-Governor", "needs_translation": "false"}, "temperature_protection": {"english": "Temperature at which we cut power by 50%", "translation": "Temperatur, bei der die Leistung um 50 % reduziert wird", "needs_translation": "false"}, "tbl_automatic": {"english": "Automatic", "translation": "Automatisch", "needs_translation": "false"}, "low_voltage_protection": {"english": "Voltage at which we cut power by 50%", "translation": "Spannung, bei der die Leistung um 50 % reduziert wird", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "soft_start": {"english": "Soft start value", "translation": "Wert fuer sanftes Anlaufen", "needs_translation": "false"}, "gov_i": {"english": "Integral value for the governor", "translation": "Integralwert fuer den Governor", "needs_translation": "false"}, "timing_angle": {"english": "Timing angle for the motor", "translation": "Timing-<PERSON><PERSON> fuer den <PERSON>", "needs_translation": "false"}, "response_speed": {"english": "Response speed for the motor", "translation": "Reaktionsgeschwindigkeit des Motors", "needs_translation": "false"}, "current_gain": {"english": "Gain value for the current sensor", "translation": "Verstaerkungswert fuer den Stromsensor", "needs_translation": "false"}, "tbl_extgov": {"english": "External Governor", "translation": "Externer Governor", "needs_translation": "false"}, "buzzer_volume": {"english": "Buzzer volume", "translation": "Summerlautstaerke", "needs_translation": "false"}, "gov_d": {"english": "Derivative value for the governor", "translation": "Differenzialwert fuer den Governor", "needs_translation": "false"}, "tbl_enabled": {"english": "Enabled", "translation": "Aktiviert", "needs_translation": "false"}, "gov_p": {"english": "Proportional value for the governor", "translation": "Proportionalwert fuer den Governor", "needs_translation": "false"}}, "ESC_PARAMETERS_YGE": {"tbl_alwayson": {"english": "Always On", "translation": "Immer an", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "Aus", "needs_translation": "false"}, "tbl_modestore": {"english": "Heli Governor Store", "translation": "Heli Governor <PERSON><PERSON>", "needs_translation": "false"}, "tbl_modefree": {"english": "Free (Attention!)", "translation": "<PERSON>ei (Achtung!)", "needs_translation": "false"}, "tbl_modeglider": {"english": "Aero Glider", "translation": "Segelflugzeug", "needs_translation": "false"}, "tbl_modeext": {"english": "<PERSON><PERSON>t Governor", "translation": "<PERSON><PERSON> Governor", "needs_translation": "false"}, "tbl_modeheli": {"english": "<PERSON><PERSON> Governor", "translation": "<PERSON><PERSON> Governor", "needs_translation": "false"}, "tbl_medium": {"english": "Medium", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_autonorm": {"english": "Auto Normal", "translation": "Automatisch Normal", "needs_translation": "false"}, "tbl_reverse": {"english": "Reverse", "translation": "Umgekehrt", "needs_translation": "false"}, "tbl_modef3a": {"english": "Aero F3A", "translation": "Luftfahrt F3A", "needs_translation": "false"}, "tbl_auto": {"english": "Auto", "translation": "Automatisch", "needs_translation": "false"}, "tbl_slowdown": {"english": "Slowdown", "translation": "Verlangsamen", "needs_translation": "false"}, "tbl_slow": {"english": "Slow", "translation": "Langsam", "needs_translation": "false"}, "tbl_modeair": {"english": "Aero Motor", "translation": "Luftfahrtmotor", "needs_translation": "false"}, "tbl_normal": {"english": "Normal", "translation": "Normal", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "An", "needs_translation": "false"}, "tbl_autoextreme": {"english": "Auto Extreme", "translation": "Automatisch Extrem", "needs_translation": "false"}, "tbl_autoefficient": {"english": "Auto Efficient", "translation": "Automatisch Effizient", "needs_translation": "false"}, "tbl_smooth": {"english": "Smooth", "translation": "Sanft", "needs_translation": "false"}, "tbl_fast": {"english": "Fast", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_custom": {"english": "Custom (PC Defined)", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (PC-Definiert)", "needs_translation": "false"}, "tbl_cutoff": {"english": "Cutoff", "translation": "Abschaltung", "needs_translation": "false"}, "tbl_autopower": {"english": "Auto Power", "translation": "Automatisch Leistung", "needs_translation": "false"}, "tbl_unused": {"english": "*Unused*", "translation": "*Nicht verwendet*", "needs_translation": "false"}}, "GOVERNOR_PROFILE": {"governor_tta_gain": {"english": "TTA gain applied to increase headspeed to control the tail in the negative direction (e.g. motorised tail less than idle speed).", "translation": "TTA-Verstaerkung zur Erhoehung der Drehzahl zur Steuerung des Hecks in die negative Richtung (z. B. motorisiertes Heck unter Leerlaufdrehzahl).", "needs_translation": "false"}, "governor_collective_ff_weight": {"english": "Collective precompensation weight - how much collective is mixed into the feedforward.", "translation": "Kollektive Vorkompensationsgewichtung – wie stark kollektiv in das Feedforward gemischt wird.", "needs_translation": "false"}, "governor_i_gain": {"english": "PID loop I-term gain.", "translation": "I-Term-Verstaerkung der PID-Regler.", "needs_translation": "false"}, "governor_cyclic_ff_weight": {"english": "Cyclic precompensation weight - how much cyclic is mixed into the feedforward.", "translation": "Zyklische Vorkompensationsgewichtung – wie stark zyklisch in das Feedforward gemischt wird.", "needs_translation": "false"}, "governor_f_gain": {"english": "Feedforward gain.", "translation": "Feedforward-Verstaerkung.", "needs_translation": "false"}, "governor_gain": {"english": "Master PID loop gain.", "translation": "Master-Verstaerkung der PID-Regler.", "needs_translation": "false"}, "governor_headspeed": {"english": "Target headspeed for the current profile.", "translation": "Ziel-Dreh<PERSON>hl fuer das aktuelle Profil.", "needs_translation": "false"}, "governor_min_throttle": {"english": "Minimum output throttle the governor is allowed to use.", "translation": "Minimales Ausgangs-Gas, das der Governor verwenden darf.", "needs_translation": "false"}, "governor_d_gain": {"english": "PID loop D-term gain.", "translation": "D-Term-Verstaerkung der PID-Regler.", "needs_translation": "false"}, "governor_p_gain": {"english": "PID loop P-term gain.", "translation": "P-Term-Verstaerkung der PID-Regler.", "needs_translation": "false"}, "governor_yaw_ff_weight": {"english": "Yaw precompensation weight - how much yaw is mixed into the feedforward.", "translation": "Gier-Vorkompensationsgewicht – wie stark Gier in das Feedforward gemischt wird.", "needs_translation": "false"}, "governor_max_throttle": {"english": "Maximum output throttle the governor is allowed to use.", "translation": "Maximales Ausgangs-Gas, das der Governor verwenden darf.", "needs_translation": "false"}, "governor_tta_limit": {"english": "TTA max headspeed increase over full headspeed.", "translation": "Maximale TTA-Drehzahlerhoehung ueber die volle Drehzahl hinaus.", "needs_translation": "false"}}, "PID_PROFILE": {"bterm_cutoff_2": {"english": "B-term cutoff in Hz.", "translation": "B-Term-Grenzfrequenz in Hz.", "needs_translation": "false"}, "dterm_cutoff_1": {"english": "D-term cutoff in Hz.", "translation": "D-Term-Grenzfrequenz in Hz.", "needs_translation": "false"}, "bterm_cutoff_1": {"english": "B-term cutoff in Hz.", "translation": "B-Term-Grenzfrequenz in Hz.", "needs_translation": "false"}, "gyro_cutoff_1": {"english": "PID loop overall bandwidth in Hz.", "translation": "Gesamtbandbreite des PID-Reglers in Hz.", "needs_translation": "false"}, "tbl_on": {"english": "ON", "translation": "EIN", "needs_translation": "false"}, "dterm_cutoff_2": {"english": "D-term cutoff in Hz.", "translation": "D-Term-Grenzfrequenz in Hz.", "needs_translation": "false"}, "yaw_inertia_precomp_cutoff": {"english": "Cutoff. Derivative cutoff frequency in 1/10Hz steps. Controls how sharp the precomp is. Higher value is sharper.", "translation": "Grenzfrequenz der Ableitung in 1/10Hz-Schritten. Hoeher bedeutet schaerfere Vorkompensation.", "needs_translation": "false"}, "offset_limit_0": {"english": "Hard limit for the High Speed Integral offset angle in the PID loop. The O-term will never go over these limits.", "translation": "Harte Begrenzung fuer den High Speed Integral Offset-Winkel in der PID-Schleife. Der O-Term wird nie ueber diese Grenzen hinausgehen.", "needs_translation": "false"}, "cyclic_cross_coupling_ratio": {"english": "Amount of roll-to-pitch compensation needed, vs. pitch-to-roll.", "translation": "Men<PERSON> der Roll-zu-Pitch-Kompensation vs. Pitch-zu-Roll.", "needs_translation": "false"}, "yaw_precomp_cutoff": {"english": "Frequency limit for all yaw precompensation actions.", "translation": "Frequenzgrenze fuer alle Gier-Vorkompensationen.", "needs_translation": "false"}, "error_limit_0": {"english": "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.", "translation": "Harte Begrenzung fuer den Winkel-Fehler im PID-Regler. Der absolute Fehler und damit der I-Term werden nie ueber diese Grenzen hinausgehen.", "needs_translation": "false"}, "trainer_gain": {"english": "Determines how aggressively the helicopter tilts back to the maximum angle (if exceeded) while in Acro Trainer Mode.", "translation": "<PERSON><PERSON><PERSON><PERSON>, wie stark der Heli im Acro-Trainer-<PERSON><PERSON>, wenn der Maximalwinkel ueberschritten wird.", "needs_translation": "false"}, "tbl_rpy": {"english": "RPY", "translation": "RPY", "needs_translation": "false"}, "gyro_cutoff_2": {"english": "PID loop overall bandwidth in Hz.", "translation": "Gesamtbandbreite des PID-Reglers in Hz.", "needs_translation": "false"}, "yaw_ccw_stop_gain": {"english": "Stop gain (PD) for counter-clockwise rotation.", "translation": "Stopp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (PD) fuer die Drehung gegen den Uhrzeigersinn.", "needs_translation": "false"}, "trainer_angle_limit": {"english": "Limit the maximum angle the helicopter will pitch/roll to while in Acro Trainer Mode.", "translation": "Begrenzt den maximalen Pitch/Roll-Winkel im Acro-Trainer-Modus.", "needs_translation": "false"}, "tbl_rp": {"english": "RP", "translation": "RP", "needs_translation": "false"}, "yaw_cyclic_ff_gain": {"english": "Cyclic feedforward mixed into yaw (cyclic-to-yaw precomp).", "translation": "Zyklische Feedforward-Mischung in Gier (zyklisch-zu-Gier Vorkompensation).", "needs_translation": "false"}, "error_decay_time_cyclic": {"english": "Time constant for bleeding off cyclic I-term. Higher will stabilize hover, lower will drift.", "translation": "Zeitkonstante fuer das allmaehliche Reduzieren des zyklischen I-Terms. Hoeher stabilisiert den Schwebeflug, nied<PERSON>er fuehrt zu mehr Drift.", "needs_translation": "false"}, "error_decay_limit_cyclic": {"english": "Maximum bleed-off speed for cyclic I-term.", "translation": "Maximale Geschwindigkeit fuer das Reduzieren des zyklischen I-Terms.", "needs_translation": "false"}, "cyclic_cross_coupling_gain": {"english": "Amount of compensation applied for pitch-to-roll decoupling.", "translation": "Menge der angewendeten Kompensation fuer Pitch-Roll-Entkopplung.", "needs_translation": "false"}, "yaw_collective_dynamic_decay": {"english": "Decay time for the extra yaw precomp on collective input.", "translation": "Abfallzeit fuer die zusaetzliche Gier-Vorkompensation bei kollektiven Eingaben.", "needs_translation": "false"}, "pitch_collective_ff_gain": {"english": "Increasing will compensate for the pitching up motion caused by tail drag when climbing.", "translation": "<PERSON><PERSON><PERSON><PERSON>, um das Nick-Aufrichten durch Heckwiderstand beim Steigen auszugleichen.", "needs_translation": "false"}, "iterm_relax_type": {"english": "Choose the axes in which this is active. RP: Roll, Pitch. RPY: Roll, Pitch, Yaw.", "translation": "Waehlen Sie die Achsen, auf denen dies aktiv ist. RP: Roll, Pitch. RPY: Roll, <PERSON><PERSON>, G<PERSON>.", "needs_translation": "false"}, "offset_limit_1": {"english": "Hard limit for the High Speed Integral offset angle in the PID loop. The O-term will never go over these limits.", "translation": "Harte Begrenzung fuer den High Speed Integral Offset-Winkel.", "needs_translation": "false"}, "iterm_relax_cutoff_1": {"english": "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.", "translation": "<PERSON><PERSON><PERSON>, das Nachschwingen zu reduzieren.", "needs_translation": "false"}, "error_limit_1": {"english": "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.", "translation": "Harte Begrenzung fuer den Winkel-Fehler im PID-Regler.", "needs_translation": "false"}, "horizon_level_strength": {"english": "Determines how aggressively the helicopter tilts back to level while in Horizon Mode.", "translation": "<PERSON><PERSON><PERSON><PERSON>, wie stark der Heli im Horizontmodus zur Horizontalen zurueckkippt.", "needs_translation": "false"}, "error_limit_2": {"english": "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.", "translation": "Harte Begrenzung fuer den Winkel-Fehler im PID-Regler.", "needs_translation": "false"}, "iterm_relax_cutoff_2": {"english": "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.", "translation": "<PERSON><PERSON><PERSON>, das Nachschwingen zu reduzieren.", "needs_translation": "false"}, "tbl_off": {"english": "OFF", "translation": "AUS", "needs_translation": "false"}, "yaw_collective_ff_gain": {"english": "Collective feedforward mixed into yaw (collective-to-yaw precomp).", "translation": "Kollektive Feedforward-Mischung in Gier (kollektiv-zu-Gier Vorkompensation).", "needs_translation": "false"}, "gyro_cutoff_0": {"english": "PID loop overall bandwidth in Hz.", "translation": "Gesamtbandbreite des PID-Reglers in Hz.", "needs_translation": "false"}, "yaw_collective_dynamic_gain": {"english": "An extra boost of yaw precomp on collective input.", "translation": "Zusaetzlicher Gier-Vorkompensations-Boost bei kollektiven Eingaben.", "needs_translation": "false"}, "cyclic_cross_coupling_cutoff": {"english": "Frequency limit for the compensation. Higher value will make the compensation action faster.", "translation": "Frequenzgrenze fuer die Kompensation. Hoeher bedeutet schnellere Korrektur.", "needs_translation": "false"}, "error_rotation": {"english": "Rotates the current roll and pitch error terms around yaw when the craft rotates. This is sometimes called Piro Compensation.", "translation": "Dreht die aktuellen Roll- und Nick-Fehlerwerte um die Gierachse, wenn sich das Modell dreht. Auch als Piro-Kompensation bekannt.", "needs_translation": "false"}, "angle_level_limit": {"english": "Limit the maximum angle the helicopter will pitch/roll to while in Angle mode.", "translation": "Begrenzt den maximalen Pitch/Roll-Winkel im Winkelmodus.", "needs_translation": "false"}, "yaw_cw_stop_gain": {"english": "Stop gain (PD) for clockwise rotation.", "translation": "Stopp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (PD) fuer die Drehung im Uhrzeigersinn.", "needs_translation": "false"}, "iterm_relax_cutoff_0": {"english": "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.", "translation": "<PERSON><PERSON><PERSON>, das Nachschwingen nach schnellen Knueppelbewegungen zu reduzieren. Kann zu Inkonsistenzen bei kleinen Knueppelbewegungen fuehren, wenn zu niedrig.", "needs_translation": "false"}, "yaw_inertia_precomp_gain": {"english": "Scalar gain. The strength of the main rotor inertia. Higher value means more precomp is applied to yaw control.", "translation": "Staerke der Hauptrotor-Traegheit. Hoeher bedeutet mehr Vorkompensation in der Giersteuerung.", "needs_translation": "false"}, "dterm_cutoff_0": {"english": "D-term cutoff in Hz.", "translation": "D-Term-Grenzfrequenz in Hz.", "needs_translation": "false"}, "angle_level_strength": {"english": "Determines how aggressively the helicopter tilts back to level while in Angle Mode.", "translation": "<PERSON><PERSON><PERSON><PERSON>, wie stark der Heli im Winkelmodus zur Horizontalen zurueckkippt.", "needs_translation": "false"}, "bterm_cutoff_0": {"english": "B-term cutoff in Hz.", "translation": "B-Term-Grenzfrequenz in Hz.", "needs_translation": "false"}, "error_decay_time_ground": {"english": "Bleeds off the current controller error when the craft is not airborne to stop the craft tipping over.", "translation": "Reduziert den aktuellen Reglerfehler, wenn das Modell nicht in der Luft ist, um ein Umkippen zu verhindern.", "needs_translation": "false"}}, "MIXER_CONFIG": {"swash_trim_1": {"english": "Swash trim to level the swash plate when using fixed links.", "translation": "Taumelscheiben-Trimmung zur Nivellierung bei festen Gestaengen.", "needs_translation": "false"}, "tail_motor_idle": {"english": "Minimum throttle signal sent to the tail motor. This should be set just high enough that the motor does not stop.", "translation": "Minimales Gas-Signal, das an den Heckmotor gesendet wird. Sollte so eingestellt sein, dass der Motor nicht stoppt.", "needs_translation": "false"}, "tail_center_trim": {"english": "Sets tail rotor trim for 0 yaw for variable pitch, or tail motor throttle for 0 yaw for motorized.", "translation": "Heckrotor-Trimmung fuer 0 Gier bei variablem Pitch oder Heckmotor-Gas fuer 0 Gier bei motorisiertem Heck.", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "swash_tta_precomp": {"english": "Mixer precomp for 0 yaw.", "translation": "Mischer-Vorkompensation fuer 0 Gier.", "needs_translation": "false"}, "swash_trim_2": {"english": "Swash trim to level the swash plate when using fixed links.", "translation": "Taumelscheiben-Trimmung zur Nivellierung bei festen Gestaengen.", "needs_translation": "false"}, "swash_geo_correction": {"english": "Adjust if there is too much negative collective or too much positive collective.", "translation": "<PERSON><PERSON><PERSON>, falls zu viel negativer oder positiver Kollektivpitch vorhanden ist.", "needs_translation": "false"}, "swash_trim_0": {"english": "Swash trim to level the swash plate when using fixed links.", "translation": "Taumelscheiben-Trimmung zur Nivellierung bei festen Gestaengen.", "needs_translation": "false"}, "swash_phase": {"english": "Phase offset for the swashplate controls.", "translation": "Phasenversatz fuer die Taumelscheibensteuerung.", "needs_translation": "false"}, "collective_tilt_correction_pos": {"english": "Adjust the collective tilt correction scaling for positive collective pitch.", "translation": "Skalierung der Kollektiv-Neigungskorrektur fuer positiven Pitch.", "needs_translation": "false"}, "collective_tilt_correction_neg": {"english": "Adjust the collective tilt correction scaling for negative collective pitch.", "translation": "Skalierung der Kollektiv-Neigungskorrektur fuer negativen Pitch.", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "swash_pitch_limit": {"english": "Maximum amount of combined cyclic and collective blade pitch.", "translation": "Maximale kombinierte zyklische und kollektive Blattverstellung.", "needs_translation": "false"}}, "MOTOR_CONFIG": {"minthrottle": {"english": "This PWM value is sent to the ESC/Servo at low throttle", "translation": "Dieser PWM-Wert wird an den ESC/Servo bei minimalem Gas gesendet.", "needs_translation": "false"}, "motor_pwm_protocol": {"english": "The protocol used to communicate with the ESC", "translation": "Das Protokoll zur Kommunikation mit dem ESC.", "needs_translation": "false"}, "main_rotor_gear_ratio_0": {"english": "Motor Pinion Gear Tooth Count", "translation": "Zahnzahl des Motorritzels.", "needs_translation": "false"}, "maxthrottle": {"english": "This PWM value is sent to the ESC/Servo at full throttle", "translation": "Dieser PWM-Wert wird an den ESC/Servo bei vollem Gas gesendet.", "needs_translation": "false"}, "mincommand": {"english": "This PWM value is sent when the motor is stopped", "translation": "Dieser PWM-Wert wird gesendet, wenn der Motor gestoppt ist.", "needs_translation": "false"}, "main_rotor_gear_ratio_1": {"english": "Main Gear Tooth Count", "translation": "Zahnzahl des Hauptgetriebes.", "needs_translation": "false"}, "tail_rotor_gear_ratio_1": {"english": "Autorotation Gear Tooth Count", "translation": "Zahnzahl des Autorotationsgetriebes.", "needs_translation": "false"}, "motor_pwm_rate": {"english": "The frequency at which the ESC sends PWM signals to the motor", "translation": "<PERSON> Frequenz, mit der der ESC PWM-Signale an den Motor sendet.", "needs_translation": "false"}, "tail_rotor_gear_ratio_0": {"english": "<PERSON><PERSON> <PERSON> Tooth Count", "translation": "Zahnzahl des Heckgetriebes.", "needs_translation": "false"}, "motor_pole_count_0": {"english": "The number of magnets on the motor bell.", "translation": "Die Anzahl der Magnete an der Motor-Glocke.", "needs_translation": "false"}}, "ESC_PARAMETERS_HW5": {"tbl_cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "tbl_fixedwing": {"english": "Fixed Wing", "translation": "Flaechenflugzeug", "needs_translation": "false"}, "tbl_disabled": {"english": "Disabled", "translation": "Deaktiviert", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "tbl_heligov": {"english": "<PERSON><PERSON> Governor", "translation": "<PERSON><PERSON> Governor", "needs_translation": "false"}, "tbl_softcutoff": {"english": "Soft Cutoff", "translation": "Sanfte Abschaltung", "needs_translation": "false"}, "tbl_proportional": {"english": "Proportional", "translation": "Proportional", "needs_translation": "false"}, "tbl_heliext": {"english": "<PERSON><PERSON>t Governor", "translation": "<PERSON><PERSON> Governor", "needs_translation": "false"}, "tbl_helistore": {"english": "Heli Governor Store", "translation": "Heli Governor <PERSON><PERSON>", "needs_translation": "false"}, "tbl_hardcutoff": {"english": "Hard Cutoff", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_normal": {"english": "Normal", "translation": "Normal", "needs_translation": "false"}, "tbl_autocalculate": {"english": "Auto Calculate", "translation": "Automatische Berechnung", "needs_translation": "false"}, "tbl_enabled": {"english": "Enabled", "translation": "Aktiviert", "needs_translation": "false"}, "tbl_reverse": {"english": "Reverse", "translation": "Umgekehrt", "needs_translation": "false"}}, "PILOT_CONFIG": {"model_param1_value": {"english": "Set this to the expected flight time in seconds.  The transmitter will beep when the flight time is reached.", "translation": "Setzen Sie die erwartete Flugzeit in Sekunden. Die Fernsteuerung biept sobald die Flugzeit erreicht wurde.", "needs_translation": "false"}}, "BATTERY_INI": {"calcfuel_local": {"english": "Calculate Fuel Using", "translation": "Calculate Fuel Using", "needs_translation": "true"}, "tbl_off": {"english": "Current Sensor", "translation": "Current Sensor", "needs_translation": "true"}, "tbl_on": {"english": "Voltage Sensor", "translation": "Voltage Sensor", "needs_translation": "true"}, "sag_multiplier": {"english": "Raise or lower to adjust for the amount of voltage sag you see in flight.", "translation": "Raise or lower to adjust for the amount of voltage sag you see in flight.", "needs_translation": "true"}, "kalman_multiplier": {"english": "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.", "translation": "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.", "needs_translation": "true"}, "alert_off": {"english": "Off", "translation": "Off", "needs_translation": "true"}, "alert_bec": {"english": "BEC", "translation": "BEC", "needs_translation": "true"}, "alert_rxbatt": {"english": "RxBatt", "translation": "<PERSON><PERSON>", "needs_translation": "true"}}, "BATTERY_CONFIG": {"vbatmincellvoltage": {"english": "The minimum voltage per cell before the low voltage alarm is triggered.", "translation": "Die minimale Spannung pro Zelle, bevor der Niederspannungsalarm ausgeloest wird.", "needs_translation": "false"}, "vbatmaxcellvoltage": {"english": "The maximum voltage per cell before the high voltage alarm is triggered.", "translation": "Die maximale Spannung pro Zelle, bevor der Hochspannungsalarm ausgeloest wird.", "needs_translation": "false"}, "vbatwarningcellvoltage": {"english": "The voltage per cell at which the low voltage alarm will start to sound.", "translation": "Die Spannung pro Zelle, bei der der Niederspannungsalarm ausgeloest wird.", "needs_translation": "false"}, "batteryCellCount": {"english": "The number of cells in your battery.", "translation": "Die Anzahl der Zellen in Ihrer Batterie.", "needs_translation": "false"}, "vbatfullcellvoltage": {"english": "The nominal voltage of a fully charged cell.", "translation": "Die Nennspannung einer vollstaendig geladenen Zelle.", "needs_translation": "false"}, "batteryCapacity": {"english": "The milliamp hour capacity of your battery.", "translation": "Die Kapazitaet Ihrer Batterie in Milliamperestunden.", "needs_translation": "false"}}, "ACC_TRIM": {"pitch": {"english": "Use to trim if the heli drifts in one of the stabilized modes (angle, horizon, etc.).", "translation": "Verwenden Sie diese Einstellung, um den Heli zu trimmen, falls er in einem der stabilisierten Modi (Angle, Horizon usw.) driftet.", "needs_translation": "false"}, "roll": {"english": "Use to trim if the heli drifts in one of the stabilized modes (angle, horizon, etc.).", "translation": "Verwenden Sie diese Einstellung, um den Heli zu trimmen, falls er in einem der stabilisierten Modi (Angle, Horizon usw.) driftet.", "needs_translation": "false"}}, "PID_TUNING": {"pid_1_I": {"english": "How tightly the system holds its position.", "translation": "Wie genau das System seine Position haelt.", "needs_translation": "false"}, "pid_2_P": {"english": "How tightly the system tracks the desired setpoint.", "translation": "Wie genau das System dem gewuenschten Sollwert folgt.", "needs_translation": "false"}, "pid_2_I": {"english": "How tightly the system holds its position.", "translation": "Wie genau das System seine Position haelt.", "needs_translation": "false"}, "pid_1_O": {"english": "Used to prevent the craft from pitching when using high collective.", "translation": "Verhindert das Nicken des Modells bei hoher Kollektivleistung.", "needs_translation": "false"}, "pid_1_F": {"english": "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.", "translation": "<PERSON><PERSON><PERSON>, den P-Term basierend auf Knueppelbewegungen zu verstaerken. <PERSON><PERSON><PERSON><PERSON> macht die Reaktion schaerfer, kann aber zu Ueberschwingern fuehren.", "needs_translation": "false"}, "pid_0_D": {"english": "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.", "translation": "Staerke der Daempfung gegen jede Bewegung des Systems, einschliesslich aeusserer Einfluesse. Reduziert auch das Ueberschwingen.", "needs_translation": "false"}, "pid_1_P": {"english": "How tightly the system tracks the desired setpoint.", "translation": "Wie genau das System dem gewuenschten Sollwert folgt.", "needs_translation": "false"}, "pid_0_I": {"english": "How tightly the system holds its position.", "translation": "Wie genau das System seine Position haelt.", "needs_translation": "false"}, "pid_2_B": {"english": "Additional boost on the feedforward to make the heli react more to quick stick movements.", "translation": "Zusaetzlicher Feedforward-<PERSON>ost, um das System reaktionsschneller auf schnelle Knueppelbewegungen zu machen.", "needs_translation": "false"}, "pid_0_O": {"english": "Used to prevent the craft from rolling when using high collective.", "translation": "Verhindert das Rollen des Modells bei hoher Kollektivleistung.", "needs_translation": "false"}, "pid_0_F": {"english": "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.", "translation": "<PERSON><PERSON><PERSON>, den P-Term basierend auf Knueppelbewegungen zu verstaerken. <PERSON><PERSON><PERSON><PERSON> macht die Reaktion schaerfer, kann aber zu Ueberschwingern fuehren.", "needs_translation": "false"}, "pid_2_F": {"english": "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.", "translation": "<PERSON><PERSON><PERSON>, den P-Term basierend auf Knueppelbewegungen zu verstaerken. <PERSON><PERSON><PERSON><PERSON> macht die Reaktion schaerfer, kann aber zu Ueberschwingern fuehren.", "needs_translation": "false"}, "pid_2_D": {"english": "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.", "translation": "Staerke der Daempfung gegen jede Bewegung des Systems, einschliesslich aeusserer Einfluesse. Reduziert auch das Ueberschwingen.", "needs_translation": "false"}, "pid_0_P": {"english": "How tightly the system tracks the desired setpoint.", "translation": "Wie genau das System dem gewuenschten Sollwert folgt.", "needs_translation": "false"}, "pid_1_D": {"english": "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.", "translation": "Staerke der Daempfung gegen jede Bewegung des Systems, einschliesslich aeusserer Einfluesse. Reduziert auch das Ueberschwingen.", "needs_translation": "false"}, "pid_0_B": {"english": "Additional boost on the feedforward to make the heli react more to quick stick movements.", "translation": "Zusaetzlicher Feedforward-<PERSON>ost, um das System reaktionsschneller auf schnelle Knueppelbewegungen zu machen.", "needs_translation": "false"}, "pid_1_B": {"english": "Additional boost on the feedforward to make the heli react more to quick stick movements.", "translation": "Zusaetzlicher Feedforward-<PERSON>ost, um das System reaktionsschneller auf schnelle Knueppelbewegungen zu machen.", "needs_translation": "false"}}, "GOVERNOR_CONFIG": {"tbl_govmode_standard": {"english": "STANDARD", "translation": "STANDARD", "needs_translation": "false"}, "tbl_govmode_mode2": {"english": "MODE2", "translation": "MODUS2", "needs_translation": "false"}, "gov_tracking_time": {"english": "Time constant for headspeed changes, in seconds, measuring the time from zero to full headspeed.", "translation": "Zeitkonstante fuer Drehzahlaenderungen in Sekunden, gemessen von null bis zur vollen Drehzahl.", "needs_translation": "false"}, "tbl_govmode_passthrough": {"english": "PASSTHROUGH", "translation": "DURCHSCHLEIFEN", "needs_translation": "false"}, "tbl_govmode_mode1": {"english": "MODE1", "translation": "MODUS1", "needs_translation": "false"}, "gov_recovery_time": {"english": "Time constant for recovery spoolup, in seconds, measuring the time from zero to full headspeed.", "translation": "Zeitkonstante fuer die Wiederherstellung des Hochfahrens in Sekunden, gemessen von null bis zur vollen Drehzahl.", "needs_translation": "false"}, "gov_startup_time": {"english": "Time constant for slow startup, in seconds, measuring the time from zero to full headspeed.", "translation": "Zeitkonstante fuer den langsamen Start in Sekunden, gemessen von null bis zur vollen Drehzahl.", "needs_translation": "false"}, "gov_handover_throttle": {"english": "Governor activates above this %. Below this the input throttle is passed to the ESC.", "translation": "Der Governor aktiviert sich ueber diesem Wert in %. Darunter wird das Eingangs-Gas an den ESC weitergegeben.", "needs_translation": "false"}, "gov_spoolup_time": {"english": "Time constant for slow spoolup, in seconds, measuring the time from zero to full headspeed.", "translation": "Zeitkonstante fuer das langsame Hochfahren in Sekunden, gemessen von null bis zur vollen Drehzahl.", "needs_translation": "false"}, "gov_spoolup_min_throttle": {"english": "Minimum throttle to use for slow spoolup, in percent. For electric motors the default is 5%, for nitro this should be set so the clutch starts to engage for a smooth spoolup 10-15%.", "translation": "Minimaler Gaswert fuer langsames Hochfahren in Prozent. Bei Elektromotoren ist der Standardwert 5 %, bei Nitro sollte der Wert so eingestellt werden, dass die Kupplung fuer ein sanftes Hochfahren zu greifen beginnt (10-15 %).", "needs_translation": "false"}, "tbl_govmode_off": {"english": "OFF", "translation": "AUS", "needs_translation": "false"}}, "RC_CONFIG": {"rc_deflection": {"english": "Stick deflection from center in microseconds (us).", "translation": "Knueppelausschlag vom Mittelpunkt in Mikrosekunden (µs).", "needs_translation": "false"}, "rc_min_throttle": {"english": "Minimum throttle (0% throttle output) expected from radio, in microseconds (us).", "translation": "Minimales Gas (0 % Gasausgabe), das von der Fernsteuerung erwartet wird, in Mikrosekunden (µs).", "needs_translation": "false"}, "rc_max_throttle": {"english": "Maximum throttle (100% throttle output) expected from radio, in microseconds (us).", "translation": "Maximales Gas (100 % Gasausgabe), das von der Fernsteuerung erwartet wird, in Mikrosekunden (µs).", "needs_translation": "false"}, "rc_arm_throttle": {"english": "Throttle must be at or below this value in microseconds (us) to allow arming. Must be at least 10us lower than minimum throttle.", "translation": "Das Gas muss bei diesem Wert oder darunter liegen (in µs), um das Schaerfen zu ermoeglichen. Muss mindestens 10 µs unter dem Mindestgaswert liegen.", "needs_translation": "false"}, "rc_yaw_deadband": {"english": "Deadband for yaw control in microseconds (us).", "translation": "Totzone fuer die Giersteuerung in Mikrosekunden (µs).", "needs_translation": "false"}, "rc_deadband": {"english": "Deadband for cyclic control in microseconds (us).", "translation": "Totzone fuer die zyklische Steuerung in Mikrosekunden (µs).", "needs_translation": "false"}, "rc_center": {"english": "Stick center in microseconds (us).", "translation": "Knueppelmittelpunkt in Mikrosekunden (µs).", "needs_translation": "false"}}, "FILTER_CONFIG": {"gyro_soft_notch_cutoff_2": {"english": "Width of the notch filter in Hz.", "translation": "Breite des Notch-Filters in Hz.", "needs_translation": "false"}, "gyro_lpf1_static_hz": {"english": "Lowpass filter cutoff frequency in Hz.", "translation": "Tiefpassfilter-Grenzfrequenz in Hz.", "needs_translation": "false"}, "tbl_none": {"english": "NONE", "translation": "KEINE", "needs_translation": "false"}, "dyn_notch_max_hz": {"english": "Maximum frequency to which the notch is applied.", "translation": "Maximale Frequenz, auf die der Notch-Filter angewendet wird.", "needs_translation": "false"}, "tbl_1st": {"english": "1ST", "translation": "1.", "needs_translation": "false"}, "rpm_min_hz": {"english": "Minimum frequency for the RPM filter.", "translation": "Minimale Frequenz des Drehzahlfilters.", "needs_translation": "false"}, "dyn_notch_min_hz": {"english": "Minimum frequency to which the notch is applied.", "translation": "Minimale Frequenz, auf die der Notch-Filter angewendet wird.", "needs_translation": "false"}, "gyro_lpf1_dyn_max_hz": {"english": "Dynamic filter max cutoff in Hz.", "translation": "Dynamischer Filter – maximale Grenzfrequenz in Hz.", "needs_translation": "false"}, "gyro_soft_notch_hz_2": {"english": "Center frequency to which the notch is applied.", "translation": "Zentralfrequenz, auf die der Notch-Filter angewendet wird.", "needs_translation": "false"}, "gyro_soft_notch_cutoff_1": {"english": "Width of the notch filter in Hz.", "translation": "Breite des Notch-Filters in Hz.", "needs_translation": "false"}, "gyro_soft_notch_hz_1": {"english": "Center frequency to which the notch is applied.", "translation": "Zentralfrequenz, auf die der Notch-Filter angewendet wird.", "needs_translation": "false"}, "dyn_notch_count": {"english": "Number of notches to apply.", "translation": "<PERSON><PERSON><PERSON> der Noteches.", "needs_translation": "false"}, "dyn_notch_q": {"english": "Quality factor of the notch filter.", "translation": "Qualitaetsfaktor des dynamischen Notch-Filters.", "needs_translation": "false"}, "gyro_lpf2_static_hz": {"english": "Lowpass filter cutoff frequency in Hz.", "translation": "Tiefpassfilter-Grenzfrequenz in Hz.", "needs_translation": "false"}, "gyro_lpf1_dyn_min_hz": {"english": "Dynamic filter min cutoff in Hz.", "translation": "Dynamischer Filter – minimale Grenzfrequenz in Hz.", "needs_translation": "false"}, "tbl_2nd": {"english": "2ND", "translation": "2.", "needs_translation": "false"}, "tbl_custom": {"english": "CUSTOM", "translation": "BENUTZERDEF.", "needs_translation": "false"}, "tbl_low": {"english": "LOW", "translation": "NIEDRIG", "needs_translation": "false"}, "tbl_medium": {"english": "MEDIUM", "translation": "MITTEL", "needs_translation": "false"}, "tbl_high": {"english": "HIGH", "translation": "HOCH", "needs_translation": "false"}}, "ESC_PARAMETERS_XDFLY": {"tbl_jadegreen": {"english": "JADE GREEN", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "Aus", "needs_translation": "false"}, "tbl_low": {"english": "Low", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_orange": {"english": "ORANGE", "translation": "Orange", "needs_translation": "false"}, "tbl_fmfw": {"english": "Fixed Wing", "translation": "Flaechenflugzeug", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "tbl_medium": {"english": "Medium", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_yellow": {"english": "YELLOW", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_reverse": {"english": "Reverse", "translation": "Umgekehrt", "needs_translation": "false"}, "tbl_red": {"english": "Red", "translation": "Rot", "needs_translation": "false"}, "tbl_high": {"english": "High", "translation": "Hoch", "needs_translation": "false"}, "tbl_auto": {"english": "Auto", "translation": "Automatisch", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "tbl_fmheli": {"english": "Helicopter", "translation": "Helikopter", "needs_translation": "false"}, "tbl_purple": {"english": "PURPLE", "translation": "<PERSON>", "needs_translation": "false"}, "tbl_green": {"english": "GREEN", "translation": "Gruen", "needs_translation": "false"}, "tbl_blue": {"english": "BLUE", "translation": "Blau", "needs_translation": "false"}, "tbl_slow": {"english": "Slow", "translation": "Langsam", "needs_translation": "false"}, "tbl_normal": {"english": "Normal", "translation": "Normal", "needs_translation": "false"}, "tbl_fast": {"english": "Fast", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_escgov": {"english": "ESC Governor", "translation": "ESC-Governor", "needs_translation": "false"}, "tbl_white": {"english": "WHITE", "translation": "<PERSON>", "needs_translation": "false"}, "tbl_cyan": {"english": "CYAN", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_vslow": {"english": "Very Slow", "translation": "<PERSON><PERSON> lang<PERSON>m", "needs_translation": "false"}, "tbl_extgov": {"english": "External Governor", "translation": "Externer Governor", "needs_translation": "false"}, "tbl_pink": {"english": "PINK", "translation": "Pink", "needs_translation": "false"}, "tbl_fwgov": {"english": "Fixed Wing", "translation": "Flaechenflugzeug-Governor", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "An", "needs_translation": "false"}}, "ESC_PARAMETERS_SCORPION": {"tbl_airplane": {"english": "Airplane mode", "translation": "Flugzeugmodus", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "Aus", "needs_translation": "false"}, "tbl_quad": {"english": "Quad mode", "translation": "Quadmodus", "needs_translation": "false"}, "tbl_heligov": {"english": "<PERSON><PERSON> Governor", "translation": "<PERSON><PERSON> Governor", "needs_translation": "false"}, "tbl_exbus": {"english": "Jeti Exbus", "translation": "Jeti Exbus", "needs_translation": "false"}, "tbl_boat": {"english": "Boat mode", "translation": "Bo<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_unsolicited": {"english": "Unsolicited", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_futsbus": {"english": "Futaba SBUS", "translation": "Futaba SBUS", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "tbl_helistore": {"english": "<PERSON>li Governor (stored)", "translation": "<PERSON><PERSON> Governor (gespeichert)", "needs_translation": "false"}, "tbl_standard": {"english": "Standard", "translation": "Standard", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "An", "needs_translation": "false"}, "tbl_vbar": {"english": "VBar", "translation": "VBar", "needs_translation": "false"}, "tbl_vbargov": {"english": "<PERSON><PERSON><PERSON> Governor", "translation": "<PERSON><PERSON><PERSON> Governor", "needs_translation": "false"}, "tbl_extgov": {"english": "External Governor", "translation": "Externer Governor", "needs_translation": "false"}}}