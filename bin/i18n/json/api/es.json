{"RC_TUNING": {"setpoint_boost_cutoff_2": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Limite de impulso para el punto seleccionado.", "needs_translation": "false"}, "response_time_3": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "Incrementar o decrementar el tiempo de respuesta de la tasa para suavizar los movimientos del heli.", "needs_translation": "false"}, "accel_limit_4": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Aceleracion maxima de la aeronave en respuesta a un movimiento del joystick.", "needs_translation": "false"}, "setpoint_boost_gain_4": {"english": "Boost gain for the setpoint.", "translation": "Ganancia de impulso para el punto seleccionado.", "needs_translation": "false"}, "yaw_dynamic_deadband_filter": {"english": "The maximum filter applied to the yaw dynamic deadband.", "translation": "El filtro maximo aplicado a la zona muerta dinamica de la direccion.", "needs_translation": "false"}, "setpoint_boost_gain_3": {"english": "Boost gain for the setpoint.", "translation": "Ganancia de impulso para el punto seleccionado.", "needs_translation": "false"}, "response_time_2": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "Incrementar o decrementar el tiempo de respuesta de la tasa para suavizar los movimientos del heli.", "needs_translation": "false"}, "accel_limit_1": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Aceleracion maxima de la aeronave en respuesta a un movimiento del joystick.", "needs_translation": "false"}, "setpoint_boost_cutoff_1": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Limite de impulso para el punto seleccionado.", "needs_translation": "false"}, "setpoint_boost_cutoff_4": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Limite de impulso para el punto seleccionado.", "needs_translation": "false"}, "setpoint_boost_gain_2": {"english": "Boost gain for the setpoint.", "translation": "Ganancia de impulso para el punto seleccionado.", "needs_translation": "false"}, "accel_limit_2": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Aceleracion maxima de la aeronave en respuesta a un movimiento del joystick.", "needs_translation": "false"}, "yaw_dynamic_deadband_gain": {"english": "The maximum gain applied to the yaw dynamic deadband.", "translation": "La ganancia maxima aplicada a la zona muerta dinamica de la direccion.", "needs_translation": "false"}, "accel_limit_3": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Aceleracion maxima de la aeronave en respuesta a un movimiento del joystick.", "needs_translation": "false"}, "response_time_4": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "Incrementar o decrementar el tiempo de respuesta de la tasa para suavizar los movimientos del heli.", "needs_translation": "false"}, "setpoint_boost_cutoff_3": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Limite de impulso para el punto deseado.", "needs_translation": "false"}, "setpoint_boost_gain_1": {"english": "Boost gain for the setpoint.", "translation": "Ganancia de impulso para el punto deseado.", "needs_translation": "false"}, "yaw_dynamic_ceiling_gain": {"english": "The maximum gain applied to the yaw dynamic ceiling.", "translation": "Ganancia maxima aplicada al limite dinamico de la direccion.", "needs_translation": "false"}, "response_time_1": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "Incrementar o decrementar el tiempo de respuesta de la tasa para suavizar los movimientos del heli.", "needs_translation": "false"}}, "RESCUE_PROFILE": {"rescue_flip_gain": {"english": "Determine how aggressively the heli flips during inverted rescue.", "translation": "Determina que tan agresivamente se da vuelta el heli durante un rescate de invertido.", "needs_translation": "false"}, "rescue_level_gain": {"english": "Determine how aggressively the heli levels during rescue.", "translation": "Determina que tan agresivamente se nivela el heli durante un rescate.", "needs_translation": "false"}, "tbl_off": {"english": "OFF", "translation": "APAGADO", "needs_translation": "false"}, "rescue_hover_collective": {"english": "Collective value for hover.", "translation": "Valor de colectivo para sobrevuelo.", "needs_translation": "false"}, "rescue_max_setpoint_rate": {"english": "Limit rescue roll/pitch rate. Larger helicopters may need slower rotation rates.", "translation": "Limite de tasa de alabeo/cabeceo en rescate. Helicopteros mas grandes pueden necesitar menores tasas de rotacion.", "needs_translation": "false"}, "tbl_flip": {"english": "FLIP", "translation": "VOLTEAR", "needs_translation": "false"}, "rescue_flip_mode": {"english": "If rescue is activated while inverted, flip to upright - or remain inverted.", "translation": "Si se activa la funcion rescate mientras esta invertido, dar vuelta a vertical - o permanecer invertido.", "needs_translation": "false"}, "rescue_pull_up_time": {"english": "When rescue is activated, helicopter will apply pull-up collective for this time period before moving to flip or climb stage.", "translation": "Cuando se activa rescate, el helicoptero aplicara colectivo arriba durante este tiempo antes de continuar con inversion o nivelacion.", "needs_translation": "false"}, "tbl_noflip": {"english": "NO FLIP", "translation": "NO VOLTEAR", "needs_translation": "false"}, "rescue_exit_time": {"english": "This limits rapid application of negative collective if the helicopter has rolled during rescue.", "translation": "Esto limita la aplicacion rapida de colectivo negativo si el helicoptero roto durante el rescate.", "needs_translation": "false"}, "rescue_pull_up_collective": {"english": "Collective value for pull-up climb.", "translation": "Valor de colectivo para trepada (pull-up).", "needs_translation": "false"}, "rescue_max_setpoint_accel": {"english": "Limit how fast the helicopter accelerates into a roll/pitch. Larger helicopters may need slower acceleration.", "translation": "Limite de que tan rapido el helicoptero acelera entrando a un alabeo/cabeceo. Helicopteros mas grandes pueden necesitar menores tasas de aceleracion.", "needs_translation": "false"}, "tbl_on": {"english": "ON", "translation": "ENCENDIDO", "needs_translation": "false"}, "rescue_climb_collective": {"english": "Collective value for rescue climb.", "translation": "Valor de colectivo para trepada de rescate.", "needs_translation": "false"}, "rescue_flip_time": {"english": "If the helicopter is in rescue and is trying to flip to upright and does not within this time, rescue will be aborted.", "translation": "Si el helicoptero esta en rescate e intentando darse vuelta a nivelado y no lo logra en este intervalo de tiempo, se abortara el rescate.", "needs_translation": "false"}, "rescue_climb_time": {"english": "Length of time the climb collective is applied before switching to hover.", "translation": "<PERSON><PERSON>zo de tiempo que se aplica colectivo arriba antes de continuar con sobrevuelo.", "needs_translation": "false"}}, "ESC_SENSOR_CONFIG": {"hw4_current_offset": {"english": "Hobbywing v4 current offset adjustment", "translation": "Ajuste de corriente del Hobbywing v4", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "update_hz": {"english": "ESC telemetry update rate", "translation": "Tasa de refresco de telemetria del ESC", "needs_translation": "false"}, "half_duplex": {"english": "Half duplex mode for ESC telemetry", "translation": "Modo half-duplex para telemetria del ESC", "needs_translation": "false"}, "consumption_correction": {"english": "Adjust the consumption correction", "translation": "Ajuste de la correccion de consumo", "needs_translation": "false"}, "current_offset": {"english": "Current sensor offset adjustment", "translation": "Ajuste del sensor de corriente", "needs_translation": "false"}, "voltage_correction": {"english": "Adjust the voltage correction", "translation": "Ajuste de la correccion de voltaje", "needs_translation": "false"}, "hw4_voltage_gain": {"english": "Hobbywing v4 voltage gain adjustment", "translation": "Ajuste de ganancia de voltaje del Hobbywing v4", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "Encendido", "needs_translation": "false"}, "hw4_current_gain": {"english": "Hobbywing v4 current gain adjustment", "translation": "Ajuste de ganancia de corriente del Hobbywing v4", "needs_translation": "false"}, "current_correction": {"english": "Adjust current correction", "translation": "Ajuste de la correccion de corriente", "needs_translation": "false"}, "pin_swap": {"english": "Swap the TX and RX pins for the ESC telemetry", "translation": "Intercambio de pines TX y RX para telemetria del ESC", "needs_translation": "false"}}, "ESC_PARAMETERS_FLYROTOR": {"tbl_alwayson": {"english": "Always On", "translation": "Siempre Encendido", "needs_translation": "false"}, "throttle_min": {"english": "Minimum throttle value", "translation": "Valor minimo del acelerador", "needs_translation": "false"}, "tbl_disabled": {"english": "Disabled", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_auto": {"english": "Auto", "translation": "Auto", "needs_translation": "false"}, "starting_torque": {"english": "Starting torque for the motor", "translation": "Torque de inicio para el motor", "needs_translation": "false"}, "cell_count": {"english": "Number of cells in the battery", "translation": "Numero de celdas en la bateria", "needs_translation": "false"}, "motor_erpm_max": {"english": "Maximum RPM", "translation": "RPM maxima", "needs_translation": "false"}, "throttle_max": {"english": "Maximum throttle value", "translation": "Valor maximo del acelerador", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "AntiH.", "needs_translation": "false"}, "tbl_escgov": {"english": "Esc Governor", "translation": "Governor del ESC", "needs_translation": "false"}, "temperature_protection": {"english": "Temperature at which we cut power by 50%", "translation": "Temperatura a la cual se disminuye la potencia en un 50%", "needs_translation": "false"}, "tbl_automatic": {"english": "Automatic", "translation": "Automatico", "needs_translation": "false"}, "low_voltage_protection": {"english": "Voltage at which we cut power by 50%", "translation": "Voltaje al cual se disminuye la potencia en un 50%", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "soft_start": {"english": "Soft start value", "translation": "Valor de inicio suave", "needs_translation": "false"}, "gov_i": {"english": "Integral value for the governor", "translation": "Valor de integral para el governor", "needs_translation": "false"}, "timing_angle": {"english": "Timing angle for the motor", "translation": "Angulo de timing para el motor", "needs_translation": "false"}, "response_speed": {"english": "Response speed for the motor", "translation": "Velocidad de respuesta para el motor", "needs_translation": "false"}, "current_gain": {"english": "Gain value for the current sensor", "translation": "Valor de ganancia para el sensor actual", "needs_translation": "false"}, "tbl_extgov": {"english": "External Governor", "translation": "Governor <PERSON><PERSON><PERSON>", "needs_translation": "false"}, "buzzer_volume": {"english": "Buzzer volume", "translation": "Volumen del Buzzer", "needs_translation": "false"}, "gov_d": {"english": "Derivative value for the governor", "translation": "<PERSON>or de derivada para el governor", "needs_translation": "false"}, "tbl_enabled": {"english": "Enabled", "translation": "Habilitado", "needs_translation": "false"}, "gov_p": {"english": "Proportional value for the governor", "translation": "Valor proporcional para el governor", "needs_translation": "false"}}, "ESC_PARAMETERS_YGE": {"tbl_alwayson": {"english": "Always On", "translation": "Siempre encendido", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_modestore": {"english": "Heli Governor Store", "translation": "Governor <PERSON><PERSON>", "needs_translation": "false"}, "tbl_modefree": {"english": "Free (Attention!)", "translation": "Libre (Atencion!)", "needs_translation": "false"}, "tbl_modeglider": {"english": "Aero Glider", "translation": "Aero Planeador", "needs_translation": "false"}, "tbl_modeext": {"english": "<PERSON><PERSON>t Governor", "translation": "Governor <PERSON><PERSON>", "needs_translation": "false"}, "tbl_modeheli": {"english": "<PERSON><PERSON> Governor", "translation": "Governor <PERSON><PERSON>", "needs_translation": "false"}, "tbl_medium": {"english": "Medium", "translation": "Medio", "needs_translation": "false"}, "tbl_autonorm": {"english": "Auto Normal", "translation": "Auto Normal", "needs_translation": "false"}, "tbl_reverse": {"english": "Reverse", "translation": "Revers<PERSON>", "needs_translation": "false"}, "tbl_modef3a": {"english": "Aero F3A", "translation": "Aero F3A", "needs_translation": "false"}, "tbl_auto": {"english": "Auto", "translation": "Auto", "needs_translation": "false"}, "tbl_slowdown": {"english": "Slowdown", "translation": "Desacelerar", "needs_translation": "false"}, "tbl_slow": {"english": "Slow", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_modeair": {"english": "Aero Motor", "translation": "Aero Motor", "needs_translation": "false"}, "tbl_normal": {"english": "Normal", "translation": "Normal", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "Encendido", "needs_translation": "false"}, "tbl_autoextreme": {"english": "Auto Extreme", "translation": "Auto Extremo", "needs_translation": "false"}, "tbl_autoefficient": {"english": "Auto Efficient", "translation": "Auto Eficiente", "needs_translation": "false"}, "tbl_smooth": {"english": "Smooth", "translation": "Suave", "needs_translation": "false"}, "tbl_fast": {"english": "Fast", "translation": "Rapido", "needs_translation": "false"}, "tbl_custom": {"english": "Custom (PC Defined)", "translation": "Personalizado (Definido por PC)", "needs_translation": "false"}, "tbl_cutoff": {"english": "Cutoff", "translation": "Corte", "needs_translation": "false"}, "tbl_autopower": {"english": "Auto Power", "translation": "Auto Potencia", "needs_translation": "false"}, "tbl_unused": {"english": "*Unused*", "translation": "*no se usa*", "needs_translation": "false"}}, "GOVERNOR_PROFILE": {"governor_tta_gain": {"english": "TTA gain applied to increase headspeed to control the tail in the negative direction (e.g. motorised tail less than idle speed).", "translation": "Ganancia TTA aplicada para incrementar la velocidad del rotor para controlar la cola en direccion negativa (ej: la cola motorizada a velocidad menor que ralenti).", "needs_translation": "false"}, "governor_collective_ff_weight": {"english": "Collective precompensation weight - how much collective is mixed into the feedforward.", "translation": "Precompensacion de colectivo - cuanto colectivo se mezcla con el avance (feedforward).", "needs_translation": "false"}, "governor_i_gain": {"english": "PID loop I-term gain.", "translation": "Ganancia I-term del loop PID.", "needs_translation": "false"}, "governor_cyclic_ff_weight": {"english": "Cyclic precompensation weight - how much cyclic is mixed into the feedforward.", "translation": "Precompensacion de ciclico - cuanto ciclico se mezcla con el avance (feedforward).", "needs_translation": "false"}, "governor_f_gain": {"english": "Feedforward gain.", "translation": "Ganancia de avance (FF).", "needs_translation": "false"}, "governor_gain": {"english": "Master PID loop gain.", "translation": "Ganancia del loop PID maestro.", "needs_translation": "false"}, "governor_headspeed": {"english": "Target headspeed for the current profile.", "translation": "Velocidad del rotor deseada para el perfil en uso.", "needs_translation": "false"}, "governor_min_throttle": {"english": "Minimum output throttle the governor is allowed to use.", "translation": "Salida minima de acelerador que se le permite usar al governor.", "needs_translation": "false"}, "governor_d_gain": {"english": "PID loop D-term gain.", "translation": "Ganancia D-term del loop PID.", "needs_translation": "false"}, "governor_p_gain": {"english": "PID loop P-term gain.", "translation": "Ganancia P-term del loop PID.", "needs_translation": "false"}, "governor_yaw_ff_weight": {"english": "Yaw precompensation weight - how much yaw is mixed into the feedforward.", "translation": "Precompensacion de direccion - cuanto comando de direccion se mezcla con el avance (feedforward).", "needs_translation": "false"}, "governor_max_throttle": {"english": "Maximum output throttle the governor is allowed to use.", "translation": "Salida maxima de acelerador que se le permite usar al governor.", "needs_translation": "false"}, "governor_tta_limit": {"english": "TTA max headspeed increase over full headspeed.", "translation": "Maximo incremento TTA por encima de la velocidad maxima del rotor.", "needs_translation": "false"}}, "PID_PROFILE": {"bterm_cutoff_2": {"english": "B-term cutoff in Hz.", "translation": "Corte de B-term en Hz.", "needs_translation": "false"}, "dterm_cutoff_1": {"english": "D-term cutoff in Hz.", "translation": "Corte D-term en Hz.", "needs_translation": "false"}, "bterm_cutoff_1": {"english": "B-term cutoff in Hz.", "translation": "Corte de B-term en Hz.", "needs_translation": "false"}, "gyro_cutoff_1": {"english": "PID loop overall bandwidth in Hz.", "translation": "Ancho de banda general para el loop PID en Hz.", "needs_translation": "false"}, "tbl_on": {"english": "ON", "translation": "ENCENDIDO", "needs_translation": "false"}, "dterm_cutoff_2": {"english": "D-term cutoff in Hz.", "translation": "Corte D-term en Hz.", "needs_translation": "false"}, "yaw_inertia_precomp_cutoff": {"english": "Cutoff. Derivative cutoff frequency in 1/10Hz steps. Controls how sharp the precomp is. Higher value is sharper.", "translation": "Corte. Frecuencia de corte derivada, en pasos de 1/10Hz. Controla que tan agresivamente se precompensa. Un valor mayor es mas fuerte.", "needs_translation": "false"}, "offset_limit_0": {"english": "Hard limit for the High Speed Integral offset angle in the PID loop. The O-term will never go over these limits.", "translation": "Limite duro del ajuste de angulo para la Integral de Alta Velocidad (HSI) en el loop PID. El O-term nunca superara estos limites.", "needs_translation": "false"}, "cyclic_cross_coupling_ratio": {"english": "Amount of roll-to-pitch compensation needed, vs. pitch-to-roll.", "translation": "Cantidad de compensacion alabeo-a-cabeceo necesaria, vs. cabeceo-a-alabeo.", "needs_translation": "false"}, "yaw_precomp_cutoff": {"english": "Frequency limit for all yaw precompensation actions.", "translation": "Limite de frecuencia para todas las acciones de precompensacion de direccion.", "needs_translation": "false"}, "error_limit_0": {"english": "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.", "translation": "Limite duro del error de angulo en el loop PID. Es el error absoluto y por lo tanto el I-term nunca superara estos limites.", "needs_translation": "false"}, "trainer_gain": {"english": "Determines how aggressively the helicopter tilts back to the maximum angle (if exceeded) while in Acro Trainer Mode.", "translation": "Determina que tan agresivamente el helicoptero vuelve al angulo maximo (si se excedio) mientras esta en modo Entrenador Acro.", "needs_translation": "false"}, "tbl_rpy": {"english": "RPY", "translation": "ACD (RPY)", "needs_translation": "false"}, "gyro_cutoff_2": {"english": "PID loop overall bandwidth in Hz.", "translation": "Ancho de banda general para el loop PID en Hz.", "needs_translation": "false"}, "yaw_ccw_stop_gain": {"english": "Stop gain (PD) for counter-clockwise rotation.", "translation": "Ganancia de detencion (PD) para rotacion antihoraria.", "needs_translation": "false"}, "trainer_angle_limit": {"english": "Limit the maximum angle the helicopter will pitch/roll to while in Acro Trainer Mode.", "translation": "Limita el angulo maximo de cabeceo/alabeo que alcanzara el helicoptero en modo Entrenador Acro.", "needs_translation": "false"}, "tbl_rp": {"english": "RP", "translation": "RP", "needs_translation": "false"}, "yaw_cyclic_ff_gain": {"english": "Cyclic feedforward mixed into yaw (cyclic-to-yaw precomp).", "translation": "Avance de ciclico mezclado con direccion (precomp. ciclico-a-direccion).", "needs_translation": "false"}, "error_decay_time_cyclic": {"english": "Time constant for bleeding off cyclic I-term. Higher will stabilize hover, lower will drift.", "translation": "Constante de tiempo para el alivio del I-term del ciclico. Valores mas altos estabilizan el sobrevuelo, valores menores producen deriva.", "needs_translation": "false"}, "error_decay_limit_cyclic": {"english": "Maximum bleed-off speed for cyclic I-term.", "translation": "Velocidad maxima de alivio del I-term para ciclico.", "needs_translation": "false"}, "cyclic_cross_coupling_gain": {"english": "Amount of compensation applied for pitch-to-roll decoupling.", "translation": "Cantidad de compensacion aplicada al desacople cabeceo-a-alabeo.", "needs_translation": "false"}, "yaw_collective_dynamic_decay": {"english": "Decay time for the extra yaw precomp on collective input.", "translation": "Tiempo de decadencia para precompensacion extra para comandos de colectivo.", "needs_translation": "false"}, "pitch_collective_ff_gain": {"english": "Increasing will compensate for the pitching up motion caused by tail drag when climbing.", "translation": "Incrementarlo compensara el movimiento de cabeceo superior causado por el arrastre de cola al trepar.", "needs_translation": "false"}, "iterm_relax_type": {"english": "Choose the axes in which this is active. RP: Roll, Pitch. RPY: Roll, Pitch, Yaw.", "translation": "Seleccione los ejes en los que esta activado. AC (RP): Alabeo, Cabeceo. ACD (RPY): Alabeo, Cabeceo, Direccion.", "needs_translation": "false"}, "offset_limit_1": {"english": "Hard limit for the High Speed Integral offset angle in the PID loop. The O-term will never go over these limits.", "translation": "Limite duro del ajuste de angulo para la Integral de Alta Velocidad (HSI) en el loop PID. El O-term nunca superara estos limites.", "needs_translation": "false"}, "iterm_relax_cutoff_1": {"english": "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.", "translation": "Ayuda a reducir el rebote luego de movimientos rapidos del joystick. Puede causar inconsistencias en movimientos chicos si esta demasiado bajo.", "needs_translation": "false"}, "error_limit_1": {"english": "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.", "translation": "Limite duro del error de angulo en el loop PID. Es el error absoluto y por lo tanto el I-term nunca superara estos limites.", "needs_translation": "false"}, "horizon_level_strength": {"english": "Determines how aggressively the helicopter tilts back to level while in Horizon Mode.", "translation": "Determina que tan agresivamente el helicoptero vuelve a horizontal en modo horizonte.", "needs_translation": "false"}, "error_limit_2": {"english": "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.", "translation": "Limite duro del error de angulo en el loop PID. Es el error absoluto y por lo tanto el I-term nunca superara estos limites.", "needs_translation": "false"}, "iterm_relax_cutoff_2": {"english": "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.", "translation": "Ayuda a reducir el rebote luego de movimientos rapidos del joystick. Puede causar inconsistencias en movimientos chicos si esta demasiado bajo.", "needs_translation": "false"}, "tbl_off": {"english": "OFF", "translation": "APAGADO", "needs_translation": "false"}, "yaw_collective_ff_gain": {"english": "Collective feedforward mixed into yaw (collective-to-yaw precomp).", "translation": "Avance de colectivo mezclado con direccion (precomp colectivo-a-direccion).", "needs_translation": "false"}, "gyro_cutoff_0": {"english": "PID loop overall bandwidth in Hz.", "translation": "Ancho de banda general para el loop PID en Hz.", "needs_translation": "false"}, "yaw_collective_dynamic_gain": {"english": "An extra boost of yaw precomp on collective input.", "translation": "Aumento de precompensacion extra de direccion para comandos de colectivo.", "needs_translation": "false"}, "cyclic_cross_coupling_cutoff": {"english": "Frequency limit for the compensation. Higher value will make the compensation action faster.", "translation": "Limite de frecuenca de la compensacion. Un valor mayor acelerara la accion de compensacion.", "needs_translation": "false"}, "error_rotation": {"english": "Rotates the current roll and pitch error terms around yaw when the craft rotates. This is sometimes called Piro Compensation.", "translation": "Rota los valores actuales de alabeo y cabeceo alrededor del eje de rotacion vertical mientras el heli esta girando. Este termino se suele llamar Compensacion Piro (de pirueta).", "needs_translation": "false"}, "angle_level_limit": {"english": "Limit the maximum angle the helicopter will pitch/roll to while in Angle mode.", "translation": "Limita el angulo maximo de inclinacion de cabeceo/alabeo en modo angulo.", "needs_translation": "false"}, "yaw_cw_stop_gain": {"english": "Stop gain (PD) for clockwise rotation.", "translation": "Ganancia de detencion (PD) para rotacion horaria.", "needs_translation": "false"}, "iterm_relax_cutoff_0": {"english": "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.", "translation": "Ayuda a reducir el rebote luego de movimientos rapidos del joystick. Puede causar inconsistencias en movimientos chicos si esta demasiado bajo.", "needs_translation": "false"}, "yaw_inertia_precomp_gain": {"english": "Scalar gain. The strength of the main rotor inertia. Higher value means more precomp is applied to yaw control.", "translation": "Ganacia escalar. La fuerza de inercia del rotor principal. Un valor mayor implica mayor precompensacion aplicada al control de direccion.", "needs_translation": "false"}, "dterm_cutoff_0": {"english": "D-term cutoff in Hz.", "translation": "Corte D-term en Hz.", "needs_translation": "false"}, "angle_level_strength": {"english": "Determines how aggressively the helicopter tilts back to level while in Angle Mode.", "translation": "Determina que tan agresivamente el helicoptero vuelve a horizontal en modo angulo.", "needs_translation": "false"}, "bterm_cutoff_0": {"english": "B-term cutoff in Hz.", "translation": "Corte de B-term en Hz.", "needs_translation": "false"}, "error_decay_time_ground": {"english": "Bleeds off the current controller error when the craft is not airborne to stop the craft tipping over.", "translation": "<PERSON><PERSON> del <PERSON> actual del controlador cuando la aeronave esta en tierra, para evitar que se vuelque.", "needs_translation": "false"}}, "MIXER_CONFIG": {"swash_trim_1": {"english": "Swash trim to level the swash plate when using fixed links.", "translation": "Ajuste para nivelar el swashplate cuando se utilizan brazos no extensibles.", "needs_translation": "false"}, "tail_motor_idle": {"english": "Minimum throttle signal sent to the tail motor. This should be set just high enough that the motor does not stop.", "translation": "Senial minima de acelerador que se envia al motor de cola. Deberia configurarse lo suficientemente alta para que no se detenga el motor.", "needs_translation": "false"}, "tail_center_trim": {"english": "Sets tail rotor trim for 0 yaw for variable pitch, or tail motor throttle for 0 yaw for motorized.", "translation": "Define el ajuste de cola para direccion 0 en paso variable, o acelerador del motor para direccion 0 en cola motorizada.", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "swash_tta_precomp": {"english": "Mixer precomp for 0 yaw.", "translation": "Precompensacion de mezclador para direccion 0.", "needs_translation": "false"}, "swash_trim_2": {"english": "Swash trim to level the swash plate when using fixed links.", "translation": "Ajuste para nivelar el swashplate cuando se utilizan brazos no extensibles.", "needs_translation": "false"}, "swash_geo_correction": {"english": "Adjust if there is too much negative collective or too much positive collective.", "translation": "Ajuste si hay demasiado colectivo negativo o positivo.", "needs_translation": "false"}, "swash_trim_0": {"english": "Swash trim to level the swash plate when using fixed links.", "translation": "Ajuste para nivelar el swashplate cuando se utilizan brazos no extensibles.", "needs_translation": "false"}, "swash_phase": {"english": "Phase offset for the swashplate controls.", "translation": "Compensacion de fase para controles del swashplate.", "needs_translation": "false"}, "collective_tilt_correction_pos": {"english": "Adjust the collective tilt correction scaling for positive collective pitch.", "translation": "Ajuste la escala de correccion de inclinacion del colectivo para paso posivo.", "needs_translation": "false"}, "collective_tilt_correction_neg": {"english": "Adjust the collective tilt correction scaling for negative collective pitch.", "translation": "Ajuste la escala de correccion de inclinacion del colectivo para paso negativo", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "AntiH.", "needs_translation": "false"}, "swash_pitch_limit": {"english": "Maximum amount of combined cyclic and collective blade pitch.", "translation": "Valor maximo del angulo combinado de ciclico y colectivo en las palas.", "needs_translation": "false"}}, "MOTOR_CONFIG": {"minthrottle": {"english": "This PWM value is sent to the ESC/Servo at low throttle", "translation": "Este valor PWM se envia al ESC/Servo cuando el acelerador esta bajo", "needs_translation": "false"}, "motor_pwm_protocol": {"english": "The protocol used to communicate with the ESC", "translation": "El protcolo utilizado para comunicarse con el ESC", "needs_translation": "false"}, "main_rotor_gear_ratio_0": {"english": "Motor Pinion Gear Tooth Count", "translation": "Numero de dientes del pinion del motor", "needs_translation": "false"}, "maxthrottle": {"english": "This PWM value is sent to the ESC/Servo at full throttle", "translation": "Este valor PWM se envia al ESC/Servo cuando el acelerador esta a fondo", "needs_translation": "false"}, "mincommand": {"english": "This PWM value is sent when the motor is stopped", "translation": "Este valor PWM se envia cuando el motor esta detenido", "needs_translation": "false"}, "main_rotor_gear_ratio_1": {"english": "Main Gear Tooth Count", "translation": "Numero de dientes del engranaje principal", "needs_translation": "false"}, "tail_rotor_gear_ratio_1": {"english": "Autorotation Gear Tooth Count", "translation": "Numero de dientes del engranaje de autorotacion", "needs_translation": "false"}, "motor_pwm_rate": {"english": "The frequency at which the ESC sends PWM signals to the motor", "translation": "La frecuencia de refresco en Hz de la senial PWM del ESC", "needs_translation": "false"}, "tail_rotor_gear_ratio_0": {"english": "<PERSON><PERSON> <PERSON> Tooth Count", "translation": "Numero de dientes del engranaje de cola", "needs_translation": "false"}, "motor_pole_count_0": {"english": "The number of magnets on the motor bell.", "translation": "El numero de imanes en la campana del motor.", "needs_translation": "false"}}, "ESC_PARAMETERS_HW5": {"tbl_cw": {"english": "CW", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_fixedwing": {"english": "Fixed Wing", "translation": "Ala Fi<PERSON>", "needs_translation": "false"}, "tbl_disabled": {"english": "Disabled", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "AntiH.", "needs_translation": "false"}, "tbl_heligov": {"english": "<PERSON><PERSON> Governor", "translation": "Governor <PERSON><PERSON>", "needs_translation": "false"}, "tbl_softcutoff": {"english": "Soft Cutoff", "translation": "Corte Suave", "needs_translation": "false"}, "tbl_proportional": {"english": "Proportional", "translation": "Proporcional", "needs_translation": "false"}, "tbl_heliext": {"english": "<PERSON><PERSON>t Governor", "translation": "Governor <PERSON><PERSON>", "needs_translation": "false"}, "tbl_helistore": {"english": "Heli Governor Store", "translation": "Guardar Governor <PERSON><PERSON>", "needs_translation": "false"}, "tbl_hardcutoff": {"english": "Hard Cutoff", "translation": "Corte Rapido", "needs_translation": "false"}, "tbl_normal": {"english": "Normal", "translation": "Normal", "needs_translation": "false"}, "tbl_autocalculate": {"english": "Auto Calculate", "translation": "Calculo Auto", "needs_translation": "false"}, "tbl_enabled": {"english": "Enabled", "translation": "Habilitado", "needs_translation": "false"}, "tbl_reverse": {"english": "Reverse", "translation": "Revers<PERSON>", "needs_translation": "false"}}, "PILOT_CONFIG": {"model_param1_value": {"english": "Set this to the expected flight time in seconds.  The transmitter will beep when the flight time is reached.", "translation": "Establezca el tiempo esperado de vuelo en segundos. El transmisor emitirá beeps cuando el tiempo establecido se haya alcanzado.", "needs_translation": "false"}}, "BATTERY_INI": {"calcfuel_local": {"english": "Calculate Fuel Using", "translation": "Calculate Fuel Using", "needs_translation": "true"}, "tbl_off": {"english": "Current Sensor", "translation": "Current Sensor", "needs_translation": "true"}, "tbl_on": {"english": "Voltage Sensor", "translation": "Voltage Sensor", "needs_translation": "true"}, "sag_multiplier": {"english": "Raise or lower to adjust for the amount of voltage sag you see in flight.", "translation": "Raise or lower to adjust for the amount of voltage sag you see in flight.", "needs_translation": "true"}, "kalman_multiplier": {"english": "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.", "translation": "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.", "needs_translation": "true"}, "alert_off": {"english": "Off", "translation": "Off", "needs_translation": "true"}, "alert_bec": {"english": "BEC", "translation": "BEC", "needs_translation": "true"}, "alert_rxbatt": {"english": "RxBatt", "translation": "<PERSON><PERSON>", "needs_translation": "true"}}, "BATTERY_CONFIG": {"vbatmincellvoltage": {"english": "The minimum voltage per cell before the low voltage alarm is triggered.", "translation": "El voltaje minimo por celda al cual se dispara la alarma de bajo voltaje.", "needs_translation": "false"}, "vbatmaxcellvoltage": {"english": "The maximum voltage per cell before the high voltage alarm is triggered.", "translation": "El voltaje maximo por celda al cual se dispara la alarma de voltaje alto.", "needs_translation": "false"}, "vbatwarningcellvoltage": {"english": "The voltage per cell at which the low voltage alarm will start to sound.", "translation": "El voltaje por celda a partir del cual empieza a sonar la alarma de bajo voltaje.", "needs_translation": "false"}, "batteryCellCount": {"english": "The number of cells in your battery.", "translation": "El numero de celdas de su bateria.", "needs_translation": "false"}, "vbatfullcellvoltage": {"english": "The nominal voltage of a fully charged cell.", "translation": "El voltaje nominal de una celda totalmente cargada.", "needs_translation": "false"}, "batteryCapacity": {"english": "The milliamp hour capacity of your battery.", "translation": "La capacidad de su bateria en miliamper-hora.", "needs_translation": "false"}}, "ACC_TRIM": {"pitch": {"english": "Use to trim if the heli drifts in one of the stabilized modes (angle, horizon, etc.).", "translation": "Utilice para corregir si el heli deriva en uno de los modos estabilizados (angulo, horizonte, etc.).", "needs_translation": "false"}, "roll": {"english": "Use to trim if the heli drifts in one of the stabilized modes (angle, horizon, etc.).", "translation": "Utilice para corregir si el heli deriva en uno de los modos estabilizados (angulo, horizonte, etc.).", "needs_translation": "false"}}, "PID_TUNING": {"pid_1_I": {"english": "How tightly the system holds its position.", "translation": "Que tan estrechamente el sistema sostiene su posicion.", "needs_translation": "false"}, "pid_2_P": {"english": "How tightly the system tracks the desired setpoint.", "translation": "Que tan estrechamente el sistema persigue el punto deseado.", "needs_translation": "false"}, "pid_2_I": {"english": "How tightly the system holds its position.", "translation": "Que tan estrechamente el sistema sostiene su posicion.", "needs_translation": "false"}, "pid_1_O": {"english": "Used to prevent the craft from pitching when using high collective.", "translation": "Se utiliza para evitar que la aeronave cabecee cuando se utiliza colectivo alto.", "needs_translation": "false"}, "pid_1_F": {"english": "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.", "translation": "Ayuda a empujar el P-term en funcion de la posicion del joystick. Aumentarlo hara la respuesta mas agresiva, pero puede causar que se pase de largo.", "needs_translation": "false"}, "pid_0_D": {"english": "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.", "translation": "Fuerza de amortiguacion de cualquier movimiento del sistema, incluyendo influencias externas. Tambien reduce el exceso de movimiento (pasarse de largo).", "needs_translation": "false"}, "pid_1_P": {"english": "How tightly the system tracks the desired setpoint.", "translation": "Que tan estrechamente el sistema persigue el punto deseado.", "needs_translation": "false"}, "pid_0_I": {"english": "How tightly the system holds its position.", "translation": "Que tan estrechamente el sistema sostiene su posicion.", "needs_translation": "false"}, "pid_2_B": {"english": "Additional boost on the feedforward to make the heli react more to quick stick movements.", "translation": "Aumento adicional de avance para hacer que el heli reaccione mas rapidamente ante los movimientos del joystick.", "needs_translation": "false"}, "pid_0_O": {"english": "Used to prevent the craft from rolling when using high collective.", "translation": "Se utiliza para evitar que la aeronave se alabee cuando se utiliza colectivo alto.", "needs_translation": "false"}, "pid_0_F": {"english": "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.", "translation": "Ayuda a empujar el P-term en funcion de la posicion del joystick. Aumentarlo hara la respuesta mas fuerte, pero puede causar que se pase de largo.", "needs_translation": "false"}, "pid_2_F": {"english": "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.", "translation": "Ayuda a empujar el P-term en funcion de la posicion del joystick. Aumentarlo hara la respuesta mas fuerte, pero puede causar que se pase de largo.", "needs_translation": "false"}, "pid_2_D": {"english": "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.", "translation": "Fuerza de amortiguacion de cualquier movimiento del sistema, incluyendo influencias externas. Tambien reduce el exceso de movimiento (pasarse de largo).", "needs_translation": "false"}, "pid_0_P": {"english": "How tightly the system tracks the desired setpoint.", "translation": "Que tan estrechamente el sistema persigue el punto deseado.", "needs_translation": "false"}, "pid_1_D": {"english": "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.", "translation": "Fuerza de amortiguacion de cualquier movimiento del sistema, incluyendo influencias externas. Tambien reduce el exceso de movimiento (pasarse de largo).", "needs_translation": "false"}, "pid_0_B": {"english": "Additional boost on the feedforward to make the heli react more to quick stick movements.", "translation": "Aumento adicional de avance para hacer que el heli reaccione mas rapidamente ante los movimientos del joystick.", "needs_translation": "false"}, "pid_1_B": {"english": "Additional boost on the feedforward to make the heli react more to quick stick movements.", "translation": "Aumento adicional de avance para hacer que el heli reaccione mas rapidamente ante los movimientos del joystick.", "needs_translation": "false"}}, "GOVERNOR_CONFIG": {"tbl_govmode_standard": {"english": "STANDARD", "translation": "ESTANDAR", "needs_translation": "false"}, "tbl_govmode_mode2": {"english": "MODE2", "translation": "MODO2", "needs_translation": "false"}, "gov_tracking_time": {"english": "Time constant for headspeed changes, in seconds, measuring the time from zero to full headspeed.", "translation": "Constante de tiempo para cambios de velocidad del rotor, en segundos, midiendo desde cero hasta la velocidad maxima del rotor.", "needs_translation": "false"}, "tbl_govmode_passthrough": {"english": "PASSTHROUGH", "translation": "SALTEAR", "needs_translation": "false"}, "tbl_govmode_mode1": {"english": "MODE1", "translation": "MODO1", "needs_translation": "false"}, "gov_recovery_time": {"english": "Time constant for recovery spoolup, in seconds, measuring the time from zero to full headspeed.", "translation": "Constante de tiempo para aceleracion de recuperacion, en segundos, midiendo desde cero hasta la velocidad maxima del rotor.", "needs_translation": "false"}, "gov_startup_time": {"english": "Time constant for slow startup, in seconds, measuring the time from zero to full headspeed.", "translation": "Constante de tiempo para inicio lento, en segundos, midiendo desde cero hasta la velocidad maxima del rotor.", "needs_translation": "false"}, "gov_handover_throttle": {"english": "Governor activates above this %. Below this the input throttle is passed to the ESC.", "translation": "El governor se activa por encima de este %. Por debajo de este valor la posicion del acelerador se transfiere directamente al ESC.", "needs_translation": "false"}, "gov_spoolup_time": {"english": "Time constant for slow spoolup, in seconds, measuring the time from zero to full headspeed.", "translation": "Constante de tiempo para arranque lento, en segundos, midiendo desde cero hasta la velocidad maxima del rotor.", "needs_translation": "false"}, "gov_spoolup_min_throttle": {"english": "Minimum throttle to use for slow spoolup, in percent. For electric motors the default is 5%, for nitro this should be set so the clutch starts to engage for a smooth spoolup 10-15%.", "translation": "Posicion minima del acelerador para arranque lento, en porcentaje. Para motores electricos el valor por defecto es 5%, para nitro este valor deberia hacer que el embrague empiece a acoplarse para un arranque suave 10-15%.", "needs_translation": "false"}, "tbl_govmode_off": {"english": "OFF", "translation": "APAGADO", "needs_translation": "false"}}, "RC_CONFIG": {"rc_deflection": {"english": "Stick deflection from center in microseconds (us).", "translation": "Defleccion desde el centro del joystick en microsegundos (uS).", "needs_translation": "false"}, "rc_min_throttle": {"english": "Minimum throttle (0% throttle output) expected from radio, in microseconds (us).", "translation": "Acelerador minimo que se espera del radio (salida del acelerador 0%), en microsegundos (uS).", "needs_translation": "false"}, "rc_max_throttle": {"english": "Maximum throttle (100% throttle output) expected from radio, in microseconds (us).", "translation": "Acelerador maximo que se espera del radio (salida del acelerador 100%), en microsegundos (uS).", "needs_translation": "false"}, "rc_arm_throttle": {"english": "Throttle must be at or below this value in microseconds (us) to allow arming. Must be at least 10us lower than minimum throttle.", "translation": "El acelerador debe estar por debajo de este valor en microsegundos (uS) para permitir el armado. Debe ser al menos 10uS menor que el minimo del acelerador.", "needs_translation": "false"}, "rc_yaw_deadband": {"english": "Deadband for yaw control in microseconds (us).", "translation": "Zona muerta para control de direccion en microsegundos (uS).", "needs_translation": "false"}, "rc_deadband": {"english": "Deadband for cyclic control in microseconds (us).", "translation": "Zona muerta para control ciclico en microsegundos (uS).", "needs_translation": "false"}, "rc_center": {"english": "Stick center in microseconds (us).", "translation": "Centro del joystick en microsegundos (uS).", "needs_translation": "false"}}, "FILTER_CONFIG": {"gyro_soft_notch_cutoff_2": {"english": "Width of the notch filter in Hz.", "translation": "Ancho del filtro para el Punto en Hz.", "needs_translation": "false"}, "gyro_lpf1_static_hz": {"english": "Lowpass filter cutoff frequency in Hz.", "translation": "Frecuencia de corte del filtro pasabajos en Hz.", "needs_translation": "false"}, "tbl_none": {"english": "NONE", "translation": "NINGUNO", "needs_translation": "false"}, "dyn_notch_max_hz": {"english": "Maximum frequency to which the notch is applied.", "translation": "Frecuencia Maxima a la que el Punto es aplicado.", "needs_translation": "false"}, "tbl_1st": {"english": "1ST", "translation": "1RO", "needs_translation": "false"}, "rpm_min_hz": {"english": "Minimum frequency for the RPM filter.", "translation": "Frecuencia Minima para el filtro de RPM.", "needs_translation": "false"}, "dyn_notch_min_hz": {"english": "Minimum frequency to which the notch is applied.", "translation": "Frecuencia Minima a la que el Punto es aplicado.", "needs_translation": "false"}, "gyro_lpf1_dyn_max_hz": {"english": "Dynamic filter max cutoff in Hz.", "translation": "Corte max del filtro dinamico en Hz.", "needs_translation": "false"}, "gyro_soft_notch_hz_2": {"english": "Center frequency to which the notch is applied.", "translation": "Frecuencia central aplicada al punto.", "needs_translation": "false"}, "gyro_soft_notch_cutoff_1": {"english": "Width of the notch filter in Hz.", "translation": "Ancho del filtro para el Punto en Hz.", "needs_translation": "false"}, "gyro_soft_notch_hz_1": {"english": "Center frequency to which the notch is applied.", "translation": "Frecuencia central aplicada al Punto.", "needs_translation": "false"}, "dyn_notch_count": {"english": "Number of notches to apply.", "translation": "Numero de Puntos a aplicar.", "needs_translation": "false"}, "dyn_notch_q": {"english": "Quality factor of the notch filter.", "translation": "Factor de Calidad del Filtro de Puntos.", "needs_translation": "false"}, "gyro_lpf2_static_hz": {"english": "Lowpass filter cutoff frequency in Hz.", "translation": "Frecuencia de corte del filtro pasabajos en Hz.", "needs_translation": "false"}, "gyro_lpf1_dyn_min_hz": {"english": "Dynamic filter min cutoff in Hz.", "translation": "Corte min del filtro dinamico en Hz.", "needs_translation": "false"}, "tbl_2nd": {"english": "2ND", "translation": "2DO", "needs_translation": "false"}, "tbl_custom": {"english": "CUSTOM", "translation": "PERSONALIZADA", "needs_translation": "false"}, "tbl_low": {"english": "LOW", "translation": "BAJA", "needs_translation": "false"}, "tbl_medium": {"english": "MEDIUM", "translation": "MEDIA", "needs_translation": "false"}, "tbl_high": {"english": "HIGH", "translation": "ALTA", "needs_translation": "false"}}, "ESC_PARAMETERS_XDFLY": {"tbl_jadegreen": {"english": "JADE GREEN", "translation": "VERDE JADE", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_low": {"english": "Low", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_orange": {"english": "ORANGE", "translation": "NARANJA", "needs_translation": "false"}, "tbl_fmfw": {"english": "Fixed Wing", "translation": "Ala Fi<PERSON>", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "AntiH.", "needs_translation": "false"}, "tbl_medium": {"english": "Medium", "translation": "Medio", "needs_translation": "false"}, "tbl_yellow": {"english": "YELLOW", "translation": "AMARILLO", "needs_translation": "false"}, "tbl_reverse": {"english": "Reverse", "translation": "Revers<PERSON>", "needs_translation": "false"}, "tbl_red": {"english": "Red", "translation": "ROJO", "needs_translation": "false"}, "tbl_high": {"english": "High", "translation": "Alto", "needs_translation": "false"}, "tbl_auto": {"english": "Auto", "translation": "Auto", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_fmheli": {"english": "Helicopter", "translation": "Helicoptero", "needs_translation": "false"}, "tbl_purple": {"english": "PURPLE", "translation": "PURPURA", "needs_translation": "false"}, "tbl_green": {"english": "GREEN", "translation": "VERDE", "needs_translation": "false"}, "tbl_blue": {"english": "BLUE", "translation": "AZUL", "needs_translation": "false"}, "tbl_slow": {"english": "Slow", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_normal": {"english": "Normal", "translation": "Normal", "needs_translation": "false"}, "tbl_fast": {"english": "Fast", "translation": "Rapido", "needs_translation": "false"}, "tbl_escgov": {"english": "ESC Governor", "translation": "Governor <PERSON><PERSON>", "needs_translation": "false"}, "tbl_white": {"english": "WHITE", "translation": "BLANCO", "needs_translation": "false"}, "tbl_cyan": {"english": "CYAN", "translation": "CYAN", "needs_translation": "false"}, "tbl_vslow": {"english": "Very Slow", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_extgov": {"english": "External Governor", "translation": "Governor <PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_pink": {"english": "PINK", "translation": "ROSA", "needs_translation": "false"}, "tbl_fwgov": {"english": "Fixed Wing", "translation": "Ala Fi<PERSON>", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "Encendido", "needs_translation": "false"}}, "ESC_PARAMETERS_SCORPION": {"tbl_airplane": {"english": "Airplane mode", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_quad": {"english": "Quad mode", "translation": "Modo Quad", "needs_translation": "false"}, "tbl_heligov": {"english": "<PERSON><PERSON> Governor", "translation": "Governor <PERSON><PERSON>", "needs_translation": "false"}, "tbl_exbus": {"english": "Jeti Exbus", "translation": "Jeti Exbus", "needs_translation": "false"}, "tbl_boat": {"english": "Boat mode", "translation": "Modo <PERSON>", "needs_translation": "false"}, "tbl_unsolicited": {"english": "Unsolicited", "translation": "No Solicitado", "needs_translation": "false"}, "tbl_futsbus": {"english": "Futaba SBUS", "translation": "SBUS Futaba", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "AntiH.", "needs_translation": "false"}, "tbl_helistore": {"english": "<PERSON>li Governor (stored)", "translation": "Governor <PERSON><PERSON> (guardado)", "needs_translation": "false"}, "tbl_standard": {"english": "Standard", "translation": "E<PERSON>ar", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "Encendido", "needs_translation": "false"}, "tbl_vbar": {"english": "VBar", "translation": "VBar", "needs_translation": "false"}, "tbl_vbargov": {"english": "<PERSON><PERSON><PERSON> Governor", "translation": "Governor <PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_extgov": {"english": "External Governor", "translation": "Governor <PERSON><PERSON><PERSON>", "needs_translation": "false"}}}