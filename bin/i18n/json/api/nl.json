{"RC_TUNING": {"setpoint_boost_cutoff_2": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Boostdrempel voor het setpoint.", "needs_translation": "false"}, "response_time_3": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "Verhoog of verlaag de reactietijd van de rates om de helikopterbewegingen soepeler te maken.", "needs_translation": "false"}, "accel_limit_4": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Maximale versnelling van de heli als reactie op een stickbeweging.", "needs_translation": "false"}, "setpoint_boost_gain_4": {"english": "Boost gain for the setpoint.", "translation": "Boostversterking voor het setpoint.", "needs_translation": "false"}, "yaw_dynamic_deadband_filter": {"english": "The maximum filter applied to the yaw dynamic deadband.", "translation": "Het maximale filter toegepast op de dynamische yaw dode zone.", "needs_translation": "false"}, "setpoint_boost_gain_3": {"english": "Boost gain for the setpoint.", "translation": "Boostversterking voor het setpoint.", "needs_translation": "false"}, "response_time_2": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "Verhoog of verlaag de reactietijd van de rates om de helikopterbewegingen soepeler te maken.", "needs_translation": "false"}, "accel_limit_1": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Maximale versnelling van de heli als reactie op een stickbeweging.", "needs_translation": "false"}, "setpoint_boost_cutoff_1": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Boostdrempel voor het setpoint.", "needs_translation": "false"}, "setpoint_boost_cutoff_4": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Boostdrempel voor het setpoint.", "needs_translation": "false"}, "setpoint_boost_gain_2": {"english": "Boost gain for the setpoint.", "translation": "Boostversterking voor het setpoint.", "needs_translation": "false"}, "accel_limit_2": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Maximale versnelling van de heli als reactie op een stickbeweging.", "needs_translation": "false"}, "yaw_dynamic_deadband_gain": {"english": "The maximum gain applied to the yaw dynamic deadband.", "translation": "De maximale gain toegepast op de dynamische yaw dode zone.", "needs_translation": "false"}, "accel_limit_3": {"english": "Maximum acceleration of the craft in response to a stick movement.", "translation": "Maximale versnelling van de heli als reactie op een stickbeweging.", "needs_translation": "false"}, "response_time_4": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "Verhoog of verlaag de reactietijd van de rates om de helikopterbewegingen soepeler te maken.", "needs_translation": "false"}, "setpoint_boost_cutoff_3": {"english": "<PERSON>ost cutoff for the setpoint.", "translation": "Boostdrempel voor het setpoint.", "needs_translation": "false"}, "setpoint_boost_gain_1": {"english": "Boost gain for the setpoint.", "translation": "Boostversterking voor het setpoint.", "needs_translation": "false"}, "yaw_dynamic_ceiling_gain": {"english": "The maximum gain applied to the yaw dynamic ceiling.", "translation": "De maximale gain toegepast op het dynamische yaw plafond.", "needs_translation": "false"}, "response_time_1": {"english": "Increase or decrease the response time of the rate to smooth heli movements.", "translation": "Verhoog of verlaag de reactietijd van de rates om de helikopterbewegingen soepeler te maken.", "needs_translation": "false"}}, "RESCUE_PROFILE": {"rescue_flip_gain": {"english": "Determine how aggressively the heli flips during inverted rescue.", "translation": "<PERSON><PERSON><PERSON> hoe agressief de heli flips bij een inverted rescue.", "needs_translation": "false"}, "rescue_level_gain": {"english": "Determine how aggressively the heli levels during rescue.", "translation": "<PERSON><PERSON><PERSON> hoe agressief de heli levels tijdens rescue.", "needs_translation": "false"}, "tbl_off": {"english": "OFF", "translation": "UIT", "needs_translation": "false"}, "rescue_hover_collective": {"english": "Collective value for hover.", "translation": "Collective waarde voor hoover", "needs_translation": "false"}, "rescue_max_setpoint_rate": {"english": "Limit rescue roll/pitch rate. Larger helicopters may need slower rotation rates.", "translation": "Limiet de rescue roll/pitch rotatiesnelheid. <PERSON><PERSON> he<PERSON>'s hebben lagere rotatiesnelheden benodigd.", "needs_translation": "false"}, "tbl_flip": {"english": "FLIP", "translation": "FLIP", "needs_translation": "false"}, "rescue_flip_mode": {"english": "If rescue is activated while inverted, flip to upright - or remain inverted.", "translation": "Als de rescue wordt geactiveerd ter<PERSON>l inverted, flip positief - of blijf inverted.", "needs_translation": "false"}, "rescue_pull_up_time": {"english": "When rescue is activated, helicopter will apply pull-up collective for this time period before moving to flip or climb stage.", "translation": "Wanneer rescue is gea<PERSON><PERSON>, helicopter geeft collective omhoog voor deze tijd voordat heli flipt of overgaat naar klim stap.", "needs_translation": "false"}, "tbl_noflip": {"english": "NO FLIP", "translation": "GEEN FLIP", "needs_translation": "false"}, "rescue_exit_time": {"english": "This limits rapid application of negative collective if the helicopter has rolled during rescue.", "translation": "Dit beperkt de snelle toepassing van negatieve collective als de helikopter tijdens de reddingsactie is gerold.", "needs_translation": "false"}, "rescue_pull_up_collective": {"english": "Collective value for pull-up climb.", "translation": "Collective waarde voor omhoog klimmen.", "needs_translation": "false"}, "rescue_max_setpoint_accel": {"english": "Limit how fast the helicopter accelerates into a roll/pitch. Larger helicopters may need slower acceleration.", "translation": "Limiet de acceleratie in een roll/pitch. <PERSON><PERSON><PERSON> he<PERSON>'s hebben een lagere acceleratie nodig.", "needs_translation": "false"}, "tbl_on": {"english": "ON", "translation": "AAN", "needs_translation": "false"}, "rescue_climb_collective": {"english": "Collective value for rescue climb.", "translation": "Collective waarde voor rescue klim.", "needs_translation": "false"}, "rescue_flip_time": {"english": "If the helicopter is in rescue and is trying to flip to upright and does not within this time, rescue will be aborted.", "translation": " <PERSON><PERSON> is in rescue en probeert te flippen wat niet binnen deze tijd lukt dan wordt rescue afgebroken.", "needs_translation": "false"}, "rescue_climb_time": {"english": "Length of time the climb collective is applied before switching to hover.", "translation": "Tijdsduur de klim collective wordt gegeven voor omgeschakeld wordt naar hover.", "needs_translation": "false"}}, "ESC_SENSOR_CONFIG": {"hw4_current_offset": {"english": "Hobbywing v4 current offset adjustment", "translation": "Hobbywing v4 stroom afwijking instelling", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "Uit", "needs_translation": "false"}, "update_hz": {"english": "ESC telemetry update rate", "translation": "ESC telemetrie update rate", "needs_translation": "false"}, "half_duplex": {"english": "Half duplex mode for ESC telemetry", "translation": "Half duplex mode voor ESC telemetry", "needs_translation": "false"}, "consumption_correction": {"english": "Adjust the consumption correction", "translation": "Pas het verbruik correctie aan", "needs_translation": "false"}, "current_offset": {"english": "Current sensor offset adjustment", "translation": "Stroomsensor afwijking instelling", "needs_translation": "false"}, "voltage_correction": {"english": "Adjust the voltage correction", "translation": "Pas de voltage correctie aan", "needs_translation": "false"}, "hw4_voltage_gain": {"english": "Hobbywing v4 voltage gain adjustment", "translation": "Hobbywing v4 voltage gain instelling", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "hw4_current_gain": {"english": "Hobbywing v4 current gain adjustment", "translation": "Hobbywing v4 stroom gain instelling", "needs_translation": "false"}, "current_correction": {"english": "Adjust current correction", "translation": "Pas de stroom correctie aan", "needs_translation": "false"}, "pin_swap": {"english": "Swap the TX and RX pins for the ESC telemetry", "translation": "Verwissel de TX en RX pins voor de esc telemetrie", "needs_translation": "false"}}, "ESC_PARAMETERS_FLYROTOR": {"tbl_alwayson": {"english": "Always On", "translation": "<PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "throttle_min": {"english": "Minimum throttle value", "translation": "Minimale throttle waarde", "needs_translation": "false"}, "tbl_disabled": {"english": "Disabled", "translation": "Uit", "needs_translation": "false"}, "tbl_auto": {"english": "Auto", "translation": "Auto", "needs_translation": "false"}, "starting_torque": {"english": "Starting torque for the motor", "translation": "Start koppel voor de motor", "needs_translation": "false"}, "cell_count": {"english": "Number of cells in the battery", "translation": "Aantal cellen in de batterij", "needs_translation": "false"}, "motor_erpm_max": {"english": "Maximum RPM", "translation": "Maximale RPM", "needs_translation": "false"}, "throttle_max": {"english": "Maximum throttle value", "translation": "Maximale throttle waarde", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "tbl_escgov": {"english": "Esc Governor", "translation": "Esc Governor", "needs_translation": "false"}, "temperature_protection": {"english": "Temperature at which we cut power by 50%", "translation": "Temperatuur waarbij vermogen wordt verlaagd met 50%", "needs_translation": "false"}, "tbl_automatic": {"english": "Automatic", "translation": "Automatisch", "needs_translation": "false"}, "low_voltage_protection": {"english": "Voltage at which we cut power by 50%", "translation": "Voltage waarbij vermogen wordt verlaagd met 50%", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "soft_start": {"english": "Soft start value", "translation": "Soft start waarde", "needs_translation": "false"}, "gov_i": {"english": "Integral value for the governor", "translation": "Integral gain voor de governor", "needs_translation": "false"}, "timing_angle": {"english": "Timing angle for the motor", "translation": "Timing voor de motor", "needs_translation": "false"}, "response_speed": {"english": "Response speed for the motor", "translation": "Respons snelheid voor de motor", "needs_translation": "false"}, "current_gain": {"english": "Gain value for the current sensor", "translation": "Gain waarde voor stroomsensor", "needs_translation": "false"}, "tbl_extgov": {"english": "External Governor", "translation": "Externe Governor", "needs_translation": "false"}, "buzzer_volume": {"english": "Buzzer volume", "translation": "Buzzer volume", "needs_translation": "false"}, "gov_d": {"english": "Derivative value for the governor", "translation": "Derivative gain voor de governor", "needs_translation": "false"}, "tbl_enabled": {"english": "Enabled", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "gov_p": {"english": "Proportional value for the governor", "translation": "Proportional gain voor de governor", "needs_translation": "false"}}, "ESC_PARAMETERS_YGE": {"tbl_alwayson": {"english": "Always On", "translation": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "Uit", "needs_translation": "false"}, "tbl_modestore": {"english": "Heli Governor Store", "translation": "Heli Governor Store", "needs_translation": "false"}, "tbl_modefree": {"english": "Free (Attention!)", "translation": "Free (Attention!)", "needs_translation": "false"}, "tbl_modeglider": {"english": "Aero Glider", "translation": "Aero Glider", "needs_translation": "false"}, "tbl_modeext": {"english": "<PERSON><PERSON>t Governor", "translation": "<PERSON><PERSON>t Governor", "needs_translation": "false"}, "tbl_modeheli": {"english": "<PERSON><PERSON> Governor", "translation": "<PERSON><PERSON> Governor", "needs_translation": "false"}, "tbl_medium": {"english": "Medium", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_autonorm": {"english": "Auto Normal", "translation": "Auto Normaal", "needs_translation": "false"}, "tbl_reverse": {"english": "Reverse", "translation": "Omgekeerd", "needs_translation": "false"}, "tbl_modef3a": {"english": "Aero F3A", "translation": "Aero F3A", "needs_translation": "false"}, "tbl_auto": {"english": "Auto", "translation": "Auto", "needs_translation": "false"}, "tbl_slowdown": {"english": "Slowdown", "translation": "Vertraging", "needs_translation": "false"}, "tbl_slow": {"english": "Slow", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_modeair": {"english": "Aero Motor", "translation": "Aero Motor", "needs_translation": "false"}, "tbl_normal": {"english": "Normal", "translation": "Normaal", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_autoextreme": {"english": "Auto Extreme", "translation": "Auto Extreme", "needs_translation": "false"}, "tbl_autoefficient": {"english": "Auto Efficient", "translation": "Auto Efficient", "needs_translation": "false"}, "tbl_smooth": {"english": "Smooth", "translation": "Smooth", "needs_translation": "false"}, "tbl_fast": {"english": "Fast", "translation": "Snel", "needs_translation": "false"}, "tbl_custom": {"english": "Custom (PC Defined)", "translation": "Custom (PC Defined)", "needs_translation": "false"}, "tbl_cutoff": {"english": "Cutoff", "translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_autopower": {"english": "Auto Power", "translation": "Auto Power", "needs_translation": "false"}, "tbl_unused": {"english": "*Unused*", "translation": "*Unused*", "needs_translation": "false"}}, "GOVERNOR_PROFILE": {"governor_tta_gain": {"english": "TTA gain applied to increase headspeed to control the tail in the negative direction (e.g. motorised tail less than idle speed).", "translation": "TTA gain toegepast om rotortoerental te verhogen om de staart in negatieve richting te sturen (e.g. gemotoriseerde staart onder idle rpm).", "needs_translation": "false"}, "governor_collective_ff_weight": {"english": "Collective precompensation weight - how much collective is mixed into the feedforward.", "translation": "Collective precompensation weight - how much collective is mixed into the feedforward.", "needs_translation": "false"}, "governor_i_gain": {"english": "PID loop I-term gain.", "translation": "PID loop I-term gain.", "needs_translation": "false"}, "governor_cyclic_ff_weight": {"english": "Cyclic precompensation weight - how much cyclic is mixed into the feedforward.", "translation": "Gewicht Cyclic precompensation - Hoeveel cyclic is mixed in de feedforward.", "needs_translation": "false"}, "governor_f_gain": {"english": "Feedforward gain.", "translation": "Feedforward gain.", "needs_translation": "false"}, "governor_gain": {"english": "Master PID loop gain.", "translation": "Master PID loop gain.", "needs_translation": "false"}, "governor_headspeed": {"english": "Target headspeed for the current profile.", "translation": "<PERSON>el rotortoerental voor het huidige profiel.", "needs_translation": "false"}, "governor_min_throttle": {"english": "Minimum output throttle the governor is allowed to use.", "translation": "Minimum throttle de governor mag g<PERSON><PERSON><PERSON>n.", "needs_translation": "false"}, "governor_d_gain": {"english": "PID loop D-term gain.", "translation": "PID loop D-term gain.", "needs_translation": "false"}, "governor_p_gain": {"english": "PID loop P-term gain.", "translation": "PID loop P-term gain.", "needs_translation": "false"}, "governor_yaw_ff_weight": {"english": "Yaw precompensation weight - how much yaw is mixed into the feedforward.", "translation": "Gewicht voor yaw precompensation - Hoeveel yaw is mixed in de feedforward.", "needs_translation": "false"}, "governor_max_throttle": {"english": "Maximum output throttle the governor is allowed to use.", "translation": "Maximale throttle welke de governor mag geb<PERSON>iken.", "needs_translation": "false"}, "governor_tta_limit": {"english": "TTA max headspeed increase over full headspeed.", "translation": "TTA max rotortoerental boven volledige rotortoerental.", "needs_translation": "false"}}, "PID_PROFILE": {"bterm_cutoff_2": {"english": "B-term cutoff in Hz.", "translation": "B-term afsnijding in Hz.", "needs_translation": "false"}, "dterm_cutoff_1": {"english": "D-term cutoff in Hz.", "translation": "D-term-afsnijding in Hz.", "needs_translation": "false"}, "bterm_cutoff_1": {"english": "B-term cutoff in Hz.", "translation": "B-term afsnijding in Hz.", "needs_translation": "false"}, "gyro_cutoff_1": {"english": "PID loop overall bandwidth in Hz.", "translation": "Totale bandbreedte van de PID-loop in Hz.", "needs_translation": "false"}, "tbl_on": {"english": "ON", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "dterm_cutoff_2": {"english": "D-term cutoff in Hz.", "translation": "D-term-afsnijding in Hz.", "needs_translation": "false"}, "yaw_inertia_precomp_cutoff": {"english": "Cutoff. Derivative cutoff frequency in 1/10Hz steps. Controls how sharp the precomp is. Higher value is sharper.", "translation": "Afsnijding. Afgeleide cutoff-frequentie in stappen van 1/10 Hz. Bepa<PERSON>t hoe scherp de precomp is. Ho<PERSON>e waarde is scherper.", "needs_translation": "false"}, "offset_limit_0": {"english": "Hard limit for the High Speed Integral offset angle in the PID loop. The O-term will never go over these limits.", "translation": "Harde limiet voor de High Speed ​​Integral offset in de PID-loop. De O-term zal nooit over deze limieten heen gaan.", "needs_translation": "false"}, "cyclic_cross_coupling_ratio": {"english": "Amount of roll-to-pitch compensation needed, vs. pitch-to-roll.", "translation": "Hoeveelheid benodigde rol-to-pitch compensatie, versus pitch-roll.", "needs_translation": "false"}, "yaw_precomp_cutoff": {"english": "Frequency limit for all yaw precompensation actions.", "translation": "Frequentie limiet voor alle yaw precompensation acties.", "needs_translation": "false"}, "error_limit_0": {"english": "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.", "translation": "Harde limiet voor de hoekfout in de PID-loop. De absolute fout en dus de I-term zal nooit boven deze limieten uitkomen.", "needs_translation": "false"}, "trainer_gain": {"english": "Determines how aggressively the helicopter tilts back to the maximum angle (if exceeded) while in Acro Trainer Mode.", "translation": "<PERSON><PERSON><PERSON>t hoe agressief de helikopter terugkantelt naar de maximale hoek (indien deze wordt overschreden) in de Acro Trainer-modus.", "needs_translation": "false"}, "tbl_rpy": {"english": "RPY", "translation": "RPY", "needs_translation": "false"}, "gyro_cutoff_2": {"english": "PID loop overall bandwidth in Hz.", "translation": "Totale bandbreedte van de PID-loop in Hz.", "needs_translation": "false"}, "yaw_ccw_stop_gain": {"english": "Stop gain (PD) for counter-clockwise rotation.", "translation": "Stop gain (PD) voor tegen de klok in rotatie.", "needs_translation": "false"}, "trainer_angle_limit": {"english": "Limit the maximum angle the helicopter will pitch/roll to while in Acro Trainer Mode.", "translation": "Beperk de maximale hoek waarin de helikopter kan rollen/hellen in de Acro Trainer-modus.", "needs_translation": "false"}, "tbl_rp": {"english": "RP", "translation": "RP", "needs_translation": "false"}, "yaw_cyclic_ff_gain": {"english": "Cyclic feedforward mixed into yaw (cyclic-to-yaw precomp).", "translation": "Cyclic feedforward mixed in de yaw (cyclic-to-yaw precomp).", "needs_translation": "false"}, "error_decay_time_cyclic": {"english": "Time constant for bleeding off cyclic I-term. Higher will stabilize hover, lower will drift.", "translation": "Tijdconstante voor het afkappen van cyclische I-term. Hoger stabiliseert de hover, lager zal driften.", "needs_translation": "false"}, "error_decay_limit_cyclic": {"english": "Maximum bleed-off speed for cyclic I-term.", "translation": "Maximale afkap snelheid voor cyclische I-term.", "needs_translation": "false"}, "cyclic_cross_coupling_gain": {"english": "Amount of compensation applied for pitch-to-roll decoupling.", "translation": "Hoeveelheid toegepaste compensatie voor pitch-to-roll-koppeling gain.", "needs_translation": "false"}, "yaw_collective_dynamic_decay": {"english": "Decay time for the extra yaw precomp on collective input.", "translation": "Vervaltijd voor de extra yaw-precomp op collectieve invoer.", "needs_translation": "false"}, "pitch_collective_ff_gain": {"english": "Increasing will compensate for the pitching up motion caused by tail drag when climbing.", "translation": "Door de snelheid te verhogen, compenseert u de opwaartse beweging die ontstaat door de staartweerstand tijdens het klimmen.", "needs_translation": "false"}, "iterm_relax_type": {"english": "Choose the axes in which this is active. RP: Roll, Pitch. RPY: Roll, Pitch, Yaw.", "translation": "Kies de assen waarin dit actief is. RP: Roll, Pitch. RPY: Roll, Pitch, Yaw.", "needs_translation": "false"}, "offset_limit_1": {"english": "Hard limit for the High Speed Integral offset angle in the PID loop. The O-term will never go over these limits.", "translation": "Harde limiet voor de High Speed ​​Integral offset in de PID-loop. De O-term zal nooit over deze limieten heen gaan.", "needs_translation": "false"}, "iterm_relax_cutoff_1": {"english": "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.", "translation": "Helpt bounce back te verminderen na snelle stickbewegingen. Kan <PERSON>ie veroorzaken in kleine stickbewegingen als deze te laag is.", "needs_translation": "false"}, "error_limit_1": {"english": "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.", "translation": "Harde limiet voor de hoekfout in de PID-loop. De absolute fout en dus de I-term zal nooit boven deze limieten uitkomen.", "needs_translation": "false"}, "horizon_level_strength": {"english": "Determines how aggressively the helicopter tilts back to level while in Horizon Mode.", "translation": "Be<PERSON>alt hoe agressief de helikopter terugkantelt naar horizontaal in de Horizonmodus.", "needs_translation": "false"}, "error_limit_2": {"english": "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.", "translation": "Harde limiet voor de hoekfout in de PID-loop. De absolute fout en dus de I-term zal nooit boven deze limieten uitkomen.", "needs_translation": "false"}, "iterm_relax_cutoff_2": {"english": "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.", "translation": "Helpt bounce back te verminderen na snelle stickbewegingen. Kan <PERSON>ie veroorzaken in kleine stickbewegingen als deze te laag is.", "needs_translation": "false"}, "tbl_off": {"english": "OFF", "translation": "Uit", "needs_translation": "false"}, "yaw_collective_ff_gain": {"english": "Collective feedforward mixed into yaw (collective-to-yaw precomp).", "translation": "Collective feedforward mixed in de yaw (collective-to-yaw precomp).", "needs_translation": "false"}, "gyro_cutoff_0": {"english": "PID loop overall bandwidth in Hz.", "translation": "Totale bandbreedte van de PID-loop in Hz.", "needs_translation": "false"}, "yaw_collective_dynamic_gain": {"english": "An extra boost of yaw precomp on collective input.", "translation": "Een extra boost van yaw precomp met collective input.", "needs_translation": "false"}, "cyclic_cross_coupling_cutoff": {"english": "Frequency limit for the compensation. Higher value will make the compensation action faster.", "translation": "Frequentielimiet voor de compensatie. Hogere waarde zal de compensatieactie sneller maken.", "needs_translation": "false"}, "error_rotation": {"english": "Rotates the current roll and pitch error terms around yaw when the craft rotates. This is sometimes called Piro Compensation.", "translation": "Draait de huidige rol- en pitch-fout termen rond de yaw wanneer het toestel roteert. Dit wordt soms Piro-compensatie genoemd.", "needs_translation": "false"}, "angle_level_limit": {"english": "Limit the maximum angle the helicopter will pitch/roll to while in Angle mode.", "translation": "Beperk de maximale hoek waarin de helikopter kan kantelen/rollen in de anglemodus.", "needs_translation": "false"}, "yaw_cw_stop_gain": {"english": "Stop gain (PD) for clockwise rotation.", "translation": "Stop gain (PD) voor klok mee rotatie.", "needs_translation": "false"}, "iterm_relax_cutoff_0": {"english": "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.", "translation": "Helpt bounce back te verminderen na snelle stickbewegingen. Kan <PERSON>ie veroorzaken in kleine stickbewegingen als deze te laag is.", "needs_translation": "false"}, "yaw_inertia_precomp_gain": {"english": "Scalar gain. The strength of the main rotor inertia. Higher value means more precomp is applied to yaw control.", "translation": "Scalar gain. De sterkte van de main rotor inertia. Hogere waarde betekend meer precomp wordt toegepast op yaw besturing.", "needs_translation": "false"}, "dterm_cutoff_0": {"english": "D-term cutoff in Hz.", "translation": "D-term-afsnijding in Hz.", "needs_translation": "false"}, "angle_level_strength": {"english": "Determines how aggressively the helicopter tilts back to level while in Angle Mode.", "translation": "Be<PERSON>alt hoe agressief de helikopter terug kantelt naar horizontaal in de anglemodus.", "needs_translation": "false"}, "bterm_cutoff_0": {"english": "B-term cutoff in Hz.", "translation": "B-term afsnijding in Hz.", "needs_translation": "false"}, "error_decay_time_ground": {"english": "Bleeds off the current controller error when the craft is not airborne to stop the craft tipping over.", "translation": "Verwijdert de controllerfout wanneer het toestel niet in de lucht is, om te voorkomen dat het toestel omvalt.", "needs_translation": "false"}}, "MIXER_CONFIG": {"swash_trim_1": {"english": "Swash trim to level the swash plate when using fixed links.", "translation": "Trim voor de tuimelschijf koppelingen. Alleen gebruiken wanneer de koppeling niet verstel<PERSON> is.", "needs_translation": "false"}, "tail_motor_idle": {"english": "Minimum throttle signal sent to the tail motor. This should be set just high enough that the motor does not stop.", "translation": "Minimale throttle signaal welke wordt gestuurd naar de staartmotor. Precies hoog genoeg zodat de staartmotor niet stil staat", "needs_translation": "false"}, "tail_center_trim": {"english": "Sets tail rotor trim for 0 yaw for variable pitch, or tail motor throttle for 0 yaw for motorized.", "translation": "Stel de trim van de staartrotor in om 0 graden bladhoek te bereiken, of staartmotor throttle voor 0 yaw voor gemotoriseerde staart.", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "swash_tta_precomp": {"english": "Mixer precomp for 0 yaw.", "translation": "Mixer precomp voor 0 yaw", "needs_translation": "false"}, "swash_trim_2": {"english": "Swash trim to level the swash plate when using fixed links.", "translation": "Trim voor de tuimelschijf koppelingen. Alleen gebruiken wanneer de koppeling niet verstel<PERSON> is.", "needs_translation": "false"}, "swash_geo_correction": {"english": "Adjust if there is too much negative collective or too much positive collective.", "translation": "Past de positieve en negatieve collective aan totdat deze gelijk zijn.", "needs_translation": "false"}, "swash_trim_0": {"english": "Swash trim to level the swash plate when using fixed links.", "translation": "Trim voor de tuimelschijf koppelingen. Alleen gebruiken wanneer de koppeling niet verstel<PERSON> is.", "needs_translation": "false"}, "swash_phase": {"english": "Phase offset for the swashplate controls.", "translation": "De fase verschuiving voor de tuimelschijf.", "needs_translation": "false"}, "collective_tilt_correction_pos": {"english": "Adjust the collective tilt correction scaling for positive collective pitch.", "translation": "Past de collective schaal correctie aan voor positive collective pitch.", "needs_translation": "false"}, "collective_tilt_correction_neg": {"english": "Adjust the collective tilt correction scaling for negative collective pitch.", "translation": "Past de collective schaal correctie aan voor negatieve collective pitch.", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "swash_pitch_limit": {"english": "Maximum amount of combined cyclic and collective blade pitch.", "translation": "Maximale cyclische en collective rotorbladhoek.", "needs_translation": "false"}}, "MOTOR_CONFIG": {"minthrottle": {"english": "This PWM value is sent to the ESC/Servo at low throttle", "translation": "Deze PWM waarde wordt gestuurd naar de ESC/Servo op laag throttle", "needs_translation": "false"}, "motor_pwm_protocol": {"english": "The protocol used to communicate with the ESC", "translation": "Het protocol wat gebruikt wordt om met de esc te communiceren", "needs_translation": "false"}, "main_rotor_gear_ratio_0": {"english": "Motor Pinion Gear Tooth Count", "translation": "Motor tandwiel aantal tanden", "needs_translation": "false"}, "maxthrottle": {"english": "This PWM value is sent to the ESC/Servo at full throttle", "translation": "Deze PWM waarde wordt gestuurd naar de ESC/Servo op volledig throttle", "needs_translation": "false"}, "mincommand": {"english": "This PWM value is sent when the motor is stopped", "translation": "Deze PWM waarde wordt gestuurd wanneer de motor gestopt is", "needs_translation": "false"}, "main_rotor_gear_ratio_1": {"english": "Main Gear Tooth Count", "translation": "Hoofd tand<PERSON>el aantal tanden", "needs_translation": "false"}, "tail_rotor_gear_ratio_1": {"english": "Autorotation Gear Tooth Count", "translation": "Autorotatie tand<PERSON><PERSON> aantal tanden", "needs_translation": "false"}, "motor_pwm_rate": {"english": "The frequency at which the ESC sends PWM signals to the motor", "translation": "De <PERSON>ie snelheid waarmee het throttle signaal wordt gestuurd naar de esc", "needs_translation": "false"}, "tail_rotor_gear_ratio_0": {"english": "<PERSON><PERSON> <PERSON> Tooth Count", "translation": "<PERSON><PERSON><PERSON> tandwiel aantal tanden", "needs_translation": "false"}, "motor_pole_count_0": {"english": "The number of magnets on the motor bell.", "translation": "Het aantal magneten in de motor.", "needs_translation": "false"}}, "ESC_PARAMETERS_HW5": {"tbl_cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "tbl_fixedwing": {"english": "Fixed Wing", "translation": "Fixed Wing", "needs_translation": "false"}, "tbl_disabled": {"english": "Disabled", "translation": "Uit", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "tbl_heligov": {"english": "<PERSON><PERSON> Governor", "translation": "<PERSON><PERSON> Governor", "needs_translation": "false"}, "tbl_softcutoff": {"english": "Soft Cutoff", "translation": "Soft Cutoff", "needs_translation": "false"}, "tbl_proportional": {"english": "Proportional", "translation": "Proportional", "needs_translation": "false"}, "tbl_heliext": {"english": "<PERSON><PERSON>t Governor", "translation": "<PERSON><PERSON>t Governor", "needs_translation": "false"}, "tbl_helistore": {"english": "Heli Governor Store", "translation": "Heli Governor Store", "needs_translation": "false"}, "tbl_hardcutoff": {"english": "Hard Cutoff", "translation": "Hard Cutoff", "needs_translation": "false"}, "tbl_normal": {"english": "Normal", "translation": "Normaal", "needs_translation": "false"}, "tbl_autocalculate": {"english": "Auto Calculate", "translation": "Auto Calculate", "needs_translation": "false"}, "tbl_enabled": {"english": "Enabled", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_reverse": {"english": "Reverse", "translation": "Omgekeerd", "needs_translation": "false"}}, "PILOT_CONFIG": {"model_param1_value": {"english": "Set this to the expected flight time in seconds.  The transmitter will beep when the flight time is reached.", "translation": "<PERSON><PERSON> deze in op de verwachtte flight tijd in seconds. De zender zal piepen als de tijd bereikt is.", "needs_translation": "false"}}, "BATTERY_INI": {"calcfuel_local": {"english": "Calculate Fuel Using", "translation": "Calculate Fuel Using", "needs_translation": "true"}, "tbl_off": {"english": "Current Sensor", "translation": "Current Sensor", "needs_translation": "true"}, "tbl_on": {"english": "Voltage Sensor", "translation": "Voltage Sensor", "needs_translation": "true"}, "sag_multiplier": {"english": "Raise or lower to adjust for the amount of voltage sag you see in flight.", "translation": "Raise or lower to adjust for the amount of voltage sag you see in flight.", "needs_translation": "true"}, "kalman_multiplier": {"english": "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.", "translation": "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.", "needs_translation": "true"}, "alert_off": {"english": "Off", "translation": "Off", "needs_translation": "true"}, "alert_bec": {"english": "BEC", "translation": "BEC", "needs_translation": "true"}, "alert_rxbatt": {"english": "RxBatt", "translation": "<PERSON><PERSON>", "needs_translation": "true"}}, "BATTERY_CONFIG": {"vbatmincellvoltage": {"english": "The minimum voltage per cell before the low voltage alarm is triggered.", "translation": "Het minimale voltage wanneer laag voltage alarm wordt getriggerd.", "needs_translation": "false"}, "vbatmaxcellvoltage": {"english": "The maximum voltage per cell before the high voltage alarm is triggered.", "translation": "Het maximale voltage per cel wanneer hoog voltage alarm wordt getriggerd.", "needs_translation": "false"}, "vbatwarningcellvoltage": {"english": "The voltage per cell at which the low voltage alarm will start to sound.", "translation": "Het voltage per cel wanneer het laag voltage alarm af gaat.", "needs_translation": "false"}, "batteryCellCount": {"english": "The number of cells in your battery.", "translation": "Het aantal cellen in je batterij.", "needs_translation": "false"}, "vbatfullcellvoltage": {"english": "The nominal voltage of a fully charged cell.", "translation": "Het nominale voltage van een volledig geladen cel.", "needs_translation": "false"}, "batteryCapacity": {"english": "The milliamp hour capacity of your battery.", "translation": "<PERSON> capaciteit van de batter<PERSON>.", "needs_translation": "false"}}, "ACC_TRIM": {"pitch": {"english": "Use to trim if the heli drifts in one of the stabilized modes (angle, horizon, etc.).", "translation": "Gebruik de trim als de heli niet stil hangt in de stabilisatie modes (angle, horizon, etc.).", "needs_translation": "false"}, "roll": {"english": "Use to trim if the heli drifts in one of the stabilized modes (angle, horizon, etc.).", "translation": "Gebruik de trim als de heli niet stil hangt in de stabilisatie modes (angle, horizon, etc.).", "needs_translation": "false"}}, "PID_TUNING": {"pid_1_I": {"english": "How tightly the system holds its position.", "translation": "Hoe nauwkeurig het systeem zijn positie vasthoudt.", "needs_translation": "false"}, "pid_2_P": {"english": "How tightly the system tracks the desired setpoint.", "translation": "Hoe nauwkeurig het systeem de gewenste instelwaarde volgt.", "needs_translation": "false"}, "pid_2_I": {"english": "How tightly the system holds its position.", "translation": "Hoe nauwkeurig het systeem zijn positie vasthoudt.", "needs_translation": "false"}, "pid_1_O": {"english": "Used to prevent the craft from pitching when using high collective.", "translation": "Wordt gebruikt om te voorkomen dat de heli gaat rollen bij gebruik van veel collective.", "needs_translation": "false"}, "pid_1_F": {"english": "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.", "translation": "Helpt P-term te pushen op basis van stick input. Verhogen zal de respons scherper maken, maar kan overshoot veroorzaken.", "needs_translation": "false"}, "pid_0_D": {"english": "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.", "translation": "<PERSON><PERSON><PERSON><PERSON> van dem<PERSON> van elke beweging op het systeem, inclusief externe invloeden. <PERSON><PERSON><PERSON><PERSON> ook overshoot.", "needs_translation": "false"}, "pid_1_P": {"english": "How tightly the system tracks the desired setpoint.", "translation": "Hoe nauwkeurig het systeem de gewenste instelwaarde volgt.", "needs_translation": "false"}, "pid_0_I": {"english": "How tightly the system holds its position.", "translation": "Hoe nauwkeurig het systeem zijn positie vasthoudt.", "needs_translation": "false"}, "pid_2_B": {"english": "Additional boost on the feedforward to make the heli react more to quick stick movements.", "translation": "Extra boost op de feedforward zodat de heli beter reageert op snelle stickbewegingen.", "needs_translation": "false"}, "pid_0_O": {"english": "Used to prevent the craft from rolling when using high collective.", "translation": "Wordt gebruikt om te voorkomen dat de heli gaat rollen bij gebruik van veel collective.", "needs_translation": "false"}, "pid_0_F": {"english": "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.", "translation": "Helpt P-term te pushen op basis van stick input. Verhogen zal de respons scherper maken, maar kan overshoot veroorzaken.", "needs_translation": "false"}, "pid_2_F": {"english": "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.", "translation": "Helpt P-term te pushen op basis van stick input. Verhogen zal de respons scherper maken, maar kan overshoot veroorzaken.", "needs_translation": "false"}, "pid_2_D": {"english": "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.", "translation": "<PERSON><PERSON><PERSON><PERSON> van dem<PERSON> van elke beweging op het systeem, inclusief externe invloeden. <PERSON><PERSON><PERSON><PERSON> ook overshoot.", "needs_translation": "false"}, "pid_0_P": {"english": "How tightly the system tracks the desired setpoint.", "translation": "Hoe nauwkeurig het systeem de gewenste instelwaarde volgt.", "needs_translation": "false"}, "pid_1_D": {"english": "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.", "translation": "<PERSON><PERSON><PERSON><PERSON> van dem<PERSON> van elke beweging op het systeem, inclusief externe invloeden. <PERSON><PERSON><PERSON><PERSON> ook overshoot.", "needs_translation": "false"}, "pid_0_B": {"english": "Additional boost on the feedforward to make the heli react more to quick stick movements.", "translation": "Extra boost op de feedforward zodat de heli beter reageert op snelle stickbewegingen.", "needs_translation": "false"}, "pid_1_B": {"english": "Additional boost on the feedforward to make the heli react more to quick stick movements.", "translation": "Extra boost op de feedforward zodat de heli beter reageert op snelle stickbewegingen.", "needs_translation": "false"}}, "GOVERNOR_CONFIG": {"tbl_govmode_standard": {"english": "STANDARD", "translation": "STANDAARD", "needs_translation": "false"}, "tbl_govmode_mode2": {"english": "MODE2", "translation": "MODE2", "needs_translation": "false"}, "gov_tracking_time": {"english": "Time constant for headspeed changes, in seconds, measuring the time from zero to full headspeed.", "translation": "Tijdconstante voor rotortoerental veranderingen in seconden, van nul tot volledig rotortoerental.", "needs_translation": "false"}, "tbl_govmode_passthrough": {"english": "PASSTHROUGH", "translation": "PASSTHROUGH", "needs_translation": "false"}, "tbl_govmode_mode1": {"english": "MODE1", "translation": "MODE1", "needs_translation": "false"}, "gov_recovery_time": {"english": "Time constant for recovery spoolup, in seconds, measuring the time from zero to full headspeed.", "translation": "Tijdconstante voor herstel opstart in seconden, van nul tot volledig rotortoerental.", "needs_translation": "false"}, "gov_startup_time": {"english": "Time constant for slow startup, in seconds, measuring the time from zero to full headspeed.", "translation": "Tijdconstante voor een langzame opstart in seconden, van nul tot volledig rotortoerental, zonder rpm signaal.", "needs_translation": "false"}, "gov_handover_throttle": {"english": "Governor activates above this %. Below this the input throttle is passed to the ESC.", "translation": "Governor activeert als throttle hoger is als deze waarde. Als throttle lager is wordt deze doorgestuurd naar de esc", "needs_translation": "false"}, "gov_spoolup_time": {"english": "Time constant for slow spoolup, in seconds, measuring the time from zero to full headspeed.", "translation": "Tijdconstante voor een langzame opstart in seconden, van nul tot volledig rotortoerental, met rpm signaal.", "needs_translation": "false"}, "gov_spoolup_min_throttle": {"english": "Minimum throttle to use for slow spoolup, in percent. For electric motors the default is 5%, for nitro this should be set so the clutch starts to engage for a smooth spoolup 10-15%.", "translation": "Minimale throttle wat gebruikt wordt voor de langzame opstart in procent. Voor elektrisch 5%, voor nitro zo hoog dat de clutch net aangrijpt meestal rond 10-15%.", "needs_translation": "false"}, "tbl_govmode_off": {"english": "OFF", "translation": "Uit", "needs_translation": "false"}}, "RC_CONFIG": {"rc_deflection": {"english": "Stick deflection from center in microseconds (us).", "translation": "<PERSON> afwijking van het midden in microseconds (us).", "needs_translation": "false"}, "rc_min_throttle": {"english": "Minimum throttle (0% throttle output) expected from radio, in microseconds (us).", "translation": "Minimaal verwachte throttle (0% throttleoutput) van de radio, in microseconden (VS).", "needs_translation": "false"}, "rc_max_throttle": {"english": "Maximum throttle (100% throttle output) expected from radio, in microseconds (us).", "translation": "Maximum verwachte throttle (100% throttle output) van de radio, in microseconds (us).", "needs_translation": "false"}, "rc_arm_throttle": {"english": "Throttle must be at or below this value in microseconds (us) to allow arming. Must be at least 10us lower than minimum throttle.", "translation": "Throttle moet op of onder deze waarde in microseconden (us) staan ​​om arming toe te staan. Moet ten minste 10us lager zijn dan minimum throttle.", "needs_translation": "false"}, "rc_yaw_deadband": {"english": "Deadband for yaw control in microseconds (us).", "translation": "Deadband voor yaw control in microseconds (us).", "needs_translation": "false"}, "rc_deadband": {"english": "Deadband for cyclic control in microseconds (us).", "translation": "Deadband voor cyclic control in microseconds (us).", "needs_translation": "false"}, "rc_center": {"english": "Stick center in microseconds (us).", "translation": "Stick center in microseconds (us).", "needs_translation": "false"}}, "FILTER_CONFIG": {"gyro_soft_notch_cutoff_2": {"english": "Width of the notch filter in Hz.", "translation": "<PERSON><PERSON><PERSON> van het filter in Hz.", "needs_translation": "false"}, "gyro_lpf1_static_hz": {"english": "Lowpass filter cutoff frequency in Hz.", "translation": "Lowpass filter cutoff frequentie in Hz.", "needs_translation": "false"}, "tbl_none": {"english": "NONE", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "dyn_notch_max_hz": {"english": "Maximum frequency to which the notch is applied.", "translation": "Maximum frequenctie tot waar het filter stopt met filteren.", "needs_translation": "false"}, "tbl_1st": {"english": "1ST", "translation": "1ST", "needs_translation": "false"}, "rpm_min_hz": {"english": "Minimum frequency for the RPM filter.", "translation": "Minimum frequenctie voor het rpm filter.", "needs_translation": "false"}, "dyn_notch_min_hz": {"english": "Minimum frequency to which the notch is applied.", "translation": "Minimum frequentie waar het filter start met filteren.", "needs_translation": "false"}, "gyro_lpf1_dyn_max_hz": {"english": "Dynamic filter max cutoff in Hz.", "translation": "Dynamisch filter maximale frequentie in Hz.", "needs_translation": "false"}, "gyro_soft_notch_hz_2": {"english": "Center frequency to which the notch is applied.", "translation": "Frequentie waar het filter wordt toegepast.", "needs_translation": "false"}, "gyro_soft_notch_cutoff_1": {"english": "Width of the notch filter in Hz.", "translation": "<PERSON><PERSON><PERSON> van de het filter in Hz.", "needs_translation": "false"}, "gyro_soft_notch_hz_1": {"english": "Center frequency to which the notch is applied.", "translation": "Frequentie waar het filter wordt toegepast.", "needs_translation": "false"}, "dyn_notch_count": {"english": "Number of notches to apply.", "translation": "Number of notches to apply.", "needs_translation": "false"}, "dyn_notch_q": {"english": "Quality factor of the notch filter.", "translation": "<PERSON><PERSON><PERSON> van het notch filter in het spectrum.", "needs_translation": "false"}, "gyro_lpf2_static_hz": {"english": "Lowpass filter cutoff frequency in Hz.", "translation": "Lowpass filter cutoff frequentie in Hz.", "needs_translation": "false"}, "gyro_lpf1_dyn_min_hz": {"english": "Dynamic filter min cutoff in Hz.", "translation": "Dynamisch filter minimale frequentie in Hz.", "needs_translation": "false"}, "tbl_2nd": {"english": "2ND", "translation": "2ND", "needs_translation": "false"}, "tbl_custom": {"english": "CUSTOM", "translation": "CUSTOM", "needs_translation": "false"}, "tbl_low": {"english": "LOW", "translation": "Laag", "needs_translation": "false"}, "tbl_medium": {"english": "MEDIUM", "translation": "MEDIUM", "needs_translation": "false"}, "tbl_high": {"english": "HIGH", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "ESC_PARAMETERS_XDFLY": {"tbl_jadegreen": {"english": "JADE GREEN", "translation": "JADE GREEN", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "Uit", "needs_translation": "false"}, "tbl_low": {"english": "Low", "translation": "Low", "needs_translation": "false"}, "tbl_orange": {"english": "ORANGE", "translation": "ORANGE", "needs_translation": "false"}, "tbl_fmfw": {"english": "Fixed Wing", "translation": "Fixed Wing", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "tbl_medium": {"english": "Medium", "translation": "Medium", "needs_translation": "false"}, "tbl_yellow": {"english": "YELLOW", "translation": "YELLOW", "needs_translation": "false"}, "tbl_reverse": {"english": "Reverse", "translation": "Omgekeerd", "needs_translation": "false"}, "tbl_red": {"english": "Red", "translation": "Red", "needs_translation": "false"}, "tbl_high": {"english": "High", "translation": "High", "needs_translation": "false"}, "tbl_auto": {"english": "Auto", "translation": "Auto", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "tbl_fmheli": {"english": "Helicopter", "translation": "Helicopter", "needs_translation": "false"}, "tbl_purple": {"english": "PURPLE", "translation": "PURPLE", "needs_translation": "false"}, "tbl_green": {"english": "GREEN", "translation": "GREEN", "needs_translation": "false"}, "tbl_blue": {"english": "BLUE", "translation": "BLUE", "needs_translation": "false"}, "tbl_slow": {"english": "Slow", "translation": "<PERSON><PERSON><PERSON>", "needs_translation": "false"}, "tbl_normal": {"english": "Normal", "translation": "Normaal", "needs_translation": "false"}, "tbl_fast": {"english": "Fast", "translation": "Snel", "needs_translation": "false"}, "tbl_escgov": {"english": "ESC Governor", "translation": "ESC Governor", "needs_translation": "false"}, "tbl_white": {"english": "WHITE", "translation": "WHITE", "needs_translation": "false"}, "tbl_cyan": {"english": "CYAN", "translation": "CYAN", "needs_translation": "false"}, "tbl_vslow": {"english": "Very Slow", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_extgov": {"english": "External Governor", "translation": "External Governor", "needs_translation": "false"}, "tbl_pink": {"english": "PINK", "translation": "PINK", "needs_translation": "false"}, "tbl_fwgov": {"english": "Fixed Wing", "translation": "Fixed Wing", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "<PERSON><PERSON>", "needs_translation": "false"}}, "ESC_PARAMETERS_SCORPION": {"tbl_airplane": {"english": "Airplane mode", "translation": "Airplane mode", "needs_translation": "false"}, "tbl_cw": {"english": "CW", "translation": "CW", "needs_translation": "false"}, "tbl_off": {"english": "Off", "translation": "Uit", "needs_translation": "false"}, "tbl_quad": {"english": "Quad mode", "translation": "Quad mode", "needs_translation": "false"}, "tbl_heligov": {"english": "<PERSON><PERSON> Governor", "translation": "<PERSON><PERSON> Governor", "needs_translation": "false"}, "tbl_exbus": {"english": "Jeti Exbus", "translation": "Jeti Exbus", "needs_translation": "false"}, "tbl_boat": {"english": "Boat mode", "translation": "Boat mode", "needs_translation": "false"}, "tbl_unsolicited": {"english": "Unsolicited", "translation": "Unsolicited", "needs_translation": "false"}, "tbl_futsbus": {"english": "Futaba SBUS", "translation": "Futaba SBUS", "needs_translation": "false"}, "tbl_ccw": {"english": "CCW", "translation": "CCW", "needs_translation": "false"}, "tbl_helistore": {"english": "<PERSON>li Governor (stored)", "translation": "<PERSON>li Governor (stored)", "needs_translation": "false"}, "tbl_standard": {"english": "Standard", "translation": "Standaard", "needs_translation": "false"}, "tbl_on": {"english": "On", "translation": "<PERSON><PERSON>", "needs_translation": "false"}, "tbl_vbar": {"english": "VBar", "translation": "VBar", "needs_translation": "false"}, "tbl_vbargov": {"english": "<PERSON><PERSON><PERSON> Governor", "translation": "<PERSON><PERSON><PERSON> Governor", "needs_translation": "false"}, "tbl_extgov": {"english": "External Governor", "translation": "External Governor", "needs_translation": "false"}}}