{"version": "0.2.0", "configurations": [{"name": "Deploy & Launch", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/bin/deploy/deploy.py", "args": ["--launch"], "console": "integratedTerminal"}, {"name": "Deploy & Choose", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/bin/deploy/deploy.py", "args": ["--choose", "--launch"], "console": "integratedTerminal"}, {"name": "Deploy Radio", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/bin/deploy/deploy.py", "args": ["--radio", "--radio-debug"], "console": "integratedTerminal"}]}