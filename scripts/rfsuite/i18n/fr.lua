--[[
 * Copyright (C) Rotorflight Project
 *
 * License GPLv3: https://www.gnu.org/licenses/gpl-3.0.en.html
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * Please do not edit these files directly. These files are generated
 * from JSON files contained within the folder:
 *
 *   bin/i18n/json/*.json
 *
 * Running the script:
 *
 *   lua54 json-to-lua.lua
 *
 * will create these files for you.
]]

return {
  ["reload"] = "recharger",
  ["image"] = "image",
  ["error"] = "erreur",
  ["save"] = "enregistrer",
  ["ethos"] = "ethos",
  ["version"] = "version",
  ["bg_task_disabled"] = "tache AR plan desactivee",
  ["no_link"] = "aucun lien",
  ["background_task_disabled"] = "tache en arriere-plan desactivee",
  ["no_sensor"] = "pas de capteur",
  ["api"] = {
    ["RC_TUNING"] = {
      ["setpoint_boost_cutoff_2"] = "Seuil de boost pour le point de consigne.",
      ["response_time_3"] = "Augmenter ou diminuer le temps de reponse du taux pour lisser les mouvements de l'helico.",
      ["accel_limit_4"] = "Acceleration maximale de l'appareil en reponse a un mouvement du manche.",
      ["setpoint_boost_gain_4"] = "Gain de boost pour le point de consigne.",
      ["yaw_dynamic_deadband_filter"] = "Le filtre maximal applique a la zone morte dynamique du yaw.",
      ["setpoint_boost_gain_3"] = "Gain de boost pour le point de consigne.",
      ["response_time_2"] = "Augmenter ou diminuer le temps de reponse du taux pour lisser les mouvements de l'helico.",
      ["accel_limit_1"] = "Acceleration maximale de l'appareil en reponse a un mouvement du manche.",
      ["setpoint_boost_cutoff_1"] = "Seuil de boost pour le point de consigne.",
      ["setpoint_boost_cutoff_4"] = "Seuil de boost pour le point de consigne.",
      ["setpoint_boost_gain_2"] = "Gain de boost pour le point de consigne.",
      ["accel_limit_2"] = "Acceleration maximale de l'appareil en reponse a un mouvement du manche.",
      ["yaw_dynamic_deadband_gain"] = "Le gain maximal applique a la zone morte dynamique du yaw.",
      ["accel_limit_3"] = "Acceleration maximale de l'appareil en reponse a un mouvement du manche.",
      ["response_time_4"] = "Augmenter ou diminuer le temps de reponse du taux pour lisser les mouvements de l'helico.",
      ["setpoint_boost_cutoff_3"] = "Seuil de boost pour le point de consigne.",
      ["setpoint_boost_gain_1"] = "Gain de boost pour le point de consigne.",
      ["yaw_dynamic_ceiling_gain"] = "Le gain maximal applique au plafond dynamique du yaw.",
      ["response_time_1"] = "Augmenter ou diminuer le temps de reponse du taux pour lisser les mouvements de l'helico."
    },
    ["RESCUE_PROFILE"] = {
      ["rescue_flip_gain"] = "Determiner l'agressivite avec laquelle l'helico se retourne pendant le sauvetage inverse.",
      ["rescue_level_gain"] = "Determiner l'agressivite avec laquelle l'helico se met a niveau pendant le sauvetage.",
      ["tbl_off"] = "DESACTIVER",
      ["rescue_hover_collective"] = "Valeur collective pour le vol stationnaire.",
      ["rescue_max_setpoint_rate"] = "Limiter le taux de roulis/tangage de sauvetage. Les helicos plus grands peuvent necessiter des taux de rotation plus lents.",
      ["tbl_flip"] = "RENVERSEMENT",
      ["rescue_flip_mode"] = "Si le sauvetage est active en etant inverse, retourner a l'endroit - ou rester inverse.",
      ["rescue_pull_up_time"] = "Lorsque le sauvetage est active, l'helicoptere appliquera un collectif de redressement pendant cette periode avant de passer a l'etape de retournement ou de montee.",
      ["tbl_noflip"] = "PAS DE RENVERSEMENT",
      ["rescue_exit_time"] = "Cela limite l'application rapide du collectif negatif si l'helicoptere a roule pendant le sauvetage.",
      ["rescue_pull_up_collective"] = "Valeur collective pour la montee de redressement.",
      ["rescue_max_setpoint_accel"] = "Limiter la vitesse a laquelle l'helicoptere accelere dans un roulis/tangage. Les helicos plus grands peuvent necessiter une acceleration plus lente.",
      ["tbl_on"] = "ACTIVER",
      ["rescue_climb_collective"] = "Valeur collective pour la montee de sauvetage.",
      ["rescue_flip_time"] = "Si l'helicoptere est en sauvetage et essaie de se retourner a l'endroit et ne le fait pas dans ce delai, le sauvetage sera annule.",
      ["rescue_climb_time"] = "Duree pendant laquelle le collectif de montee est applique avant de passer au vol stationnaire."
    },
    ["ESC_SENSOR_CONFIG"] = {
      ["hw4_current_offset"] = "Ajustement de l'offset de courant Hobbywing v4",
      ["tbl_off"] = "Desactive",
      ["update_hz"] = "Taux de mise a jour de la telemetrie ESC",
      ["half_duplex"] = "Mode half-duplex pour la telemetrie ESC",
      ["consumption_correction"] = "Ajuster la correction de consommation",
      ["current_offset"] = "Ajustement de l'offset du capteur de courant",
      ["voltage_correction"] = "Ajuster la correction de tension",
      ["hw4_voltage_gain"] = "Ajustement du gain de tension Hobbywing v4",
      ["tbl_on"] = "Active",
      ["hw4_current_gain"] = "Ajustement du gain de courant Hobbywing v4",
      ["current_correction"] = "Ajuster la correction de courant",
      ["pin_swap"] = "Inverser les broches TX et RX pour la telemetrie ESC"
    },
    ["ESC_PARAMETERS_FLYROTOR"] = {
      ["tbl_alwayson"] = "Toujours Allume",
      ["throttle_min"] = "Valeur minimale des gaz",
      ["tbl_disabled"] = "Desactive",
      ["tbl_auto"] = "Automatique",
      ["starting_torque"] = "Couple de demarrage pour le moteur",
      ["cell_count"] = "Nombre de cellules dans la batterie",
      ["motor_erpm_max"] = "RPM maximum",
      ["throttle_max"] = "Valeur maximale des gaz",
      ["tbl_ccw"] = "Sens AntiHoraire",
      ["tbl_escgov"] = "Regulateur ESC",
      ["temperature_protection"] = "Temperature a laquelle nous reduisons la puissance de 50%",
      ["tbl_automatic"] = "Automatique",
      ["low_voltage_protection"] = "Tension a laquelle nous reduisons la puissance de 50%",
      ["tbl_cw"] = "Sens Horaire",
      ["soft_start"] = "Valeur de demarrage progressif",
      ["gov_i"] = "Valeur integrale pour le regulateur",
      ["timing_angle"] = "Angle de synchronisation pour le moteur",
      ["response_speed"] = "Vitesse de reponse pour le moteur",
      ["current_gain"] = "Valeur de gain pour le capteur de courant",
      ["tbl_extgov"] = "Regulateur Externe",
      ["buzzer_volume"] = "Volume du buzzer",
      ["gov_d"] = "Valeur derivee pour le regulateur",
      ["tbl_enabled"] = "Active",
      ["gov_p"] = "Valeur proportionnelle pour le regulateur"
    },
    ["ESC_PARAMETERS_YGE"] = {
      ["tbl_alwayson"] = "Toujours Allume",
      ["tbl_off"] = "Desactive",
      ["tbl_modestore"] = "Gouverneur Heli (maintient)",
      ["tbl_modefree"] = "Libre (Attention!)",
      ["tbl_modeglider"] = "Planeur Aero",
      ["tbl_modeext"] = "Gouverneur Ext Heli",
      ["tbl_modeheli"] = "Gouverneur Heli",
      ["tbl_medium"] = "Moyen",
      ["tbl_autonorm"] = "Auto Normal",
      ["tbl_reverse"] = "Inverse",
      ["tbl_modef3a"] = "Aero F3A",
      ["tbl_auto"] = "Auto",
      ["tbl_slowdown"] = "Ralentir",
      ["tbl_slow"] = "Lent",
      ["tbl_modeair"] = "Moteur Aero",
      ["tbl_normal"] = "Normal",
      ["tbl_on"] = "Active",
      ["tbl_autoextreme"] = "Auto Extreme",
      ["tbl_autoefficient"] = "Auto Efficace",
      ["tbl_smooth"] = "Doux",
      ["tbl_fast"] = "Rapide",
      ["tbl_custom"] = "Personnalise (Defini par PC)",
      ["tbl_cutoff"] = "Coupure",
      ["tbl_autopower"] = "Auto Puissance",
      ["tbl_unused"] = "*Inutilise*"
    },
    ["GOVERNOR_PROFILE"] = {
      ["governor_tta_gain"] = "Gain TTA appliquer pour augmenter la vitesse de rotation afin de controler la queue dans le sens negatif (par exemple, une queue motorisee a une vitesse inferieure au ralenti).",
      ["governor_collective_ff_weight"] = "Valeur de precompensation collective - quantite de collective melangee dans la precompensation.",
      ["governor_i_gain"] = "Gain du terme I de la boucle PID.",
      ["governor_cyclic_ff_weight"] = "Valeur de precompensation cyclique - quantite de cyclique melangee dans la precompensation.",
      ["governor_f_gain"] = "Gain de precompensation (feedforward).",
      ["governor_gain"] = "Gain principal de la boucle PID.",
      ["governor_headspeed"] = "Vitesse de rotation pour le profil actuel.",
      ["governor_min_throttle"] = "Accelerateur de sortie minimal que le regulateur est autorise a utiliser.",
      ["governor_d_gain"] = "Gain du terme D de la boucle PID.",
      ["governor_p_gain"] = "Gain du terme P de la boucle PID.",
      ["governor_yaw_ff_weight"] = "Valeur de precompensation en lacet - quantite de lacet melangee dans la precompensation.",
      ["governor_max_throttle"] = "Accelerateur de sortie maximal que le regulateur est autorise a utiliser.",
      ["governor_tta_limit"] = "Limite TTA de l'augmentation maximale de la vitesse de rotation par rapport a la vitesse de rotation totale."
    },
    ["PID_PROFILE"] = {
      ["bterm_cutoff_2"] = "Frequence de coupure du B-term en Hz.",
      ["dterm_cutoff_1"] = "Frequence de coupure du D-term en Hz.",
      ["bterm_cutoff_1"] = "Frequence de coupure du B-term en Hz.",
      ["gyro_cutoff_1"] = "Bande passante globale de la boucle PID en Hz.",
      ["tbl_on"] = "ACTIVER",
      ["dterm_cutoff_2"] = "Frequence de coupure du D-term en Hz.",
      ["yaw_inertia_precomp_cutoff"] = "Frequence de coupure. Frequence de coupure du derive en pas de 1/10Hz. Controle la nettete de la precompensation. Une valeur plus elevee est plus nette.",
      ["offset_limit_0"] = "Limite stricte pour l'angle de decalage de l'integrale a grande vitesse dans la boucle PID. Le O-term ne depassera jamais ces limites.",
      ["cyclic_cross_coupling_ratio"] = "Quantite de compensation roulis-tangage necessaire, par rapport au tangage-roulis.",
      ["yaw_precomp_cutoff"] = "Limite de frequence pour toutes les actions de precompensation du lacet.",
      ["error_limit_0"] = "Limite stricte pour l'erreur d'angle dans la boucle PID. L'erreur absolue et donc l'I-term ne depasseront jamais ces limites.",
      ["trainer_gain"] = "Determine l'agressivite avec laquelle l'helicoptere s'incline pour revenir a l'angle maximal (si depasse) en mode Acro Trainer.",
      ["tbl_rpy"] = "RPL",
      ["gyro_cutoff_2"] = "Bande passante globale de la boucle PID en Hz.",
      ["yaw_ccw_stop_gain"] = "Gain d'arret (PD) pour la rotation dans le sens antihoraire.",
      ["trainer_angle_limit"] = "Limite l'angle maximal auquel l'helicoptere s'inclinera en mode Acro Trainer.",
      ["tbl_rp"] = "RP",
      ["yaw_cyclic_ff_gain"] = "Feedforward cyclique melange dans le lacet (precomp cyclique-a-lacet).",
      ["error_decay_time_cyclic"] = "Constante de temps pour reduire l'I-term cyclique. Plus eleve stabilisera le vol stationnaire, plus bas derivera.",
      ["error_decay_limit_cyclic"] = "Vitesse maximale de reduction pour l'I-term cyclique.",
      ["cyclic_cross_coupling_gain"] = "Quantite de compensation appliquee pour le decouplage tangage-roulis.",
      ["yaw_collective_dynamic_decay"] = "Temps de decroissance pour la precompensation supplementaire du lacet sur l'entree collective.",
      ["pitch_collective_ff_gain"] = "Augmenter pour compenser le mouvement de tangage vers le haut cause par la trainee de la queue lors de la montee.",
      ["iterm_relax_type"] = "Choisissez les axes dans lesquels cela est actif. RP: Roulis, Tangage. RPY: Roulis, Tangage, Lacet.",
      ["offset_limit_1"] = "Limite stricte pour l'angle de decalage de l'integrale a grande vitesse dans la boucle PID. Le O-term ne depassera jamais ces limites.",
      ["iterm_relax_cutoff_1"] = "Aide a reduire le rebond apres des mouvements rapides du manche. Peut causer une incoherence dans les petits mouvements du manche si trop bas.",
      ["error_limit_1"] = "Limite stricte pour l'erreur d'angle dans la boucle PID. L'erreur absolue et donc l'I-term ne depasseront jamais ces limites.",
      ["horizon_level_strength"] = "Determine l'agressivite avec laquelle l'helicoptere s'incline pour revenir a l'horizontale en mode Horizon.",
      ["error_limit_2"] = "Limite stricte pour l'erreur d'angle dans la boucle PID. L'erreur absolue et donc l'I-term ne depasseront jamais ces limites.",
      ["iterm_relax_cutoff_2"] = "Aide a reduire le rebond apres des mouvements rapides du manche. Peut causer une incoherence dans les petits mouvements du manche si trop bas.",
      ["tbl_off"] = "DESACTIVER",
      ["yaw_collective_ff_gain"] = "Feedforward collectif melange dans le lacet (precomp collectif-a-lacet).",
      ["gyro_cutoff_0"] = "Bande passante globale de la boucle PID en Hz.",
      ["yaw_collective_dynamic_gain"] = "Un boost supplementaire de precompensation du lacet sur l'entree collective.",
      ["cyclic_cross_coupling_cutoff"] = "Limite de frequence pour la compensation. Une valeur plus elevee rendra l'action de compensation plus rapide.",
      ["error_rotation"] = "Fait pivoter les termes d'erreur actuels de roulis et de tangage autour du lacet lorsque l'appareil tourne. Cela est parfois appele compensation de piro.",
      ["angle_level_limit"] = "Limite l'angle maximal auquel l'helicoptere s'inclinera en mode Angle.",
      ["yaw_cw_stop_gain"] = "Gain d'arret (PD) pour la rotation dans le sens horaire.",
      ["iterm_relax_cutoff_0"] = "Aide a reduire le rebond apres des mouvements rapides du manche. Peut causer une incoherence dans les petits mouvements du manche si trop bas.",
      ["yaw_inertia_precomp_gain"] = "Gain scalaire. La force de l'inertie du rotor principal. Une valeur plus elevee signifie qu'une plus grande precompensation est appliquee au controle du lacet.",
      ["dterm_cutoff_0"] = "Frequence de coupure du D-term en Hz.",
      ["angle_level_strength"] = "Determine l'agressivite avec laquelle l'helicoptere s'incline pour revenir a l'horizontale en mode Angle.",
      ["bterm_cutoff_0"] = "Frequence de coupure du B-term en Hz.",
      ["error_decay_time_ground"] = "Reduit l'erreur actuelle du controleur lorsque l'appareil n'est pas en vol pour eviter que l'appareil ne bascule."
    },
    ["MIXER_CONFIG"] = {
      ["swash_trim_1"] = "Reglage du plateau cyclique pour l'equilibrer lorsque des liens fixes sont utilises.",
      ["tail_motor_idle"] = "Signal de gaz minimum envoye au moteur de queue. Doit etre juste assez eleve pour que le moteur ne s'arrete pas.",
      ["tail_center_trim"] = "Reglage du rotor de queue pour un lacet nul pour un pas variable, ou une acceleration du moteur de queue pour un lacet nul.",
      ["tbl_cw"] = "Sens Horaire",
      ["swash_tta_precomp"] = "Precompensation du mixeur pour un lacet nul.",
      ["swash_trim_2"] = "Reglage du plateau cyclique pour l'equilibrer lorsque des liens fixes sont utilises.",
      ["swash_geo_correction"] = "Ajuster s'il y a trop de collectif negatif ou trop de collectif positif.",
      ["swash_trim_0"] = "Reglage du plateau cyclique pour l'equilibrer lorsque des liens fixes sont utilises.",
      ["swash_phase"] = "Decalage de phase pour les commandes du plateau cyclique.",
      ["collective_tilt_correction_pos"] = "Ajuster l'echelle de correction de l'inclinaison du collectif pour un pas collectif positif.",
      ["collective_tilt_correction_neg"] = "Ajuster l'echelle de correction de l'inclinaison du collectif pour un pas collectif negatif.",
      ["tbl_ccw"] = "Sens AntiHoraire",
      ["swash_pitch_limit"] = "Quantite maximale de pas de pale combine cyclique et collectif."
    },
    ["MOTOR_CONFIG"] = {
      ["minthrottle"] = "Cette valeur PWM est envoyee a l'ESC/Servo a bas regime",
      ["motor_pwm_protocol"] = "Le protocole utilise pour communiquer avec l'ESC",
      ["main_rotor_gear_ratio_0"] = "Nombre de dents du pignon moteur",
      ["maxthrottle"] = "Cette valeur PWM est envoyee a l'ESC/Servo a plein regime",
      ["mincommand"] = "Cette valeur PWM est envoyee lorsque le moteur est arrete",
      ["main_rotor_gear_ratio_1"] = "Nombre de dents de la roue principale",
      ["tail_rotor_gear_ratio_1"] = "Nombre de dents de la roue d'autorotation",
      ["motor_pwm_rate"] = "La frequence a laquelle l'ESC envoie des signaux PWM au moteur",
      ["tail_rotor_gear_ratio_0"] = "Nombre de dents de la roue de queue",
      ["motor_pole_count_0"] = "Le nombre d'aimants sur la cloche du moteur."
    },
    ["ESC_PARAMETERS_HW5"] = {
      ["tbl_cw"] = "Sens Horaire",
      ["tbl_fixedwing"] = "Aile Fixe",
      ["tbl_disabled"] = "Desactive",
      ["tbl_ccw"] = "Sens AntiHoraire",
      ["tbl_heligov"] = "Gouverneur Heli",
      ["tbl_softcutoff"] = "Coupure Douce",
      ["tbl_proportional"] = "Proportionnel",
      ["tbl_heliext"] = "Gouverneur Externe Heli",
      ["tbl_helistore"] = "Gouverneur Heli (maintient)",
      ["tbl_hardcutoff"] = "Coupure Dure",
      ["tbl_normal"] = "Normal",
      ["tbl_autocalculate"] = "Calcul Auto",
      ["tbl_enabled"] = "Active",
      ["tbl_reverse"] = "Inverse"
    },
    ["PILOT_CONFIG"] = {
      ["model_param1_value"] = "Reglez ceci sur la duree de vol prevue en secondes. La radiocommande emettra un bip lorsque la duree sera atteinte."
    },
    ["BATTERY_INI"] = {
      ["calcfuel_local"] = "Calculate Fuel Using",
      ["tbl_off"] = "Current Sensor",
      ["tbl_on"] = "Voltage Sensor",
      ["sag_multiplier"] = "Raise or lower to adjust for the amount of voltage sag you see in flight.",
      ["kalman_multiplier"] = "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.",
      ["alert_off"] = "Off",
      ["alert_bec"] = "BEC",
      ["alert_rxbatt"] = "Rx Batt"
    },
    ["BATTERY_CONFIG"] = {
      ["vbatmincellvoltage"] = "Tension minimale par cellule avant le declenchement de l'alarme de basse tension.",
      ["vbatmaxcellvoltage"] = "Tension maximale par cellule avant le declenchement de l'alarme de haute tension.",
      ["vbatwarningcellvoltage"] = "Tension par cellule a laquelle l'alarme de basse tension commencera a sonner.",
      ["batteryCellCount"] = "Nombre de cellules dans votre batterie.",
      ["vbatfullcellvoltage"] = "Tension nominale d'une cellule completement chargee.",
      ["batteryCapacity"] = "Capacite de votre batterie en milliamperes-heure."
    },
    ["ACC_TRIM"] = {
      ["pitch"] = "Utiliser pour ajuster si l'helico derive dans l'un des modes stabilises (angle, horizon, etc.).",
      ["roll"] = "Utiliser pour ajuster si l'helico derive dans l'un des modes stabilises (angle, horizon, etc.)."
    },
    ["PID_TUNING"] = {
      ["pid_1_I"] = "A quel point le systeme maintient sa position.",
      ["pid_2_P"] = "A quel point le systeme suit le point de consigne desire.",
      ["pid_2_I"] = "A quel point le systeme maintient sa position.",
      ["pid_1_O"] = "Utilise pour empecher l'appareil de tanguer lors de l'utilisation d'un collectif eleve.",
      ["pid_1_F"] = "Aide a pousser le terme P en fonction de l'entree du manche. Augmenter rendra la reponse plus vive, mais peut causer un depassement.",
      ["pid_0_D"] = "Force de l'amortissement de tout mouvement sur le systeme, y compris les influences externes. Reduit egalement le depassement.",
      ["pid_1_P"] = "A quel point le systeme suit le point de consigne desire.",
      ["pid_0_I"] = "A quel point le systeme maintient sa position.",
      ["pid_2_B"] = "Boost supplementaire sur le feedforward pour rendre l'helico plus reactif aux mouvements rapides du manche.",
      ["pid_0_O"] = "Utilise pour empecher l'appareil de rouler lors de l'utilisation d'un collectif eleve.",
      ["pid_0_F"] = "Aide a pousser le terme P en fonction de l'entree du manche. Augmenter rendra la reponse plus vive, mais peut causer un depassement.",
      ["pid_2_F"] = "Aide a pousser le terme P en fonction de l'entree du manche. Augmenter rendra la reponse plus vive, mais peut causer un depassement.",
      ["pid_2_D"] = "Force de l'amortissement de tout mouvement sur le systeme, y compris les influences externes. Reduit egalement le depassement.",
      ["pid_0_P"] = "A quel point le systeme suit le point de consigne desire.",
      ["pid_1_D"] = "Force de l'amortissement de tout mouvement sur le systeme, y compris les influences externes. Reduit egalement le depassement.",
      ["pid_0_B"] = "Boost supplementaire sur le feedforward pour rendre l'helico plus reactif aux mouvements rapides du manche.",
      ["pid_1_B"] = "Boost supplementaire sur le feedforward pour rendre l'helico plus reactif aux mouvements rapides du manche."
    },
    ["GOVERNOR_CONFIG"] = {
      ["tbl_govmode_standard"] = "STANDARD",
      ["tbl_govmode_mode2"] = "MODE2",
      ["gov_tracking_time"] = "Temps constant pour les changements de vitesse de rotation, en secondes, mesurant le temps de zero a pleine vitesse de rotation.",
      ["tbl_govmode_passthrough"] = "EN DIRECT",
      ["tbl_govmode_mode1"] = "MODE1",
      ["gov_recovery_time"] = "Temps constant pour la recuperation progressive, en secondes, mesurant le temps de zero a pleine vitesse de rotation.",
      ["gov_startup_time"] = "Temps constant pour le demarrage lent, en secondes, mesurant le temps de zero a pleine vitesse de rotation.",
      ["gov_handover_throttle"] = "Le gouverneur s'active au-dessus de ce %. En dessous, la commande des gaz est transmise directement a l'ESC.",
      ["gov_spoolup_time"] = "Temps constant pour l'augmentation progressive, en secondes, mesurant le temps de zero a pleine vitesse de rotation.",
      ["gov_spoolup_min_throttle"] = "Gaz minimum a utiliser pour une montee progressive, en pourcentage. Pour les moteurs electriques, la valeur par defaut est 5%, pour le nitro, elle doit etre reglee pour que l'embrayage commence a s'engager en douceur 10-15%.",
      ["tbl_govmode_off"] = "DESACTIVER"
    },
    ["RC_CONFIG"] = {
      ["rc_deflection"] = "Deviation du manche par rapport au centre en microsecondes (us).",
      ["rc_min_throttle"] = "Gaz minimum (0% de sortie des gaz) attendu de la radio, en microsecondes (us).",
      ["rc_max_throttle"] = "Gaz maximum (100% de sortie des gaz) attendu de la radio, en microsecondes (us).",
      ["rc_arm_throttle"] = "Les gaz doivent etre a ou en dessous de cette valeur en microsecondes (us) pour permettre l'armement. Doit etre au moins 10us inferieur au gaz minimum.",
      ["rc_yaw_deadband"] = "Zone morte pour le controle du lacet en microsecondes (us).",
      ["rc_deadband"] = "Zone morte pour le controle cyclique en microsecondes (us).",
      ["rc_center"] = "Centre du manche en microsecondes (us)."
    },
    ["FILTER_CONFIG"] = {
      ["gyro_soft_notch_cutoff_2"] = "Largeur du filtre notch en Hz.",
      ["gyro_lpf1_static_hz"] = "Frequence de coupure du filtre passe-bas en Hz.",
      ["tbl_none"] = "AUCUN",
      ["dyn_notch_max_hz"] = "Frequence maximale a laquelle le notch est applique.",
      ["tbl_1st"] = "1ER",
      ["rpm_min_hz"] = "Frequence minimale pour le filtre des tours moteurs.",
      ["dyn_notch_min_hz"] = "Frequence minimale a laquelle le notch est applique.",
      ["gyro_lpf1_dyn_max_hz"] = "Filtre dynamique - frequence de coupure maximale en Hz.",
      ["gyro_soft_notch_hz_2"] = "Frequence centrale a laquelle le notch est applique.",
      ["gyro_soft_notch_cutoff_1"] = "Largeur du filtre notch en Hz.",
      ["gyro_soft_notch_hz_1"] = "Frequence centrale a laquelle le notch est applique.",
      ["dyn_notch_count"] = "Nombre de filtres notch a appliquer.",
      ["dyn_notch_q"] = "Facteur de qualite du filtre notch.",
      ["gyro_lpf2_static_hz"] = "Frequence de coupure du filtre passe-bas en Hz.",
      ["gyro_lpf1_dyn_min_hz"] = "Filtre dynamique - frequence de coupure minimale en Hz.",
      ["tbl_2nd"] = "2EME",
      ["tbl_custom"] = "PERSONNALISE",
      ["tbl_low"] = "BAS",
      ["tbl_medium"] = "MOYEN",
      ["tbl_high"] = "HAUT"
    },
    ["ESC_PARAMETERS_XDFLY"] = {
      ["tbl_jadegreen"] = "Vert Jade",
      ["tbl_off"] = "Desactive",
      ["tbl_low"] = "Bas",
      ["tbl_orange"] = "Orange",
      ["tbl_fmfw"] = "Aile Fixe",
      ["tbl_ccw"] = " Sens AntiHoraire",
      ["tbl_medium"] = "Moyen",
      ["tbl_yellow"] = "Jaune",
      ["tbl_reverse"] = "Inverse",
      ["tbl_red"] = "Rouge",
      ["tbl_high"] = "Haut",
      ["tbl_auto"] = "Auto",
      ["tbl_cw"] = "Sens Horaire",
      ["tbl_fmheli"] = "Helicoptere",
      ["tbl_purple"] = "Violet",
      ["tbl_green"] = "Vert",
      ["tbl_blue"] = "Bleu",
      ["tbl_slow"] = "Lent",
      ["tbl_normal"] = "Normal",
      ["tbl_fast"] = "Rapide",
      ["tbl_escgov"] = "Gouverneur ESC",
      ["tbl_white"] = "Blanc",
      ["tbl_cyan"] = "Cyan",
      ["tbl_vslow"] = "Tres Lent",
      ["tbl_extgov"] = "Gouverneur Externe",
      ["tbl_pink"] = "Rose",
      ["tbl_fwgov"] = "Gouverneur Aile Fixe",
      ["tbl_on"] = "Active"
    },
    ["ESC_PARAMETERS_SCORPION"] = {
      ["tbl_airplane"] = "Mode Avion",
      ["tbl_cw"] = "Sens Horaire",
      ["tbl_off"] = "Desactive",
      ["tbl_quad"] = "Mode Quad",
      ["tbl_heligov"] = "Gouverneur Heli",
      ["tbl_exbus"] = "Jeti Exbus",
      ["tbl_boat"] = "Mode Bateau",
      ["tbl_unsolicited"] = "Non Sollicite",
      ["tbl_futsbus"] = "Futaba SBUS",
      ["tbl_ccw"] = "Sens AntiHoraire",
      ["tbl_helistore"] = "Gouverneur Heli (maintient)",
      ["tbl_standard"] = "Standard",
      ["tbl_on"] = "Active",
      ["tbl_vbar"] = "VBar",
      ["tbl_vbargov"] = "Gouverneur VBar",
      ["tbl_extgov"] = "Gouverneur Externe"
    }
  },
  ["app"] = {
    ["btn_ok"] = "          OK           ",
    ["btn_close"] = "FERMER",
    ["navigation_menu"] = "MENU",
    ["menu_section_hardware"] = "Materiel",
    ["msg_please_disarm_to_save_warning"] = "Les parametres ne seront sauvegardes dans l'EEPROM qu'apres desarmement",
    ["msg_saving_settings"] = "Enregistrement des parametres...",
    ["msg_saving_to_fbl"] = "Enregistrement des donnees dans le controleur de vol...",
    ["navigation_reload"] = "RECHAR.",
    ["menu_section_developer"] = "Developpeur",
    ["check_msp_version"] = "Impossible de determiner la version MSP utilisee.",
    ["menu_section_about"] = "A propos",
    ["msg_please_disarm_to_save"] = "Veuillez desarmer pour enregistrer afin d'assurer l'integrite des donnees.",
    ["unit_hertz"] = "Hz",
    ["msg_rebooting"] = "Redemarrage...",
    ["msg_save_settings"] = "Enregistrer les parametres",
    ["btn_cancel"] = "ANNULER",
    ["msg_connecting_to_fbl"] = "Connexion au controleur de vol...",
    ["navigation_help"] = "?",
    ["modules"] = {
      ["stats"] = {
        ["name"] = "Statistiques",
        ["totalflighttime"] = "Temps de vol total",
        ["flightcount"] = "Nombre de vols",
        ["lastflighttime"] = "Dernier temps de vol",
        ["help_p1"] = "Utilisez ce module pour mettre à jour les statistiques de vol enregistrées sur le contrôleur de vol."
      },
      ["settings"] = {
        ["name"] = "Paramètres",
        ["no_themes_available_to_configure"] = "Aucun theme configurable n'est installe sur cet appareil",
        ["txt_audio_timer"] = "Minuteur",
        ["txt_audio_events"] = "Evenements",
        ["txt_audio_switches"] = "Interrupteurs",
        ["txt_iconsize"] = "Taille Icones",
        ["txt_general"] = "General",
        ["txt_text"] = "TEXTE",
        ["txt_small"] = "PETIT",
        ["txt_large"] = "GRAND",
        ["txt_syncname"] = "Synchroniser nom modele",
        ["txt_devtools"] = "Outils developpeur",
        ["txt_apiversion"] = "Version API",
        ["txt_logging"] = "Journalisation",
        ["txt_compilation"] = "Compilation",
        ["txt_loglocation"] = "Emplacement Journal",
        ["txt_console"] = "CONSOLE",
        ["txt_consolefile"] = "CONSOLE ET FICHIER",
        ["txt_loglevel"] = "Niveau journal",
        ["txt_off"] = "DESACTIVE",
        ["txt_info"] = "INFO",
        ["txt_debug"] = "DEBUG",
        ["txt_mspdata"] = "Journaliser donnees MSP",
        ["txt_queuesize"] = "Taille de file d'attente MSP Journal",
        ["txt_memusage"] = "Journaliser utilisation memoire",
        ["txt_batttype"] = "Tx Battery Options",
        ["txt_battdef"] = "Default",
        ["txt_batttext"] = "Text",
        ["txt_battdig"] = "Digital",
        ["dashboard"] = "Tableau de bord",
        ["dashboard_theme"] = "Theme",
        ["dashboard_theme_panel_global"] = "Theme par defaut pour tous les modeles",
        ["dashboard_theme_panel_model"] = "Theme optionnel pour ce modele",
        ["dashboard_theme_panel_model_disabled"] = "Desactive",
        ["dashboard_settings"] = "Options",
        ["dashboard_theme_preflight"] = "Theme prevol",
        ["dashboard_theme_inflight"] = "Theme en vol",
        ["dashboard_theme_postflight"] = "Theme apres vol",
        ["audio"] = "Audio",
        ["localizations"] = "Localisation",
        ["txt_development"] = "Developpement",
        ["temperature_unit"] = "Unite de temperature",
        ["altitude_unit"] = "Unite d'altitude",
        ["celcius"] = "Celsius",
        ["fahrenheit"] = "Fahrenheit",
        ["meters"] = "Metres",
        ["feet"] = "Pieds",
        ["warning"] = "Avertissement",
        ["governor_state"] = "Etat du regulateur",
        ["arming_flags"] = "Indicatifs d'armement",
        ["voltage"] = "Tension",
        ["pid_rates_profile"] = "Profil PID/Taux",
        ["pid_profile"] = "Profil de PID",
        ["rate_profile"] = "Profil de Taux",
        ["esc_temperature"] = "Temperature ESC",
        ["esc_threshold"] = "Seuil (deg)",
        ["bec_voltage"] = "Tension BEC",
        ["bec_threshold"] = "Seuil (Volt)",
        ["fuel"] = "Carburant",
        ["fuel_callout_default"] = "Par defaut (uniquement a 10%)",
        ["fuel_callout_10"] = "Chaque 10%",
        ["fuel_callout_20"] = "Chaque 20%",
        ["fuel_callout_25"] = "Every 25%",
        ["fuel_callout_50"] = "Every 50%",
        ["fuel_callout_percent"] = "Annonce %",
        ["fuel_repeats_below"] = "Rappels en dessous de 0%",
        ["fuel_haptic_below"] = "Vibration en dessous de 0%",
        ["timer_alerting"] = "Alerte minuteur",
        ["timer_elapsed_alert_mode"] = "Alerte fin de minuteur",
        ["timer_prealert_options"] = "Options d'alerte avant minuteur",
        ["timer_prealert"] = "Alerte avant minuteur",
        ["timer_alert_period"] = "Periode d'alerte",
        ["timer_postalert_options"] = "Options d'alerte apres minuteur",
        ["timer_postalert"] = "Alerte apres minuteur",
        ["timer_postalert_period"] = "Periode alerte",
        ["timer_postalert_interval"] = "Intervalle alerte"
      },
      ["validate_sensors"] = {
        ["help_p1"] = "Cet outil tente de lister tous les capteurs que vous ne recevez pas dans une liste concise.",
        ["invalid"] = "INVALIDE",
        ["name"] = "Capteurs",
        ["msg_repair"] = "Activer les capteurs requis sur le controleur de vol ?",
        ["msg_repair_fin"] = "Le controleur de vol a-t-il ete configure ? Vous devrez peut-etre effectuer une detection des capteurs pour voir les modifications.",
        ["ok"] = "OK",
        ["help_p2"] = "Utilisez cet outil pour vous assurer que vous envoyez les bons capteurs."
      },
      ["msp_exp"] = {
        ["help_p1"] = "Cet outil permet d'envoyer une chaîne d'octets personnalisee au controleur de vol. Il est utile aux developpeurs pour le debogage des valeurs.",
        ["name"] = "MSP Experimental",
        ["help_p2"] = "Si vous ne comprenez pas ce que vous faites, ne l'utilisez pas, car des problemes graves pourraient survenir."
      },
      ["esc_tools"] = {
        ["unknown"] = "INCONNU",
        ["name"] = "Outils ESC",
        ["please_powercycle"] = "Veuillez redemarrer l'ESC...",
        ["mfg"] = {
          ["hw5"] = {
            ["esc"] = "ESC",
            ["brake_force"] = "% Force frein",
            ["rotation"] = "Rotation",
            ["soft_start"] = "Demar. Prog.",
            ["name"] = "Hobbywing V5",
            ["limits"] = "Limites",
            ["bec_voltage"] = "Tension BEC",
            ["gov_i_gain"] = "Gain-I",
            ["startup_time"] = "Temps demar.",
            ["lipo_cell_count"] = "Nombre cellules LiPo",
            ["restart_time"] = "Temps redemarrage",
            ["volt_cutoff_type"] = "Type coupure tension",
            ["motor"] = "Moteur",
            ["brake_type"] = "Type de frein",
            ["brake"] = "Frein",
            ["governor"] = "Gouverneur",
            ["advanced"] = "Avance",
            ["basic"] = "Basique",
            ["flight_mode"] = "Mode de vol",
            ["auto_restart"] = "Redemarrage auto",
            ["active_freewheel"] = "Roue libre active",
            ["cutoff_voltage"] = "Tension de coupure",
            ["startup_power"] = "Puissance demarrage",
            ["other"] = "Autre",
            ["timing"] = "Timing",
            ["gov_p_gain"] = "Gain-P"
          },
          ["xdfly"] = {
            ["hv_bec_voltage"] = "Tension HV BEC",
            ["gov"] = "Gouverneur",
            ["brake_force"] = "Force de freinage",
            ["sr_function"] = "Synchronisation Rectificative",
            ["name"] = "XDFLY",
            ["lv_bec_voltage"] = "Tension LV BEC",
            ["auto_restart_time"] = "Temps redemarrage auto",
            ["acceleration"] = "Acceleration",
            ["motor_direction"] = "Sens moteur",
            ["smart_fan"] = "Ventilateur intelligent",
            ["governor"] = "Gouverneur",
            ["advanced"] = "Avance",
            ["gov_i"] = "Gov-I",
            ["cell_cutoff"] = "Coupure cellule",
            ["led_color"] = "Couleur LED",
            ["basic"] = "Basique",
            ["startup_power"] = "Puissance demarrage",
            ["motor_poles"] = "Poles moteur",
            ["capacity_correction"] = "Correction capacite",
            ["timing"] = "Timing",
            ["gov_p"] = "Gov-P"
          },
          ["flrtr"] = {
            ["gov"] = "Gouverneur",
            ["motor_temp_sensor"] = "Capteur de temperature moteur",
            ["starting_torque"] = "Couple de demarrage",
            ["cell_count"] = "Nombre de cellules",
            ["gov_p"] = "Gov-P",
            ["motor_erpm_max"] = "ERPM max moteur",
            ["name"] = "FLYROTOR",
            ["low_voltage_protection"] = "Protection basse tension",
            ["gov_d"] = "Gov-D",
            ["telemetry_protocol"] = "Protocole de telemetrie",
            ["motor_direction"] = "Sens moteur",
            ["throttle_protocol"] = "Protocole des gaz",
            ["soft_start"] = "Demarrage progressif",
            ["other"] = "Autre",
            ["temperature_protection"] = "Protection temperature",
            ["buzzer_volume"] = "Volume du buzzer",
            ["timing_angle"] = "Angle de timing",
            ["governor"] = "Gouverneur",
            ["advanced"] = "Avance",
            ["gov_i"] = "Gov-I",
            ["bec_voltage"] = "Tension BEC",
            ["fan_control"] = "Controle ventilateur",
            ["basic"] = "Basique",
            ["current_gain"] = "Gain du courant",
            ["led_color"] = "Couleur LED",
            ["motor_temp"] = "Temperature moteur",
            ["response_speed"] = "Vitesse de reponse",
            ["battery_capacity"] = "Capacite de la batterie"
          },
          ["scorp"] = {
            ["esc_mode"] = "Mode ESC",
            ["min_voltage"] = "Tension min",
            ["rotation"] = "Rotation",
            ["telemetry_protocol"] = "Protocole telemetrie",
            ["name"] = "Scorpion",
            ["runup_time"] = "Temps de montee",
            ["motor_startup_sound"] = "Son demarrage moteur",
            ["gov_integral"] = "Gov Integral",
            ["gov_proportional"] = "Gov Proportionnel",
            ["cutoff_handling"] = "Gestion coupure",
            ["bailout"] = "Bailout",
            ["limits"] = "Limites",
            ["soft_start_time"] = "Temps demarrage progressif",
            ["advanced"] = "Avance",
            ["bec_voltage"] = "Tension BEC",
            ["extra_msg_save"] = "Veuillez redemarrer l'ESC pour appliquer les changements",
            ["basic"] = "Basique",
            ["max_current"] = "Courant max",
            ["max_temperature"] = "Temperature max",
            ["protection_delay"] = "Delai protection",
            ["max_used"] = "Max utilise"
          },
          ["yge"] = {
            ["esc_mode"] = "Mode ESC",
            ["esc"] = "ESC",
            ["current_limit"] = "Limite courant",
            ["f3c_auto"] = "Autorotation F3C",
            ["name"] = "YGE",
            ["max_start_power"] = "Puissance max demarrage",
            ["lv_bec_voltage"] = "BEC",
            ["pinion_teeth"] = "Dents du pignon",
            ["auto_restart_time"] = "Temps redemar. auto",
            ["main_teeth"] = "Dents principales",
            ["other"] = "Autre",
            ["limits"] = "Limites",
            ["cell_cutoff"] = "Coupure cellule",
            ["throttle_response"] = "Reponse acceleration",
            ["stick_zero_us"] = "Zero stick",
            ["advanced"] = "Avance",
            ["gov_i"] = "Gov-I",
            ["motor_pole_pairs"] = "Paires de poles moteur",
            ["stick_range_us"] = "Plage du stick",
            ["basic"] = "Basique",
            ["min_start_power"] = "Puissance min demarrage",
            ["active_freewheel"] = "Roue libre active",
            ["direction"] = "Sens",
            ["timing"] = "Timing moteur",
            ["gov_p"] = "Gov-P"
          }
        },
        ["searching"] = "Recherche"
      },
      ["pids"] = {
        ["help_p1"] = "FeedForward (Roulis/Tangage): Commencez a 70, augmentez jusqu'a obtenir des arrets nets sans derive. Gardez les valeurs roulis et tangage egales.",
        ["o"] = "O",
        ["pitch"] = "Tangage",
        ["i"] = "I",
        ["yaw"] = "Lacet",
        ["roll"] = "Roulis",
        ["help_p5"] = "Tester & Ajuster : Volez, observez et affinez pour obtenir les meilleures performances dans des conditions reelles.",
        ["p"] = "P",
        ["f"] = "F",
        ["name"] = "PIDs",
        ["help_p2"] = "Gain I (Roulis/Tangage): Augmentez progressivement pour stabiliser les manoeuvres rapides en pirouettes. Trop eleve provoque des oscillations; egalisez les valeurs roulis/tangage.",
        ["d"] = "D",
        ["b"] = "B",
        ["help_p4"] = "Gain d'arret de la queue (Horaire/Anti-horaire): Ajustez separement pour des arrets nets et sans rebonds dans les deux directions.",
        ["help_p3"] = "Gains P/I/D de queue: Augmentez P jusqu'a un leger tremblement en funnels, puis reduisez legerement. Montez I jusqu'a un maintien ferme de la queue dans les manoeuvres rapides (trop eleve provoque une oscillation lente). Ajustez D pour des arrets en douceur. Plus eleve pour les servos lents et plus faible pour des servos rapide"
      },
      ["msp_speed"] = {
        ["seconds_600"] = "  600S  ",
        ["avg_query_time"] = "Temps moyen par requete",
        ["seconds_30"] = "  30S  ",
        ["name"] = "Vitesse MSP",
        ["max_query_time"] = "Temps max. par requete",
        ["help_p1"] = "Cet outil mesure la qualite de votre liaison de donnees MSP en effectuant le plus possible de grandes requetes MSP en 30 secondes.",
        ["retries"] = "Reessais",
        ["checksum_errors"] = "Erreurs de checksum",
        ["test_length"] = "Duree du test",
        ["start"] = "Demarrer",
        ["memory_free"] = "Memoire disponible",
        ["start_prompt"] = "Souhaitez-vous demarrer le test ? Choisissez la duree du test ci-dessous.",
        ["rf_protocol"] = "Protocole RF",
        ["min_query_time"] = "Temps min. par requete",
        ["seconds_120"] = "  120S  ",
        ["seconds_300"] = "  300S  ",
        ["testing"] = "Test en cours",
        ["successful_queries"] = "Requetes reussies",
        ["timeouts"] = "Timeouts",
        ["testing_performance"] = "Test des performances MSP...",
        ["total_queries"] = "Requetes totales"
      },
      ["copyprofiles"] = {
        ["profile_type"] = "Type de profil",
        ["profile_type_pid"] = "PID",
        ["profile_type_rate"] = "Taux",
        ["msgbox_save"] = "Enregistrer les parametres",
        ["name"] = "Copier les profils",
        ["help_p1"] = "Copier le profil PID ou le profil de taux depuis la source vers la destination.",
        ["dest_profile"] = "Profil destination",
        ["source_profile"] = "Profil source",
        ["msgbox_msg"] = "Enregistrer la page actuelle sur le controleur de vol ?",
        ["help_p2"] = "Choisissez la source et la destination puis enregistrez pour copier le profil."
      },
      ["esc_motors"] = {
        ["min_throttle"] = "Valeur PWM accel. 0%",
        ["tail_motor_ratio"] = "Ratio mot. queue",
        ["max_throttle"] = "Valeur PWM accel. 100%",
        ["main_motor_ratio"] = "Ratio mot. princ.",
        ["pinion"] = "Pignon",
        ["main"] = "Principal",
        ["help_p1"] = "Configurer les parametres du moteur et du controleur de vitesse.",
        ["rear"] = "Arriere",
        ["front"] = "Avant",
        ["voltage_correction"] = "Correction de tension",
        ["mincommand"] = "Valeur PWM arret moteur",
        ["name"] = "ESC/Moteurs",
        ["motor_pole_count"] = "Nombre de poles moteur",
        ["current_correction"] = "Correction du courant",
        ["consumption_correction"] = "Correction de consommation"
      },
      ["radio_config"] = {
        ["deflection"] = "Deviation",
        ["max_throttle"] = "Max",
        ["stick"] = "Manche",
        ["arming"] = "Armement",
        ["yaw_deadband"] = "Lacet",
        ["cyclic"] = "Cyclique",
        ["name"] = "Config Radio",
        ["help_p1"] = "Configurez les parametres de votre radio. Centre du manche, armement, maintien des gaz et coupure des gaz.",
        ["min_throttle"] = "Min",
        ["throttle"] = "Gaz",
        ["deadband"] = "Zone morte",
        ["center"] = "Centre"
      },
      ["profile_select"] = {
        ["help_p1"] = "Definissez le profil de vol ou le profil de taux actuel que vous souhaitez utiliser.",
        ["rate_profile"] = "Profil de taux",
        ["pid_profile"] = "Profil PID",
        ["save_prompt"] = "Enregistrer la page actuelle dans le controleur de vol ?",
        ["save_prompt_local"] = "Enregistrer les parametres actuels?",
        ["cancel"] = "ANNULER",
        ["name"] = "Selectionner le profil",
        ["save_settings"] = "Enregistrer les parametres",
        ["ok"] = "OK",
        ["help_p2"] = "Si vous utilisez un interrupteur sur votre radio pour changer les modes de vol ou de taux, cela remplacera ce choix des que vous basculerez l'interrupteur."
      },
      ["profile_governor"] = {
        ["tail_torque_assist"] = "Assist. au Couple d'AC",
        ["p"] = "P",
        ["i"] = "I",
        ["yaw"] = "Lacet",
        ["cyc"] = "Cycl.",
        ["f"] = "F",
        ["name"] = "Gouverneur",
        ["d"] = "D",
        ["help_p1"] = "Regime rotor max : Regime cible lorsque l'entree des gaz est a 100 %.",
        ["help_p6"] = "Assistance couple d'anticouple : Pour rotors anticouple motorises. Gain et limite de l'augmentation du regime lors de l'utilisation du couple du rotor principal pour l'assistance en lacet.",
        ["help_p4"] = "Precompensation : Gain de precompensation du gouverneur pour les commandes de lacet, cyclique et collectif.",
        ["max_throttle"] = "Gaz max",
        ["full_headspeed"] = "Regime rotor max",
        ["precomp"] = "Precompensation",
        ["gain"] = "Gain PID principal",
        ["disabled_message"] = "Le gouverneur Rotorflight n'est pas active",
        ["help_p3"] = "Gains : Reglages fins du gouverneur.",
        ["col"] = "Pas",
        ["min_throttle"] = "Gaz min",
        ["tta_limit"] = "Limite",
        ["help_p2"] = "Gain PID principal : Intensite du maintien du regime par le gouverneur.",
        ["gains"] = "Gains",
        ["help_p5"] = "Gaz max : Pourcentage maximal autorise des gaz pour le gouverneur.",
        ["tta_gain"] = "Gain"
      },
      ["profile_tailrotor"] = {
        ["gain"] = "Gain",
        ["help_p4"] = "Gain FeedFoward collectif : Precompensation de la queue pour les entrees collectives.",
        ["collective_impulse_ff"] = "Impulsion collective FeedFoward",
        ["help_p2"] = "Coupure de precompensation : Limite de frequence pour toutes les actions de precompensation de lacet.",
        ["cutoff"] = "Coupure",
        ["help_p3"] = "Gain FeedFoward cyclique : Precompensation de la queue pour les entrees cycliques.",
        ["help_p1"] = "Gain d'arret de lacet : Un gain d'arret plus eleve rendra l'arret de la queue plus agressif mais peut provoquer des oscillations s'il est trop eleve. Ajustez Horaire ou Anti-Horaire pour rendre les arrets de lacet uniformes.",
        ["inertia_precomp"] = "Precomp. d'inertie",
        ["cyclic_ff_gain"] = "Gain FeedFoward cyclique",
        ["help_p5"] = "Impulsion collective FeedFoward : Precompensation d'impulsion de la queue pour les entrees collectives. Si vous avez besoin d'une precompensation supplementaire de la queue au debut de l'entree collective.",
        ["cw"] = "Horaire",
        ["ccw"] = "Anti-Hor.",
        ["yaw_stop_gain"] = "Gain d'arret de lacet",
        ["precomp_cutoff"] = "Coupure de precompensation",
        ["collective_ff_gain"] = "Gain FeedFoward collectif",
        ["name"] = "Rotor de queue",
        ["decay"] = "Decroissance"
      },
      ["profile_pidcontroller"] = {
        ["help_p4"] = "Rotation d'erreur : Permettre aux erreurs d'etre partagees entre tous les axes.",
        ["ground_error_decay"] = "Decrois. d'erreur au sol",
        ["yaw"] = "Lct",
        ["inflight_error_decay"] = "Decrois. d'erreur en vol",
        ["help_p2"] = "Limite d'erreur : Limite d'angle pour I-term.",
        ["error_limit"] = "Limite d'erreur",
        ["help_p3"] = "Limite de decalage : Limite d'angle pour l'integrale a haute vitesse (O-term).",
        ["cutoff_point"] = "Point de coupure",
        ["limit"] = "Lmt",
        ["iterm_relax"] = "Relaxation I-term",
        ["hsi_offset_limit"] = "Limite de decalage HSI",
        ["pitch"] = "Tng",
        ["name"] = "Controleur PID",
        ["error_rotation"] = "Rotation d'erreur",
        ["roll"] = "Rol",
        ["help_p5"] = "Relaxation I-term : Limiter l'accumulation de I-term lors des mouvements rapides - aide a reduire le rebond apres des mouvements rapides du manche. Doit generalement etre plus faible pour les grands helicopteres et peut etre plus eleve pour les petits helicopteres. Il est preferable de ne reduire que ce qui est necessaire pour votre style de vol.",
        ["time"] = "Tmp",
        ["help_p1"] = "Decroissance d'erreur au sol : Decroissance PID pour aider a empecher l'helicoptere de basculer lorsqu'il est au sol."
      },
      ["logs"] = {
        ["help_logs_p2"] = "Les journaux necessitent une analyse reguliere pour surveiller le comportement de vol.",
        ["name"] = "Journaux",
        ["help_logs_p1"] = "Veuillez selectionner un fichier journal dans la liste ci-dessous.",
        ["msg_no_logs_found"] = "AUCUN FICHIER JOURNAL TROUVE",
        ["help_logs_tool_p1"] = "Veuillez utiliser le curseur pour naviguer dans le graphique.",
        ["help_logs_p3"] = "- Status Actuel d'Armement, Tension Actuelle, Tours Moteurs, Courant Amp, Temperature ESC"
      },
      ["battery"] = {
        ["calcfuel_local"] = "Calculate fuel using",
        ["max_cell_voltage"] = "Tension max par cellule",
        ["full_cell_voltage"] = "Tension pleine de cellule",
        ["name"] = "Batterie",
        ["min_cell_voltage"] = "Tension min cellule",
        ["help_p1"] = "Les parametres de la batterie sont utilises pour configurer le controleur de vol afin de surveiller la tension de la batterie et fournir des avertissements lorsque la tension descend en dessous d'un certain niveau.",
        ["battery_capacity"] = "Capacite de la batterie",
        ["warn_cell_voltage"] = "Tension d'alerte de cellule",
        ["cell_count"] = "Nombre de cellules",
        ["consumption_warning_percentage"] = "Avert. conso %",
        ["timer"] = "Temps de vol",
        ["voltage_multiplier"] = "Compensation de chute",
        ["kalman_multiplier"] = "Filter compensation",
        ["alert_type"] = "BEC or Rx Batt Voltage Alert",
        ["bec_voltage_alert"] = "BEC Alert Value",
        ["rx_voltage_alert"] = "RX Batt Alert Value"
      },
      ["profile_mainrotor"] = {
        ["gain"] = "Gain",
        ["help_p4"] = "Frequence limite couplage croise : Limite frequentielle de la compensation, une valeur plus elevee accelere l'action compensatrice.",
        ["collective_pitch_comp_short"] = "Comp. pas collectif",
        ["cyclic_cross_coupling"] = "Couplage cyclique croise",
        ["collective_pitch_comp"] = "Compensation pas collectif",
        ["name"] = "Rotor principal",
        ["cutoff"] = "Freq. limite",
        ["ratio"] = "Ratio",
        ["help_p1"] = "Compensation pas collectif : Une valeur plus elevee compense mieux le mouvement de tangage dû a la traînee de l'anticouple lors des montees.",
        ["help_p2"] = "Gain couplage croise : Reduit le couplage en roulis lorsque seule la profondeur est utilisee.",
        ["help_p3"] = "Ratio couplage croise : Quantite de compensation (tangage vs roulis) appliquee."
      },
      ["sbusout"] = {
        ["title"] = "Sortie SBUS",
        ["help_fields_source"] = "ID de la source pour le mixage, comptant de 0 a 15.",
        ["help_default_p4"] = "- Pour les moteurs, utilisez 0, 1000.",
        ["ch_prefix"] = "CH",
        ["channel_prefix"] = "CANAL ",
        ["saving"] = "Enregistrement",
        ["name"] = "SBUS Sortie",
        ["channel_page"] = "Sbus sortie / CH",
        ["receiver"] = "Recepteur",
        ["servo"] = "Servo",
        ["type"] = "Type",
        ["saving_data"] = "Enregistrement des donnees...",
        ["help_fields_max"] = "La valeur PWM maximale a envoyer",
        ["motor"] = "Moteur",
        ["help_default_p5"] = "- Ou vous pouvez personnaliser votre propre mappage.",
        ["help_default_p1"] = "Configurez le mixage avance et le mappage des canaux si vous avez active SBUS Out sur un port serie.",
        ["max"] = "Max",
        ["save_prompt"] = "Enregistrer la page actuelle dans le controleur de vol ?",
        ["help_fields_min"] = "La valeur PWM minimale a envoyer.",
        ["mixer"] = "Mixeur",
        ["ok"] = "OK",
        ["cancel"] = "ANNULER",
        ["help_default_p2"] = "- Pour les canaux RX ou les servos (large bande), utilisez 1000, 2000 ou 500,1000 pour les servos a bande etroite.",
        ["save_settings"] = "Enregistrer les parametres",
        ["min"] = "Min",
        ["help_default_p3"] = "- Pour les regles de mixage, utilisez -1000, 1000.",
        ["source"] = "Source"
      },
      ["profile_rescue"] = {
        ["help_p4"] = "Stationnaire : Combien de collectif pour maintenir un vol stationnaire stable.",
        ["hover"] = "Stationnaire",
        ["collective"] = "Collectif",
        ["help_p2"] = "Redresser : Combien de collectif et pendant combien de temps pour arreter la chute.",
        ["climb"] = "Montee",
        ["mode_enable"] = "Activer le mode secours",
        ["help_p3"] = "Montee : Combien de collectif pour maintenir une montee reguliere - et combien de temps.",
        ["help_p1"] = "Retourner a l'endroit : Retourner l'helicoptere a l'endroit lorsque le mode secours est active.",
        ["flip_upright"] = "Retourner a l'endroit",
        ["flip"] = "Renvers.",
        ["level_gain"] = "Niveau",
        ["name"] = "Secours",
        ["exit_time"] = "Transit.",
        ["help_p5"] = "Retourner : Combien de temps attendre avant d'abandonner parce que le retournement n'a pas fonctionne.",
        ["help_p6"] = "Gains : Combien d'effort pour maintenir l'helicoptere a niveau lors de l'activation du mode secours.",
        ["fail_time"] = "Duree",
        ["pull_up"] = "Redresser",
        ["rate"] = "Taux",
        ["help_p7"] = "Taux et Acceleration : Taux de rotation et d'acceleration maximum lors de la mise a niveau pendant le secours.",
        ["gains"] = "Gains",
        ["time"] = "Temps",
        ["accel"] = "Acceleration"
      },
      ["trim"] = {
        ["disable_mixer_message"] = "Rendre le controle des servos au controleur de vol.",
        ["tail_motor_idle"] = "Ralenti moteur de queue %",
        ["disable_mixer_override"] = "Desactiver le remplacement du mixeur",
        ["yaw_trim"] = "Trim lacet %",
        ["enable_mixer_message"] = "Reglez tous les servos a leur position centrale configuree. \r\n\r\n Cela entrainera l'enregistrement de toutes les valeurs de cette page lors du reglage du trim du servo.",
        ["mixer_override_disabling"] = "Desactivation du remplacement du mixeur...",
        ["roll_trim"] = "Trim roulis %",
        ["pitch_trim"] = "Trim tangage %",
        ["name"] = "Trim",
        ["help_p2"] = "Queue motorisee : Si vous utilisez une queue motorisee, utilisez ceci pour regler la vitesse minimale de ralenti et le zero lacet.",
        ["mixer_override"] = "Remplacement mixeur",
        ["mixer_override_enabling"] = "Activation du remplacement du mixeur...",
        ["enable_mixer_override"] = "Activer le remplacement du mixeur",
        ["collective_trim"] = "Trim collectif %",
        ["help_p1"] = "Lier les trims : Utilisez pour ajuster les petits problemes de nivellement dans votre plateau cyclique. A utilise uniquement si les liens du plateau cyclique ne sont pas ajustables."
      },
      ["governor"] = {
        ["help_p1"] = "Ces parametres s'appliquent globalement au gouverneur, quel que soit le profil utilise.",
        ["handover_throttle"] = "Transfert des gaz %",
        ["spoolup_min_throttle"] = "Gaz min. de demarrage %",
        ["recovery_time"] = "Temps de recuperation",
        ["mode"] = "Mode",
        ["help_p2"] = "Chaque paramtre est simplement une valeur de temps en secondes pour chaque action du gouverneur.",
        ["tracking_time"] = "Temps de suivi",
        ["name"] = "Gouverneur",
        ["startup_time"] = "Temps de demarrage",
        ["spoolup_time"] = "Temps de monte en regime"
      },
      ["accelerometer"] = {
        ["help_p1"] = "L'accelerometre est utilise pour mesurer l'angle du controleur de vol par rapport a l'horizon. Ces donnees sont utilisees pour stabiliser l'aeronef et fournir une fonctionnalite d'auto-nivellement.",
        ["name"] = "Accelerometre",
        ["pitch"] = "Tangage",
        ["msg_calibrate"] = "Calibrer l'accelerometre ?",
        ["roll"] = "Roulis"
      },
      ["gyro_alignment"] = {
        ["name"] = "Alignement de la Carte",
        ["board_alignment"] = "Alignement de la Carte",
        ["roll"] = "Roulis",
        ["pitch"] = "Tangage",
        ["yaw"] = "Lacet",
        ["msg_calibrate"] = "Calibrer l'accelerometre ? Cela reinitialise l'accelerometre et applique les parametres d'alignement actuels de la carte.",
        ["help_p1"] = "L'outil d'alignement de la carte permet de configurer les angles d'alignement de la carte pour compenser l'orientation de montage du controleur de vol.",
        ["help_p2"] = "Alignement de la Carte : Ajustez les angles de roulis, tangage et lacet (en degres) pour correspondre a l'orientation physique de votre controleur de vol par rapport au chassis de l'helicoptere.",
        ["help_p3"] = "Ces parametres compensent les cas ou le controleur de vol n'est pas monte parfaitement aligne avec les axes du chassis de l'helicoptere.",
        ["help_p4"] = "Utilisez le bouton Outil pour calibrer l'accelerometre apres avoir effectue des modifications d'alignement. Sauvegardez les parametres dans l'EEPROM une fois termine."
      },
      ["rates"] = {
        ["help_table_5_p2"] = "Taux Max : Taux de rotation maximal a pleine deviation du manche en degres par seconde.",
        ["actual"] = "ACTUEL",
        ["max_rate"] = "Taux Max",
        ["help_table_4_p3"] = "Expo : Reduit la sensibilite pres du centre du manche ou des controles fins sont necessaires.",
        ["rate"] = "Taux",
        ["help_table_5_p1"] = "Taux RC : Utilisez pour reduire la sensibilite autour du centre du manche. Le Taux RC regle a la moitie du Taux Max est lineaire. Un nombre inferieur reduira la sensibilite autour du centre du manche. Superieur a la moitie du Taux Max augmentera egalement le Taux Max.",
        ["help_table_4_p2"] = "Taux Max : Taux de rotation maximal a pleine deviation du manche en degres par seconde.",
        ["center_sensitivity"] = "Sens. Centre",
        ["rc_curve"] = "Courbe RC",
        ["roll"] = "Roulis",
        ["none"] = "AUCUN",
        ["betaflight"] = "BETAFLIGHT",
        ["kiss"] = "KISS",
        ["help_table_1_p3"] = "Expo : Reduit la sensibilite pres du centre du manche ou des controles fins sont necessaires.",
        ["help_table_3_p2"] = "Taux : Augmente le taux de rotation maximal tout en reduisant la sensibilite autour de la moitie du manche.",
        ["help_table_2_p2"] = "Acro+ : Augmente le taux de rotation maximal tout en reduisant la sensibilite autour de la moitie du manche.",
        ["superrate"] = "SuperTaux",
        ["help_table_2_p3"] = "Expo : Reduit la sensibilite pres du centre du manche ou des controles fins sont necessaires.",
        ["raceflight"] = "RACEFLIGHT",
        ["yaw"] = "Lacet",
        ["collective"] = "Collectif",
        ["name"] = "Taux",
        ["help_table_5_p3"] = "Expo : Reduit la sensibilite pres du centre du manche ou des controles fins sont necessaires.",
        ["help_table_3_p3"] = "Courbe RC : Reduit la sensibilite pres du centre du manche ou des controles fins sont necessaires.",
        ["expo"] = "Expo",
        ["help_table_1_p2"] = "SuperTaux : Augmente le taux de rotation maximal tout en reduisant la sensibilite autour de la moitie du manche.",
        ["help_default_p2"] = "Nous utiliserons les sous-cles ci-dessous.",
        ["help_default_p1"] = "Par defaut : Nous gardons cela pour faire apparaitre le bouton pour les taux.",
        ["quick"] = "RAPIDE",
        ["pitch"] = "Tangage",
        ["acroplus"] = "Acro+",
        ["help_table_1_p1"] = "Taux RC : Taux de rotation maximal a pleine deviation du manche.",
        ["rc_rate"] = "Taux RC",
        ["help_table_2_p1"] = "Taux : Taux de rotation maximal a pleine deviation du manche en degres par seconde.",
        ["help_table_4_p1"] = "Sensibilite Centre : Utilisez pour reduire la sensibilite autour du centre du manche. Reglez la Sensibilite Centre a la meme valeur que le Taux Max pour une reponse lineaire. Un nombre inferieur au Taux Max reduira la sensibilite autour du centre du manche. Notez qu'un nombre superieur au Taux Max augmentera le Taux Max - non recommande car cela cause des problemes dans le journal Blackbox.",
        ["help_table_0_p1"] = "Toutes les valeurs sont definies a zero car aucune TABLE DE TAUX n'est utilisee.",
        ["help_table_3_p1"] = "Taux RC : Taux de rotation maximal a pleine deviation du manche."
      },
      ["mixer"] = {
        ["help_p1"] = "Ajustez la geometrie du plateau cyclique, les angles de phase et les limites. TTA : Precompensation pour l'aide a l'anticouple",
        ["collective_tilt_correction_pos"] = "Positive",
        ["geo_correction"] = "Correction Geometrique",
        ["swash_tta_precomp"] = "Precomp. TTA",
        ["name"] = "Mixeur",
        ["collective_tilt_correction_neg"] = "Negative",
        ["tail_motor_idle"] = "Ralenti Queue %",
        ["swash_phase"] = "Angle de Phase",
        ["collective_tilt_correction"] = "Correction Inclinaison Collective",
        ["swash_pitch_limit"] = "Limite Totale du Pas"
      },
      ["about"] = {
        ["help_p1"] = "Cette page fournit des informations utiles que vous pourriez devoir fournir lors d'une demande d'assistance.",
        ["msgbox_credits"] = "Credits",
        ["ethos_version"] = "Version Ethos",
        ["rf_version"] = "Version Rotorflight",
        ["fc_version"] = "Version FC",
        ["name"] = "A propos",
        ["supported_versions"] = "Versions MSP supportes",
        ["license"] = "Vous pouvez copier, distribuer et modifier le logiciel tant que vous suivez les modifications et les dates dans les fichiers sources. Toute modification ou tout logiciel incluant du code sous licence GPL (via le compilateur) doit egalement etre mis a disposition sous la GPL avec les instructions de compilation et d'installation.",
        ["simulation"] = "Simulation",
        ["help_p2"] = "Pour toute assistance, veuillez d'abord lire les pages d'aide sur www.rotorflight.org",
        ["opener"] = "Rotorflight est un projet open source. Toute contribution de personnes partageant le meme etat d'esprit, desireuses d'ameliorer encore ce logiciel, est la bienvenue et encouragee. Vous n'avez pas besoin d'etre un programmeur experimente pour aider.",
        ["version"] = "Version",
        ["msp_version"] = "Version MSP",
        ["credits"] = "Les contributeurs notables au firmware Rotorflight et a ce logiciel sont : Petri Mattila, Egon Lubbers, Rob Thomson, Rob Gayle, Phil Kaighin, Robert Burrow, Keith Williams, Bertrand Songis, Venbs Zhou... et bien d'autres qui ont passe des heures a tester et fournir des retours !",
        ["msp_transport"] = "Transport MSP"
      },
      ["rates_advanced"] = {
        ["dyn_ceiling_gain"] = "Gain plafond dynamique",
        ["acc_limit"] = "Limite d'acceleration",
        ["roll"] = "Roulis",
        ["yaw_dynamics"] = "Dynamique de lacet",
        ["pitch"] = "Tangage",
        ["col"] = "Collectif",
        ["setpoint_boost_cutoff"] = "Seuil consigne",
        ["yaw_dynamic_deadband_gain"] = "B. morte",
        ["rates_type"] = "Type de taux",
        ["setpoint_boost_gain"] = "Gain consigne",
        ["msg_reset_to_defaults"] = "Type de taux change. Les valeurs seront reinitialisees par defaut.",
        ["yaw_dynamic_ceiling_gain"] = "Plafond",
        ["yaw_boost"] = "Boost de lacet",
        ["gain"] = "Gain",
        ["rate_table"] = "Table des vitesses",
        ["dynamics"] = "Dynamique",
        ["yaw"] = "Lacet",
        ["yaw_dynamic_deadband_filter"] = "Filtre",
        ["name"] = "Taux",
        ["cutoff"] = "Seuil",
        ["help_rate_table"] = "Selectionnez la vitesse desiree. L'enregistrement applique ce choix au profil actif.",
        ["help_p1"] = "Type de taux : Choisissez le type de taux avec lequel vous preferez voler. Raceflight et Actual sont les plus simples.",
        ["pitch_boost"] = "Boost de tangage",
        ["help_p2"] = "Dynamique : Applique independamment du type de taux. Typiquement laisse par defaut mais peut etre ajuste pour lisser les mouvements de l'helicoptere, comme avec les helicopteres de maquette.",
        ["accel_limit"] = "Accel",
        ["dyn_deadband_filter"] = "Filtre zone morte dynamique",
        ["roll_boost"] = "Boost de roulis",
        ["dyn_deadband_gain"] = "Gain zone morte dynamique",
        ["collective_dynamics"] = "Dynamique de pas",
        ["roll_dynamics"] = "Dynamique de roulis",
        ["collective_boost"] = "Boost collectif",
        ["pitch_dynamics"] = "Dynamique de prof.",
        ["response_time"] = "Temps de reponse"
      },
      ["servos"] = {
        ["tbl_yes"] = "OUI",
        ["enable_servo_override"] = "Activer remplacement servo",
        ["disabling_servo_override"] = "Desactivation du remplacement servo...",
        ["help_tool_p3"] = "Minimum/Maximum : Ajuster les points de fin du servo selectionne.",
        ["tail"] = "QUEUE",
        ["scale_negative"] = "Echelle Negative",
        ["help_tool_p1"] = "Remplacement : [*] Activer le remplacement pour permettre les mises a jour en temps reel du point central du servo.",
        ["tbl_no"] = "NON",
        ["maximum"] = "Maximum",
        ["help_tool_p6"] = "Vitesse : La vitesse de deplacement du servo. Generalement utilise uniquement pour les servos cycliques pour aider le plateau cyclique a se deplacer uniformement. Optionnel - laisser tous a 0 si incertain.",
        ["help_fields_rate"] = "Taux PWM du servo.",
        ["cyc_pitch"] = "CYC. TANGAGE",
        ["center"] = "Centre",
        ["minimum"] = "Minimum",
        ["speed"] = "Vitesse",
        ["help_fields_speed"] = "Vitesse de mouvement du servo en millisecondes.",
        ["disable_servo_override"] = "Desactiver remplacement servo",
        ["help_fields_scale_pos"] = "Echelle positive du servo.",
        ["saving_data"] = "Enregistrement des donnees...",
        ["cyc_left"] = "CYC. GAUCHE",
        ["saving"] = "Enregistrement",
        ["name"] = "Servos",
        ["help_tool_p5"] = "Taux : La frequence a laquelle le servo fonctionne le mieux - verifier avec le fabricant.",
        ["help_tool_p2"] = "Centre : Ajuster la position centrale du servo.",
        ["enabling_servo_override"] = "Activation du remplacement servo...",
        ["servo_prefix"] = "SERVO ",
        ["reverse"] = "Inversement",
        ["enable_servo_override_msg"] = "Le remplacement du servo vous permet de 'regler' le point central de votre servo en temps reel.",
        ["cyc_right"] = "CYC. DROITE",
        ["help_default_p2"] = "Les commandes de vol principales qui utilisent le mixeur rotoflight s'afficheront dans la section appelee 'mixeur'.",
        ["scale_positive"] = "Echelle Positive",
        ["help_default_p1"] = "Veuillez selectionner le servo que vous souhaitez configurer dans la liste ci-dessous.",
        ["servo_override"] = "Remplacement Servo",
        ["disable_servo_override_msg"] = "Rendre le controle des servos au controleur de vol.",
        ["help_fields_min"] = "Limite de deplacement negatif du servo.",
        ["help_default_p3"] = "Tous les autres servos qui ne sont pas controles par le mixeur de vol principal s'afficheront dans la section appelee 'Autres servos'.",
        ["help_fields_mid"] = "Largeur d'impulsion de la position centrale du servo.",
        ["help_fields_scale_neg"] = "Echelle negative du servo.",
        ["rate"] = "Taux",
        ["help_tool_p4"] = "Echelle : Ajuster la quantite de mouvement du servo pour une entree donnee.",
        ["help_fields_flags"] = "0 = Par defaut, 1=Inverser, 2 = Correction Geometrique, 3 = Inverser + Correction Geometrique",
        ["geometry"] = "Geometrie",
        ["help_fields_max"] = "Limite de deplacement positif du servo."
      },
      ["profile_autolevel"] = {
        ["acro_trainer"] = "Entrainement Acro",
        ["angle_mode"] = "Mode Angle",
        ["max"] = "Max",
        ["name"] = "Auto-niveau",
        ["help_p1"] = "Entrainement Acro : Intensite avec laquelle l'helicoptere revient a l'horizontale en mode entrainement Acro.",
        ["horizon_mode"] = "Mode Horizon",
        ["gain"] = "Gain",
        ["help_p2"] = "Mode Angle : Intensite avec laquelle l'helicoptere revient a l'horizontale en mode Angle.",
        ["help_p3"] = "Mode Horizon : Intensite avec laquelle l'helicoptere revient a l'horizontale en mode Horizon."
      },
      ["filters"] = {
        ["filter_type"] = "Type de filtre",
        ["help_p4"] = "Les Filtreurs elimine une plage de frequences specifiques.",
        ["notch_c"] = "Nbrs filtres",
        ["rpm_preset"] = "Type",
        ["lowpass_1"] = "Passe-bas 1",
        ["rpm_min_hz"] = "Frequence minimum",
        ["help_p2"] = "Le filtre passe-bas elimine les frequences elevees indesirables.",
        ["cutoff"] = "Coupure",
        ["notch_1"] = "Filtreur 1",
        ["max_cutoff"] = "Coupure max",
        ["help_p3"] = "Choisissez soigneusement les frequences minimales et maximales pour optimiser les performances.",
        ["lowpass_2"] = "Passe-bas 2",
        ["rpm_filter"] = "Filtre Tours Moteurs",
        ["help_p1"] = "Normalement, vous ne devriez pas modifier cette page sans verifier vos journaux Blackbox !",
        ["dyn_notch"] = "Filtres dynamique",
        ["notch_q"] = "Facteurs",
        ["lowpass_1_dyn"] = "Passe-bas 1 dyn.",
        ["notch_min_hz"] = "Min",
        ["notch_max_hz"] = "Max",
        ["notch_2"] = "Filtreur 2",
        ["name"] = "Filtres",
        ["min_cutoff"] = "Coupure min",
        ["center"] = "Centre"
      },
      ["status"] = {
        ["arming_disable_flag_3"] = "Mauvaise recuperation RX",
        ["arming_disable_flag_20"] = "Filtre RPM",
        ["arming_disable_flag_11"] = "Charge",
        ["arming_disable_flag_22"] = "DSHOT Bitbang",
        ["dataflash_free_space"] = "Espace libre Dataflash",
        ["arming_disable_flag_25"] = "Interrupteur d'armement",
        ["erasing"] = "Effacement",
        ["arming_disable_flag_9"] = "Temps de grace au demarrage",
        ["megabyte"] = "MB",
        ["arming_disable_flag_17"] = "Paralyser",
        ["arming_disable_flag_5"] = "Gouverneur",
        ["arming_disable_flag_8"] = "Angle",
        ["arming_disable_flag_1"] = "Fail Safe",
        ["cpu_load"] = "Charge CPU",
        ["arming_disable_flag_15"] = "BST",
        ["arming_disable_flag_12"] = "Calibration",
        ["arming_disable_flag_19"] = "Secours",
        ["arming_disable_flag_4"] = "Box Fail Safe",
        ["arming_disable_flag_24"] = "Protocole moteur",
        ["real_time_load"] = "Charge en temps reel",
        ["help_p2"] = "Pour effacer le dataflash pour plus de stockage de fichiers journaux, appuyez sur le bouton du menu indique par un '*'.",
        ["arming_disable_flag_2"] = "RX Fail Safe",
        ["ok"] = "OK",
        ["arming_disable_flag_0"] = "Pas de Gyro",
        ["arming_disable_flag_18"] = "GPS",
        ["help_p1"] = "Utilisez cette page pour voir l'etat actuel de votre controleur de vol. Cela peut etre utile pour determiner pourquoi votre helicoptere ne s'arme pas.",
        ["arming_flags"] = "Drapeaux d'armement",
        ["unsupported"] = "Non supporte",
        ["erase_prompt"] = "Voulez-vous effacer le dataflash ?",
        ["erase"] = "Effacer",
        ["arming_disable_flag_10"] = "Pas de pre-armement",
        ["arming_disable_flag_21"] = "Redemarrage requis",
        ["name"] = "Statut",
        ["arming_disable_flag_13"] = "CLI",
        ["arming_disable_flag_14"] = "Menu CMS",
        ["arming_disable_flag_16"] = "MSP",
        ["arming_disable_flag_7"] = "Gaz",
        ["erasing_dataflash"] = "Effacement du dataflash...",
        ["arming_disable_flag_23"] = "Calibration Acc"
      },
      ["profile_pidbandwidth"] = {
        ["help_p1"] = "Bande passante PID : Bande passante globale en Hz utilisee par la boucle PID.",
        ["pitch"] = "P",
        ["yaw"] = "Y",
        ["name"] = "Bande passante PID",
        ["bterm_cutoff"] = "Freq. de coupure B-term",
        ["help_p3"] = "Frequence de coupure B-term : Frequence de coupure B-term en Hz.",
        ["dterm_cutoff"] = "Freq. de coupure D-term",
        ["help_p2"] = "Frequence de coupure D-term : Frequence de coupure D-term en Hz.",
        ["roll"] = "R"
      }
    },
    ["navigation_save"] = "ENREG.",
    ["menu_section_flight_tuning"] = "Reglage du vol",
    ["error_timed_out"] = "Erreur : le delai est depasse",
    ["check_rf_module_on"] = "Veuillez verifier que votre module RF est active.",
    ["msg_saving"] = "Enregistrement...",
    ["msg_save_not_commited"] = "Enregistrement non confirme dans l'EEPROM",
    ["menu_section_advanced"] = "Avance",
    ["msg_loading_from_fbl"] = "Chargement des donnees du controleur de vol...",
    ["msg_reload_settings"] = "Recharger les donnees du controleur de vol ?",
    ["menu_section_tools"] = "Outils",
    ["msg_connecting"] = "Connexion",
    ["msg_save_current_page"] = "Enregistrer la page actuelle dans le controleur de vol ?",
    ["btn_ok_long"] = "                OK                ",
    ["check_discovered_sensors"] = "Veuillez verifier que tous les capteurs ont ete decouverts.",
    ["msg_loading"] = "Chargement...",
    ["check_heli_on"] = "Veuillez verifier que votre helicoptere est sous tension et connecte a la radio.",
    ["check_bg_task"] = "Veuillez activer la tache en arriere-plan.",
    ["navigation_tools"] = "*",
    ["check_supported_version"] = "Cette version du script Lua ne peut pas etre utilisee avec le modele selectionne."
  },
  ["telemetry"] = {
    ["sensors"] = {
      ["attpitch"] = "Angle Prof.",
      ["attroll"] = "Angle Roll",
      ["attyaw"] = "Angle Lacet",
      ["accx"] = "Accel X",
      ["accy"] = "Accel Z",
      ["accz"] = "Accel Z",
      ["groundspeed"] = "Vitesse Sol",
      ["esc_temp"] = "Temperature ESC",
      ["rate_profile"] = "Profile de Rates",
      ["headspeed"] = "Tours Moteurs",
      ["altitude"] = "Altitude",
      ["voltage"] = "Tension",
      ["bec_voltage"] = "Tension BEC",
      ["cell_count"] = "Nombre de cellules",
      ["governor"] = "Status du Gouverneur",
      ["adj_func"] = "Ajustements Fonctions",
      ["fuel"] = "Niveau de Carburant",
      ["smartfuel"] = "Smart Fuel",
      ["rssi"] = "Signal du Recepteur",
      ["link"] = "Qualité du Lien",
      ["adj_val"] = "Ajustements Valeurs",
      ["arming_flags"] = "Drapeaux Armement",
      ["current"] = "Courant",
      ["throttle_pct"] = "Pourcentage de Gaz",
      ["consumption"] = "Consommation",
      ["smartconsumption"] = "Consommation Intelligente",
      ["pid_profile"] = "Profile de PID",
      ["mcu_temp"] = "Temperature Micro Processeur",
      ["armdisableflags"] = "Desactivation Armement"
    }
  },
  ["widgets"] = {
    ["bbl"] = {
      ["erase_dataflash"] = "Effacer la mémoire flash",
      ["erasing"] = "Effacement...",
      ["display"] = "Affichage",
      ["display_free"] = "Libre",
      ["display_used"] = "Utilisé",
      ["display_outof"] = "Utilisé/Total"
    },
    ["craftimage"] = {
    },
    ["craftname"] = {
      ["txt_enter_craft_name"] = "Entrez le nom de l'appareil",
      ["title"] = "NOM DE L'APPAREIL",
      ["txt_cancel"] = "Annuler",
      ["txt_save"] = "Enregistrer"
    },
    ["dashboard"] = {
      ["theme_load_error"] = "Votre theme ne s'est pas charge correctement. Retour au theme par defaut.",
      ["validate_sensors"] = "VEUILLEZ VERIFIER LES CAPTEURS",
      ["unsupported_resolution"] = "TROP PETIT",
      ["loading"] = "ROTORFLIGHT",
      ["waiting_for_connection"] = "CONNECTION",
      ["check_bg_task"] = "TACHE ARRIERE-PLAN",
      ["check_rf_module_on"] = "MODULE RF",
      ["check_discovered_sensors"] = "CAPTEURS",
      ["no_link"] = "AUCUNE CONNECTION",
      ["reset_flight"] = "Reinitialiser vol",
      ["reset_flight_ask_title"] = "Reinitialiser vol",
      ["reset_flight_ask_text"] = "Etes-vous sur de vouloir reinitialiser le vol?",
      ["voltage"] = "Tension",
      ["fuel"] = "Carburant",
      ["headspeed"] = "Vitesse Rotor",
      ["max"] = "Max",
      ["min"] = "Min",
      ["bec_voltage"] = "Tension BEC",
      ["esc_temp"] = "Temp. ESC",
      ["flight_duration"] = "Duree vol",
      ["total_flight_duration"] = "Duree totale de vol du modele",
      ["rpm_min"] = "RPM Min",
      ["rpm_max"] = "RPM Max",
      ["throttle_max"] = "Gaz Max",
      ["current_max"] = "Courant Max",
      ["esc_max_temp"] = "Temp. ESC Max",
      ["watts_max"] = "Watts Max",
      ["consumed_mah"] = "mAh consommes",
      ["fuel_remaining"] = "Carburant restant",
      ["min_volts_cell"] = "Tension min par cellule",
      ["link_min"] = "Lien Min",
      ["governor"] = "Governeur",
      ["profile"] = "Profile",
      ["rates"] = "Rates",
      ["flights"] = "Vols",
      ["lq"] = "Qualite basse",
      ["time"] = "Temps",
      ["blackbox"] = "Blackbox",
      ["throttle"] = "Gaz",
      ["flight_time"] = "Temps de vol",
      ["rssi_min"] = "RSSI Min",
      ["current"] = "Courant",
      ["timer"] = "Minuteur",
      ["rpm"] = "Tours par Minute",
      ["min_voltage"] = "Tension Min.",
      ["max_voltage"] = "Tension Max.",
      ["min_current"] = "Courant Min.",
      ["max_current"] = "Courant Max.",
      ["max_tmcu"] = "Temp max MCU",
      ["max_emcu"] = "Temp max E.MCU",
      ["altitude"] = "Altitude",
      ["altitude_max"] = "Altitude Max",
      ["power"] = "Puissance",
      ["cell_voltage"] = "Tension Cellule",
      ["volts_per_cell"] = "VVolts par cellule",
      ["warning"] = "Avertissement",
      ["tx_batt"] = "TX Battery",
      ["link_max"] = "Link Max"
    },
    ["governor"] = {
      ["UNKNOWN"] = "INCONNU",
      ["IDLE"] = "RALENTI",
      ["DISARMED"] = "DESARMER",
      ["OFF"] = "ARRET",
      ["SPOOLUP"] = "ACCELERATION",
      ["ACTIVE"] = "ACTIF",
      ["RECOVERY"] = "RECUPERATION",
      ["THROFF"] = "COUPURE MOTEUR",
      ["LOSTHS"] = "PERTE DES TOURS",
      ["AUTOROT"] = "AUTOROTATION",
      ["DISABLED"] = "DESACTIVER",
      ["BAILOUT"] = "SAUVETAGE"
    }
  }
}
