--[[
 * Copyright (C) Rotorflight Project
 *
 * License GPLv3: https://www.gnu.org/licenses/gpl-3.0.en.html
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * Please do not edit these files directly. These files are generated
 * from JSON files contained within the folder:
 *
 *   bin/i18n/json/*.json
 *
 * Running the script:
 *
 *   lua54 json-to-lua.lua
 *
 * will create these files for you.
]]

return {
  ["reload"] = "Neu laden",
  ["image"] = "Bild",
  ["error"] = "Fehler",
  ["save"] = "Speichern",
  ["ethos"] = "Ethos",
  ["version"] = "Version",
  ["bg_task_disabled"] = "Hintergrundtask deaktiviert",
  ["no_link"] = "Keine Verbindung",
  ["background_task_disabled"] = "Hintergrundtask deaktiviert",
  ["no_sensor"] = "kein sensor",
  ["api"] = {
    ["RC_TUNING"] = {
      ["setpoint_boost_cutoff_2"] = "Boost-Grenzwert fuer den Sollwert.",
      ["response_time_3"] = "Erhoeht oder verringert die Reaktionszeit der Rate, um die Heli-Bewegungen zu glaetten.",
      ["accel_limit_4"] = "Maximale Beschleunigung des Modells als Reaktion auf eine Knueppelbewegung.",
      ["setpoint_boost_gain_4"] = "Boost-Verstaerkung fuer den Sollwert.",
      ["yaw_dynamic_deadband_filter"] = "Der maximale Filter, der auf die dynamische Yaw-Totzone angewendet wird.",
      ["setpoint_boost_gain_3"] = "Boost-Verstaerkung fuer den Sollwert.",
      ["response_time_2"] = "Erhoeht oder verringert die Reaktionszeit der Rate, um die Heli-Bewegungen zu glaetten.",
      ["accel_limit_1"] = "Maximale Beschleunigung des Modells als Reaktion auf eine Knueppelbewegung.",
      ["setpoint_boost_cutoff_1"] = "Boost-Grenzwert fuer den Sollwert.",
      ["setpoint_boost_cutoff_4"] = "Boost-Grenzwert fuer den Sollwert.",
      ["setpoint_boost_gain_2"] = "Boost-Verstaerkung fuer den Sollwert.",
      ["accel_limit_2"] = "Maximale Beschleunigung des Modells als Reaktion auf eine Knueppelbewegung.",
      ["yaw_dynamic_deadband_gain"] = "Der maximale Gewinn, der auf die dynamische Yaw-Totzone angewendet wird.",
      ["accel_limit_3"] = "Maximale Beschleunigung des Modells als Reaktion auf eine Knueppelbewegung.",
      ["response_time_4"] = "Erhoeht oder verringert die Reaktionszeit der Rate, um die Heli-Bewegungen zu glaetten.",
      ["setpoint_boost_cutoff_3"] = "Boost-Grenzwert fuer den Sollwert.",
      ["setpoint_boost_gain_1"] = "Boost-Verstaerkung fuer den Sollwert.",
      ["yaw_dynamic_ceiling_gain"] = "Der maximale Gewinn, der auf die dynamische Yaw-Obergrenze angewendet wird.",
      ["response_time_1"] = "Erhoeht oder verringert die Reaktionszeit der Rate, um die Heli-Bewegungen zu glaetten."
    },
    ["RESCUE_PROFILE"] = {
      ["rescue_flip_gain"] = "Bestimmt, wie aggressiv der Heli waehrend der invertierten Rettung flippt.",
      ["rescue_level_gain"] = "Bestimmt, wie aggressiv der Heli sich waehrend der Rettung ausrichtet.",
      ["tbl_off"] = "AUS",
      ["rescue_hover_collective"] = "Kollektivwert fuer das Schweben.",
      ["rescue_max_setpoint_rate"] = "Begrenzt die Roll-/Nick-Rate waehrend der Rettung. Groessere Helis benoetigen moeglicherweise langsamere Drehgeschwindigkeiten.",
      ["tbl_flip"] = "FLIP",
      ["rescue_flip_mode"] = "Falls die Rettung im invertierten Zustand aktiviert wird: Aufrichten oder invertiert bleiben.",
      ["rescue_pull_up_time"] = "Wenn die Rettung aktiviert wird, wird fuer diese Dauer kollektives Steigen angewendet, bevor zum Flip- oder Steigstadium uebergegangen wird.",
      ["tbl_noflip"] = "KEIN FLIP",
      ["rescue_exit_time"] = "Begrenzt die schnelle Anwendung von negativem Kollektiv, falls der Heli sich waehrend der Rettung gedreht hat.",
      ["rescue_pull_up_collective"] = "Kollektivwert fuer das Hochziehen.",
      ["rescue_max_setpoint_accel"] = "Begrenzt, wie schnell der Heli in eine Roll-/Nick-Bewegung beschleunigt. Groessere Helis benoetigen moeglicherweise langsamere Beschleunigungswerte.",
      ["tbl_on"] = "EIN",
      ["rescue_climb_collective"] = "Kollektivwert fuer den Steigflug waehrend der Rettung.",
      ["rescue_flip_time"] = "Falls der Heli in der Rettung nicht innerhalb dieser Zeit aufrichtet, wird die Rettung abgebrochen.",
      ["rescue_climb_time"] = "Dauer des Steigflugs, bevor in den Schwebezustand gewechselt wird."
    },
    ["ESC_SENSOR_CONFIG"] = {
      ["hw4_current_offset"] = "Hobbywing v4 Strom-Offset-Anpassung",
      ["tbl_off"] = "Aus",
      ["update_hz"] = "ESC-Telemetrie-Aktualisierungsrate",
      ["half_duplex"] = "Halbduplex-Modus fuer ESC-Telemetrie",
      ["consumption_correction"] = "Anpassen der Verbrauchskorrektur",
      ["current_offset"] = "Stromsensor-Offset-Anpassung",
      ["voltage_correction"] = "Anpassen der Spannungskorrektur",
      ["hw4_voltage_gain"] = "Hobbywing v4 Spannungsverstaerkung-Anpassung",
      ["tbl_on"] = "Ein",
      ["hw4_current_gain"] = "Hobbywing v4 Stromverstaerkung-Anpassung",
      ["current_correction"] = "Anpassen der Stromkorrektur",
      ["pin_swap"] = "Tauschen der TX- und RX-Pins fuer die ESC-Telemetrie"
    },
    ["ESC_PARAMETERS_FLYROTOR"] = {
      ["tbl_alwayson"] = "Immer an",
      ["throttle_min"] = "Minimaler Gaswert",
      ["tbl_disabled"] = "Deaktiviert",
      ["tbl_auto"] = "Auto",
      ["starting_torque"] = "Anlaufmoment fuer den Motor",
      ["cell_count"] = "Anzahl der Zellen in der Batterie",
      ["motor_erpm_max"] = "Maximale RPM",
      ["throttle_max"] = "Maximaler Gaswert",
      ["tbl_ccw"] = "CCW",
      ["tbl_escgov"] = "ESC-Governor",
      ["temperature_protection"] = "Temperatur, bei der die Leistung um 50 % reduziert wird",
      ["tbl_automatic"] = "Automatisch",
      ["low_voltage_protection"] = "Spannung, bei der die Leistung um 50 % reduziert wird",
      ["tbl_cw"] = "CW",
      ["soft_start"] = "Wert fuer sanftes Anlaufen",
      ["gov_i"] = "Integralwert fuer den Governor",
      ["timing_angle"] = "Timing-Winkel fuer den Motor",
      ["response_speed"] = "Reaktionsgeschwindigkeit des Motors",
      ["current_gain"] = "Verstaerkungswert fuer den Stromsensor",
      ["tbl_extgov"] = "Externer Governor",
      ["buzzer_volume"] = "Summerlautstaerke",
      ["gov_d"] = "Differenzialwert fuer den Governor",
      ["tbl_enabled"] = "Aktiviert",
      ["gov_p"] = "Proportionalwert fuer den Governor"
    },
    ["ESC_PARAMETERS_YGE"] = {
      ["tbl_alwayson"] = "Immer an",
      ["tbl_off"] = "Aus",
      ["tbl_modestore"] = "Heli Governor Speicher",
      ["tbl_modefree"] = "Frei (Achtung!)",
      ["tbl_modeglider"] = "Segelflugzeug",
      ["tbl_modeext"] = "Heli Externer Governor",
      ["tbl_modeheli"] = "Heli Governor",
      ["tbl_medium"] = "Mittel",
      ["tbl_autonorm"] = "Automatisch Normal",
      ["tbl_reverse"] = "Umgekehrt",
      ["tbl_modef3a"] = "Luftfahrt F3A",
      ["tbl_auto"] = "Automatisch",
      ["tbl_slowdown"] = "Verlangsamen",
      ["tbl_slow"] = "Langsam",
      ["tbl_modeair"] = "Luftfahrtmotor",
      ["tbl_normal"] = "Normal",
      ["tbl_on"] = "An",
      ["tbl_autoextreme"] = "Automatisch Extrem",
      ["tbl_autoefficient"] = "Automatisch Effizient",
      ["tbl_smooth"] = "Sanft",
      ["tbl_fast"] = "Schnell",
      ["tbl_custom"] = "Benutzerdefiniert (PC-Definiert)",
      ["tbl_cutoff"] = "Abschaltung",
      ["tbl_autopower"] = "Automatisch Leistung",
      ["tbl_unused"] = "*Nicht verwendet*"
    },
    ["GOVERNOR_PROFILE"] = {
      ["governor_tta_gain"] = "TTA-Verstaerkung zur Erhoehung der Drehzahl zur Steuerung des Hecks in die negative Richtung (z. B. motorisiertes Heck unter Leerlaufdrehzahl).",
      ["governor_collective_ff_weight"] = "Kollektive Vorkompensationsgewichtung – wie stark kollektiv in das Feedforward gemischt wird.",
      ["governor_i_gain"] = "I-Term-Verstaerkung der PID-Regler.",
      ["governor_cyclic_ff_weight"] = "Zyklische Vorkompensationsgewichtung – wie stark zyklisch in das Feedforward gemischt wird.",
      ["governor_f_gain"] = "Feedforward-Verstaerkung.",
      ["governor_gain"] = "Master-Verstaerkung der PID-Regler.",
      ["governor_headspeed"] = "Ziel-Drehzahl fuer das aktuelle Profil.",
      ["governor_min_throttle"] = "Minimales Ausgangs-Gas, das der Governor verwenden darf.",
      ["governor_d_gain"] = "D-Term-Verstaerkung der PID-Regler.",
      ["governor_p_gain"] = "P-Term-Verstaerkung der PID-Regler.",
      ["governor_yaw_ff_weight"] = "Gier-Vorkompensationsgewicht – wie stark Gier in das Feedforward gemischt wird.",
      ["governor_max_throttle"] = "Maximales Ausgangs-Gas, das der Governor verwenden darf.",
      ["governor_tta_limit"] = "Maximale TTA-Drehzahlerhoehung ueber die volle Drehzahl hinaus."
    },
    ["PID_PROFILE"] = {
      ["bterm_cutoff_2"] = "B-Term-Grenzfrequenz in Hz.",
      ["dterm_cutoff_1"] = "D-Term-Grenzfrequenz in Hz.",
      ["bterm_cutoff_1"] = "B-Term-Grenzfrequenz in Hz.",
      ["gyro_cutoff_1"] = "Gesamtbandbreite des PID-Reglers in Hz.",
      ["tbl_on"] = "EIN",
      ["dterm_cutoff_2"] = "D-Term-Grenzfrequenz in Hz.",
      ["yaw_inertia_precomp_cutoff"] = "Grenzfrequenz der Ableitung in 1/10Hz-Schritten. Hoeher bedeutet schaerfere Vorkompensation.",
      ["offset_limit_0"] = "Harte Begrenzung fuer den High Speed Integral Offset-Winkel in der PID-Schleife. Der O-Term wird nie ueber diese Grenzen hinausgehen.",
      ["cyclic_cross_coupling_ratio"] = "Menge der Roll-zu-Pitch-Kompensation vs. Pitch-zu-Roll.",
      ["yaw_precomp_cutoff"] = "Frequenzgrenze fuer alle Gier-Vorkompensationen.",
      ["error_limit_0"] = "Harte Begrenzung fuer den Winkel-Fehler im PID-Regler. Der absolute Fehler und damit der I-Term werden nie ueber diese Grenzen hinausgehen.",
      ["trainer_gain"] = "Bestimmt, wie stark der Heli im Acro-Trainer-Modus zurueckkippt, wenn der Maximalwinkel ueberschritten wird.",
      ["tbl_rpy"] = "RPY",
      ["gyro_cutoff_2"] = "Gesamtbandbreite des PID-Reglers in Hz.",
      ["yaw_ccw_stop_gain"] = "Stopp-Verstaerkung (PD) fuer die Drehung gegen den Uhrzeigersinn.",
      ["trainer_angle_limit"] = "Begrenzt den maximalen Pitch/Roll-Winkel im Acro-Trainer-Modus.",
      ["tbl_rp"] = "RP",
      ["yaw_cyclic_ff_gain"] = "Zyklische Feedforward-Mischung in Gier (zyklisch-zu-Gier Vorkompensation).",
      ["error_decay_time_cyclic"] = "Zeitkonstante fuer das allmaehliche Reduzieren des zyklischen I-Terms. Hoeher stabilisiert den Schwebeflug, niedriger fuehrt zu mehr Drift.",
      ["error_decay_limit_cyclic"] = "Maximale Geschwindigkeit fuer das Reduzieren des zyklischen I-Terms.",
      ["cyclic_cross_coupling_gain"] = "Menge der angewendeten Kompensation fuer Pitch-Roll-Entkopplung.",
      ["yaw_collective_dynamic_decay"] = "Abfallzeit fuer die zusaetzliche Gier-Vorkompensation bei kollektiven Eingaben.",
      ["pitch_collective_ff_gain"] = "Erhoehen, um das Nick-Aufrichten durch Heckwiderstand beim Steigen auszugleichen.",
      ["iterm_relax_type"] = "Waehlen Sie die Achsen, auf denen dies aktiv ist. RP: Roll, Pitch. RPY: Roll, Pitch, Gier.",
      ["offset_limit_1"] = "Harte Begrenzung fuer den High Speed Integral Offset-Winkel.",
      ["iterm_relax_cutoff_1"] = "Hilft, das Nachschwingen zu reduzieren.",
      ["error_limit_1"] = "Harte Begrenzung fuer den Winkel-Fehler im PID-Regler.",
      ["horizon_level_strength"] = "Bestimmt, wie stark der Heli im Horizontmodus zur Horizontalen zurueckkippt.",
      ["error_limit_2"] = "Harte Begrenzung fuer den Winkel-Fehler im PID-Regler.",
      ["iterm_relax_cutoff_2"] = "Hilft, das Nachschwingen zu reduzieren.",
      ["tbl_off"] = "AUS",
      ["yaw_collective_ff_gain"] = "Kollektive Feedforward-Mischung in Gier (kollektiv-zu-Gier Vorkompensation).",
      ["gyro_cutoff_0"] = "Gesamtbandbreite des PID-Reglers in Hz.",
      ["yaw_collective_dynamic_gain"] = "Zusaetzlicher Gier-Vorkompensations-Boost bei kollektiven Eingaben.",
      ["cyclic_cross_coupling_cutoff"] = "Frequenzgrenze fuer die Kompensation. Hoeher bedeutet schnellere Korrektur.",
      ["error_rotation"] = "Dreht die aktuellen Roll- und Nick-Fehlerwerte um die Gierachse, wenn sich das Modell dreht. Auch als Piro-Kompensation bekannt.",
      ["angle_level_limit"] = "Begrenzt den maximalen Pitch/Roll-Winkel im Winkelmodus.",
      ["yaw_cw_stop_gain"] = "Stopp-Verstaerkung (PD) fuer die Drehung im Uhrzeigersinn.",
      ["iterm_relax_cutoff_0"] = "Hilft, das Nachschwingen nach schnellen Knueppelbewegungen zu reduzieren. Kann zu Inkonsistenzen bei kleinen Knueppelbewegungen fuehren, wenn zu niedrig.",
      ["yaw_inertia_precomp_gain"] = "Staerke der Hauptrotor-Traegheit. Hoeher bedeutet mehr Vorkompensation in der Giersteuerung.",
      ["dterm_cutoff_0"] = "D-Term-Grenzfrequenz in Hz.",
      ["angle_level_strength"] = "Bestimmt, wie stark der Heli im Winkelmodus zur Horizontalen zurueckkippt.",
      ["bterm_cutoff_0"] = "B-Term-Grenzfrequenz in Hz.",
      ["error_decay_time_ground"] = "Reduziert den aktuellen Reglerfehler, wenn das Modell nicht in der Luft ist, um ein Umkippen zu verhindern."
    },
    ["MIXER_CONFIG"] = {
      ["swash_trim_1"] = "Taumelscheiben-Trimmung zur Nivellierung bei festen Gestaengen.",
      ["tail_motor_idle"] = "Minimales Gas-Signal, das an den Heckmotor gesendet wird. Sollte so eingestellt sein, dass der Motor nicht stoppt.",
      ["tail_center_trim"] = "Heckrotor-Trimmung fuer 0 Gier bei variablem Pitch oder Heckmotor-Gas fuer 0 Gier bei motorisiertem Heck.",
      ["tbl_cw"] = "CW",
      ["swash_tta_precomp"] = "Mischer-Vorkompensation fuer 0 Gier.",
      ["swash_trim_2"] = "Taumelscheiben-Trimmung zur Nivellierung bei festen Gestaengen.",
      ["swash_geo_correction"] = "Anpassen, falls zu viel negativer oder positiver Kollektivpitch vorhanden ist.",
      ["swash_trim_0"] = "Taumelscheiben-Trimmung zur Nivellierung bei festen Gestaengen.",
      ["swash_phase"] = "Phasenversatz fuer die Taumelscheibensteuerung.",
      ["collective_tilt_correction_pos"] = "Skalierung der Kollektiv-Neigungskorrektur fuer positiven Pitch.",
      ["collective_tilt_correction_neg"] = "Skalierung der Kollektiv-Neigungskorrektur fuer negativen Pitch.",
      ["tbl_ccw"] = "CCW",
      ["swash_pitch_limit"] = "Maximale kombinierte zyklische und kollektive Blattverstellung."
    },
    ["MOTOR_CONFIG"] = {
      ["minthrottle"] = "Dieser PWM-Wert wird an den ESC/Servo bei minimalem Gas gesendet.",
      ["motor_pwm_protocol"] = "Das Protokoll zur Kommunikation mit dem ESC.",
      ["main_rotor_gear_ratio_0"] = "Zahnzahl des Motorritzels.",
      ["maxthrottle"] = "Dieser PWM-Wert wird an den ESC/Servo bei vollem Gas gesendet.",
      ["mincommand"] = "Dieser PWM-Wert wird gesendet, wenn der Motor gestoppt ist.",
      ["main_rotor_gear_ratio_1"] = "Zahnzahl des Hauptgetriebes.",
      ["tail_rotor_gear_ratio_1"] = "Zahnzahl des Autorotationsgetriebes.",
      ["motor_pwm_rate"] = "Die Frequenz, mit der der ESC PWM-Signale an den Motor sendet.",
      ["tail_rotor_gear_ratio_0"] = "Zahnzahl des Heckgetriebes.",
      ["motor_pole_count_0"] = "Die Anzahl der Magnete an der Motor-Glocke."
    },
    ["ESC_PARAMETERS_HW5"] = {
      ["tbl_cw"] = "CW",
      ["tbl_fixedwing"] = "Flaechenflugzeug",
      ["tbl_disabled"] = "Deaktiviert",
      ["tbl_ccw"] = "CCW",
      ["tbl_heligov"] = "Heli Governor",
      ["tbl_softcutoff"] = "Sanfte Abschaltung",
      ["tbl_proportional"] = "Proportional",
      ["tbl_heliext"] = "Heli Externer Governor",
      ["tbl_helistore"] = "Heli Governor Speicher",
      ["tbl_hardcutoff"] = "Harte Abschaltung",
      ["tbl_normal"] = "Normal",
      ["tbl_autocalculate"] = "Automatische Berechnung",
      ["tbl_enabled"] = "Aktiviert",
      ["tbl_reverse"] = "Umgekehrt"
    },
    ["PILOT_CONFIG"] = {
      ["model_param1_value"] = "Setzen Sie die erwartete Flugzeit in Sekunden. Die Fernsteuerung biept sobald die Flugzeit erreicht wurde."
    },
    ["BATTERY_INI"] = {
      ["calcfuel_local"] = "Calculate Fuel Using",
      ["tbl_off"] = "Current Sensor",
      ["tbl_on"] = "Voltage Sensor",
      ["sag_multiplier"] = "Raise or lower to adjust for the amount of voltage sag you see in flight.",
      ["kalman_multiplier"] = "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.",
      ["alert_off"] = "Off",
      ["alert_bec"] = "BEC",
      ["alert_rxbatt"] = "Rx Batt"
    },
    ["BATTERY_CONFIG"] = {
      ["vbatmincellvoltage"] = "Die minimale Spannung pro Zelle, bevor der Niederspannungsalarm ausgeloest wird.",
      ["vbatmaxcellvoltage"] = "Die maximale Spannung pro Zelle, bevor der Hochspannungsalarm ausgeloest wird.",
      ["vbatwarningcellvoltage"] = "Die Spannung pro Zelle, bei der der Niederspannungsalarm ausgeloest wird.",
      ["batteryCellCount"] = "Die Anzahl der Zellen in Ihrer Batterie.",
      ["vbatfullcellvoltage"] = "Die Nennspannung einer vollstaendig geladenen Zelle.",
      ["batteryCapacity"] = "Die Kapazitaet Ihrer Batterie in Milliamperestunden."
    },
    ["ACC_TRIM"] = {
      ["pitch"] = "Verwenden Sie diese Einstellung, um den Heli zu trimmen, falls er in einem der stabilisierten Modi (Angle, Horizon usw.) driftet.",
      ["roll"] = "Verwenden Sie diese Einstellung, um den Heli zu trimmen, falls er in einem der stabilisierten Modi (Angle, Horizon usw.) driftet."
    },
    ["PID_TUNING"] = {
      ["pid_1_I"] = "Wie genau das System seine Position haelt.",
      ["pid_2_P"] = "Wie genau das System dem gewuenschten Sollwert folgt.",
      ["pid_2_I"] = "Wie genau das System seine Position haelt.",
      ["pid_1_O"] = "Verhindert das Nicken des Modells bei hoher Kollektivleistung.",
      ["pid_1_F"] = "Hilft, den P-Term basierend auf Knueppelbewegungen zu verstaerken. Erhoehen macht die Reaktion schaerfer, kann aber zu Ueberschwingern fuehren.",
      ["pid_0_D"] = "Staerke der Daempfung gegen jede Bewegung des Systems, einschliesslich aeusserer Einfluesse. Reduziert auch das Ueberschwingen.",
      ["pid_1_P"] = "Wie genau das System dem gewuenschten Sollwert folgt.",
      ["pid_0_I"] = "Wie genau das System seine Position haelt.",
      ["pid_2_B"] = "Zusaetzlicher Feedforward-Boost, um das System reaktionsschneller auf schnelle Knueppelbewegungen zu machen.",
      ["pid_0_O"] = "Verhindert das Rollen des Modells bei hoher Kollektivleistung.",
      ["pid_0_F"] = "Hilft, den P-Term basierend auf Knueppelbewegungen zu verstaerken. Erhoehen macht die Reaktion schaerfer, kann aber zu Ueberschwingern fuehren.",
      ["pid_2_F"] = "Hilft, den P-Term basierend auf Knueppelbewegungen zu verstaerken. Erhoehen macht die Reaktion schaerfer, kann aber zu Ueberschwingern fuehren.",
      ["pid_2_D"] = "Staerke der Daempfung gegen jede Bewegung des Systems, einschliesslich aeusserer Einfluesse. Reduziert auch das Ueberschwingen.",
      ["pid_0_P"] = "Wie genau das System dem gewuenschten Sollwert folgt.",
      ["pid_1_D"] = "Staerke der Daempfung gegen jede Bewegung des Systems, einschliesslich aeusserer Einfluesse. Reduziert auch das Ueberschwingen.",
      ["pid_0_B"] = "Zusaetzlicher Feedforward-Boost, um das System reaktionsschneller auf schnelle Knueppelbewegungen zu machen.",
      ["pid_1_B"] = "Zusaetzlicher Feedforward-Boost, um das System reaktionsschneller auf schnelle Knueppelbewegungen zu machen."
    },
    ["GOVERNOR_CONFIG"] = {
      ["tbl_govmode_standard"] = "STANDARD",
      ["tbl_govmode_mode2"] = "MODUS2",
      ["gov_tracking_time"] = "Zeitkonstante fuer Drehzahlaenderungen in Sekunden, gemessen von null bis zur vollen Drehzahl.",
      ["tbl_govmode_passthrough"] = "DURCHSCHLEIFEN",
      ["tbl_govmode_mode1"] = "MODUS1",
      ["gov_recovery_time"] = "Zeitkonstante fuer die Wiederherstellung des Hochfahrens in Sekunden, gemessen von null bis zur vollen Drehzahl.",
      ["gov_startup_time"] = "Zeitkonstante fuer den langsamen Start in Sekunden, gemessen von null bis zur vollen Drehzahl.",
      ["gov_handover_throttle"] = "Der Governor aktiviert sich ueber diesem Wert in %. Darunter wird das Eingangs-Gas an den ESC weitergegeben.",
      ["gov_spoolup_time"] = "Zeitkonstante fuer das langsame Hochfahren in Sekunden, gemessen von null bis zur vollen Drehzahl.",
      ["gov_spoolup_min_throttle"] = "Minimaler Gaswert fuer langsames Hochfahren in Prozent. Bei Elektromotoren ist der Standardwert 5 %, bei Nitro sollte der Wert so eingestellt werden, dass die Kupplung fuer ein sanftes Hochfahren zu greifen beginnt (10-15 %).",
      ["tbl_govmode_off"] = "AUS"
    },
    ["RC_CONFIG"] = {
      ["rc_deflection"] = "Knueppelausschlag vom Mittelpunkt in Mikrosekunden (µs).",
      ["rc_min_throttle"] = "Minimales Gas (0 % Gasausgabe), das von der Fernsteuerung erwartet wird, in Mikrosekunden (µs).",
      ["rc_max_throttle"] = "Maximales Gas (100 % Gasausgabe), das von der Fernsteuerung erwartet wird, in Mikrosekunden (µs).",
      ["rc_arm_throttle"] = "Das Gas muss bei diesem Wert oder darunter liegen (in µs), um das Schaerfen zu ermoeglichen. Muss mindestens 10 µs unter dem Mindestgaswert liegen.",
      ["rc_yaw_deadband"] = "Totzone fuer die Giersteuerung in Mikrosekunden (µs).",
      ["rc_deadband"] = "Totzone fuer die zyklische Steuerung in Mikrosekunden (µs).",
      ["rc_center"] = "Knueppelmittelpunkt in Mikrosekunden (µs)."
    },
    ["FILTER_CONFIG"] = {
      ["gyro_soft_notch_cutoff_2"] = "Breite des Notch-Filters in Hz.",
      ["gyro_lpf1_static_hz"] = "Tiefpassfilter-Grenzfrequenz in Hz.",
      ["tbl_none"] = "KEINE",
      ["dyn_notch_max_hz"] = "Maximale Frequenz, auf die der Notch-Filter angewendet wird.",
      ["tbl_1st"] = "1.",
      ["rpm_min_hz"] = "Minimale Frequenz des Drehzahlfilters.",
      ["dyn_notch_min_hz"] = "Minimale Frequenz, auf die der Notch-Filter angewendet wird.",
      ["gyro_lpf1_dyn_max_hz"] = "Dynamischer Filter – maximale Grenzfrequenz in Hz.",
      ["gyro_soft_notch_hz_2"] = "Zentralfrequenz, auf die der Notch-Filter angewendet wird.",
      ["gyro_soft_notch_cutoff_1"] = "Breite des Notch-Filters in Hz.",
      ["gyro_soft_notch_hz_1"] = "Zentralfrequenz, auf die der Notch-Filter angewendet wird.",
      ["dyn_notch_count"] = "Anzahl der Noteches.",
      ["dyn_notch_q"] = "Qualitaetsfaktor des dynamischen Notch-Filters.",
      ["gyro_lpf2_static_hz"] = "Tiefpassfilter-Grenzfrequenz in Hz.",
      ["gyro_lpf1_dyn_min_hz"] = "Dynamischer Filter – minimale Grenzfrequenz in Hz.",
      ["tbl_2nd"] = "2.",
      ["tbl_custom"] = "BENUTZERDEF.",
      ["tbl_low"] = "NIEDRIG",
      ["tbl_medium"] = "MITTEL",
      ["tbl_high"] = "HOCH"
    },
    ["ESC_PARAMETERS_XDFLY"] = {
      ["tbl_jadegreen"] = "Jadegruen",
      ["tbl_off"] = "Aus",
      ["tbl_low"] = "Niedrig",
      ["tbl_orange"] = "Orange",
      ["tbl_fmfw"] = "Flaechenflugzeug",
      ["tbl_ccw"] = "CCW",
      ["tbl_medium"] = "Mittel",
      ["tbl_yellow"] = "Gelb",
      ["tbl_reverse"] = "Umgekehrt",
      ["tbl_red"] = "Rot",
      ["tbl_high"] = "Hoch",
      ["tbl_auto"] = "Automatisch",
      ["tbl_cw"] = "CW",
      ["tbl_fmheli"] = "Helikopter",
      ["tbl_purple"] = "Lila",
      ["tbl_green"] = "Gruen",
      ["tbl_blue"] = "Blau",
      ["tbl_slow"] = "Langsam",
      ["tbl_normal"] = "Normal",
      ["tbl_fast"] = "Schnell",
      ["tbl_escgov"] = "ESC-Governor",
      ["tbl_white"] = "Weiss",
      ["tbl_cyan"] = "Cyan",
      ["tbl_vslow"] = "Sehr langsam",
      ["tbl_extgov"] = "Externer Governor",
      ["tbl_pink"] = "Pink",
      ["tbl_fwgov"] = "Flaechenflugzeug-Governor",
      ["tbl_on"] = "An"
    },
    ["ESC_PARAMETERS_SCORPION"] = {
      ["tbl_airplane"] = "Flugzeugmodus",
      ["tbl_cw"] = "CW",
      ["tbl_off"] = "Aus",
      ["tbl_quad"] = "Quadmodus",
      ["tbl_heligov"] = "Heli Governor",
      ["tbl_exbus"] = "Jeti Exbus",
      ["tbl_boat"] = "Bootmodus",
      ["tbl_unsolicited"] = "Ungefragt",
      ["tbl_futsbus"] = "Futaba SBUS",
      ["tbl_ccw"] = "CCW",
      ["tbl_helistore"] = "Heli Governor (gespeichert)",
      ["tbl_standard"] = "Standard",
      ["tbl_on"] = "An",
      ["tbl_vbar"] = "VBar",
      ["tbl_vbargov"] = "VBar Governor",
      ["tbl_extgov"] = "Externer Governor"
    }
  },
  ["app"] = {
    ["btn_ok"] = "          OK           ",
    ["btn_close"] = "SCHLIESSEN",
    ["navigation_menu"] = "MENUE",
    ["menu_section_hardware"] = "Hardware",
    ["msg_please_disarm_to_save_warning"] = "Einstellungen werden nur beim Entschaerfen im EEPROM gespeichert",
    ["msg_saving_settings"] = "Einstellungen werden gespeichert...",
    ["msg_saving_to_fbl"] = "Daten werden auf dem Flugcontroller gespeichert...",
    ["navigation_reload"] = "NEU LADEN",
    ["menu_section_developer"] = "Entwickler",
    ["check_msp_version"] = "MSP-Version konnte nicht ermittelt werden.",
    ["menu_section_about"] = "Ueber",
    ["msg_please_disarm_to_save"] = "Bitte disarmen, um die Datensicherheit zu gewaehrleisten.",
    ["unit_hertz"] = "Hz",
    ["msg_rebooting"] = "Neustart...",
    ["msg_save_settings"] = "Einstellungen speichern",
    ["btn_cancel"] = "ABBRECHEN",
    ["msg_connecting_to_fbl"] = "Verbindung zum Flugcontroller wird hergestellt...",
    ["navigation_help"] = "?",
    ["modules"] = {
      ["stats"] = {
        ["name"] = "Statistiken",
        ["totalflighttime"] = "Gesamtflugzeit",
        ["flightcount"] = "Fluganzahl",
        ["lastflighttime"] = "Letzte Flugdauer",
        ["help_p1"] = "Verwenden Sie dieses Modul, um die aufgezeichneten Flugstatistiken am Flugcontroller upzudaten."
      },
      ["settings"] = {
        ["name"] = "Einstellungen",
        ["no_themes_available_to_configure"] = "Keine konfigurierbaren Designs auf dem Geraet installiert.",
        ["txt_audio_timer"] = "Timer",
        ["txt_audio_events"] = "Events",
        ["txt_audio_switches"] = "Schalter",
        ["txt_iconsize"] = "Icongroesse",
        ["txt_general"] = "Allgemein",
        ["txt_text"] = "TEXT",
        ["txt_small"] = "KLEIN",
        ["txt_large"] = "GROSS",
        ["txt_syncname"] = "Modellname synchronisieren",
        ["txt_devtools"] = "Entwickler-Werkzeuge",
        ["txt_apiversion"] = "API Version",
        ["txt_logging"] = "Protokollierung",
        ["txt_compilation"] = "Kompilation",
        ["txt_loglocation"] = "Protokoll-Speicherort",
        ["txt_console"] = "KONSOLE",
        ["txt_consolefile"] = "KONSOLE & DATEI",
        ["txt_loglevel"] = "Protokollierungsgrad",
        ["txt_off"] = "AUS",
        ["txt_info"] = "INFO",
        ["txt_debug"] = "DEBUG",
        ["txt_mspdata"] = "Protokolliere MSP Daten",
        ["txt_queuesize"] = "Protokolliere MSP Warteschlangengroesse",
        ["txt_memusage"] = "Protokolliere Speicherauslastung",
        ["txt_batttype"] = "Tx Battery Options",
        ["txt_battdef"] = "Default",
        ["txt_batttext"] = "Text",
        ["txt_battdig"] = "Digital",
        ["dashboard"] = "Dashboard",
        ["dashboard_theme"] = "Design",
        ["dashboard_theme_panel_global"] = "Standard-Design fuer alle Modelle",
        ["dashboard_theme_panel_model"] = "Optionales Design fuer dieses Modell",
        ["dashboard_theme_panel_model_disabled"] = "Ausgeschalten",
        ["dashboard_settings"] = "Einstellungen",
        ["dashboard_theme_preflight"] = "Vorflug-Design",
        ["dashboard_theme_inflight"] = "Flug-Design",
        ["dashboard_theme_postflight"] = "Nachflug-Design",
        ["audio"] = "Audio",
        ["localizations"] = "Lokalisierung",
        ["txt_development"] = "Entwicklung",
        ["temperature_unit"] = "Temperatureinheit",
        ["altitude_unit"] = "Hoeheneinheit",
        ["celcius"] = "Celsius",
        ["fahrenheit"] = "Fahrenheit",
        ["meters"] = "Meter",
        ["feet"] = "Fuss",
        ["warning"] = "Warnung",
        ["governor_state"] = "Governor Status",
        ["arming_flags"] = "Arming Flags",
        ["voltage"] = "Spannung",
        ["pid_rates_profile"] = "PID/Raten Profil",
        ["pid_profile"] = "PID Profil",
        ["rate_profile"] = "Raten Profil",
        ["esc_temperature"] = "ESC Temperatur",
        ["esc_threshold"] = "Schwellwert (°)",
        ["bec_voltage"] = "BEC Spannung",
        ["bec_threshold"] = "Schwellwert (V)",
        ["fuel"] = "Kraftstoff",
        ["fuel_callout_default"] = "Standard (Nur wenn 10%)",
        ["fuel_callout_10"] = "Alle 10%",
        ["fuel_callout_20"] = "Alle 20%",
        ["fuel_callout_25"] = "Alle 25%",
        ["fuel_callout_50"] = "Alle 50%",
        ["fuel_callout_percent"] = "Ansage %",
        ["fuel_repeats_below"] = "Wiederholungen unter 0%",
        ["fuel_haptic_below"] = "Haptisch unter 0%",
        ["timer_alerting"] = "Timer Alarmierung",
        ["timer_elapsed_alert_mode"] = "Alarmierung bei Timer-Ablauf",
        ["timer_prealert_options"] = "Vor-timer Alarmierungsoptionen",
        ["timer_prealert"] = "Vor-timer Alarmierung",
        ["timer_alert_period"] = "Alarmierungsperiode",
        ["timer_postalert_options"] = "Nach-timer Alarmierungs Optionen",
        ["timer_postalert"] = "Nach-timer Alarmierung",
        ["timer_postalert_period"] = "Alarmierungs Periode",
        ["timer_postalert_interval"] = "Alarmierungs Interval"
      },
      ["validate_sensors"] = {
        ["help_p1"] = "Dieses Tool versucht, eine kurze Liste aller Sensoren zu erstellen, die nicht empfangen werden.",
        ["invalid"] = "UNGUELTIG",
        ["name"] = "Sensoren",
        ["msg_repair"] = "Erforderliche Sensoren auf dem Flugcontroller aktivieren?",
        ["msg_repair_fin"] = "Der Flugcontroller wurde konfiguriert? Moeglicherweise muessen Sie Sensoren neu suchen, um die Aenderungen zu sehen.",
        ["ok"] = "OK",
        ["help_p2"] = "Verwenden Sie dieses Tool, um sicherzustellen, dass Sie die richtigen Sensoren senden."
      },
      ["msp_exp"] = {
        ["help_p1"] = "Dieses Tool ermoeglicht das Senden einer benutzerdefinierten Byte-Zeichenfolge an den Flugcontroller. Es ist nuetzlich fuer Entwickler beim Debuggen von Werten.",
        ["name"] = "MSP Experimentell",
        ["help_p2"] = "Wenn Sie nicht verstehen, was Sie tun, verwenden Sie es nicht, da dies zu Problemen fuehren kann."
      },
      ["esc_tools"] = {
        ["unknown"] = "UNBEKANNT",
        ["name"] = "ESC-Werkzeuge",
        ["please_powercycle"] = "Bitte schalten Sie den ESC aus und wieder ein...",
        ["mfg"] = {
          ["hw5"] = {
            ["esc"] = "ESC",
            ["brake_force"] = "Bremskraft%",
            ["rotation"] = "Drehrichtung",
            ["soft_start"] = "Sanftanlauf",
            ["name"] = "Hobbywing V5",
            ["limits"] = "Grenzwerte",
            ["bec_voltage"] = "BEC-Spannung",
            ["gov_i_gain"] = "I-Verst.",
            ["startup_time"] = "Anlaufzeit",
            ["lipo_cell_count"] = "LiPo-Zellenanzahl",
            ["restart_time"] = "Neustartzeit",
            ["volt_cutoff_type"] = "Spannungsabschaltmodus",
            ["motor"] = "Motor",
            ["brake_type"] = "Bremstyp",
            ["brake"] = "Bremse",
            ["governor"] = "Governor",
            ["advanced"] = "Erweitert",
            ["basic"] = "Grundlegend",
            ["flight_mode"] = "Flugmodus",
            ["auto_restart"] = "Automatischer Neustart",
            ["active_freewheel"] = "Aktive Freilaufsteuerung",
            ["cutoff_voltage"] = "Abschaltspannung",
            ["startup_power"] = "Anlaufleistung",
            ["other"] = "Sonstiges",
            ["timing"] = "Timing",
            ["gov_p_gain"] = "P-Verst."
          },
          ["xdfly"] = {
            ["hv_bec_voltage"] = "HV BEC-Spannung",
            ["gov"] = "Governor",
            ["brake_force"] = "Bremskraft",
            ["sr_function"] = "SR-Funktion",
            ["name"] = "XDFLY",
            ["lv_bec_voltage"] = "LV BEC-Spannung",
            ["auto_restart_time"] = "Neustartzeit",
            ["acceleration"] = "Beschleunigung",
            ["motor_direction"] = "Motordrehrichtung",
            ["smart_fan"] = "Intelligenter Luefter",
            ["governor"] = "Governor",
            ["advanced"] = "Erweitert",
            ["gov_i"] = "Gov-I",
            ["cell_cutoff"] = "Zellenabschaltung",
            ["led_color"] = "LED-Farbe",
            ["basic"] = "Grundlegend",
            ["startup_power"] = "Anlaufleistung",
            ["motor_poles"] = "Motorpole",
            ["capacity_correction"] = "Kapazitaetskorrektur",
            ["timing"] = "Timing",
            ["gov_p"] = "Gov-P"
          },
          ["flrtr"] = {
            ["gov"] = "Governor",
            ["motor_temp_sensor"] = "Motortemperatursensor",
            ["starting_torque"] = "Anlaufmoment",
            ["cell_count"] = "Zellenanzahl",
            ["gov_p"] = "Gov-P",
            ["motor_erpm_max"] = "Max. Motor-ERPM",
            ["name"] = "FLYROTOR",
            ["low_voltage_protection"] = "Unterspannungsschutz",
            ["gov_d"] = "Gov-D",
            ["telemetry_protocol"] = "Telemetrieprotokoll",
            ["motor_direction"] = "Motordrehrichtung",
            ["throttle_protocol"] = "Gasprotokoll",
            ["soft_start"] = "Sanftanlauf",
            ["other"] = "Sonstiges",
            ["temperature_protection"] = "Temperaturschutz",
            ["buzzer_volume"] = "Summerlautstaerke",
            ["timing_angle"] = "Timing-Winkel",
            ["governor"] = "Governor",
            ["advanced"] = "Erweitert",
            ["gov_i"] = "Gov-I",
            ["bec_voltage"] = "BEC-Spannung",
            ["fan_control"] = "Lueftersteuerung",
            ["basic"] = "Grundlegend",
            ["current_gain"] = "Stromverstaerkung",
            ["led_color"] = "LED-Farbe",
            ["motor_temp"] = "Motortemperatur",
            ["response_speed"] = "Reaktionsgeschwindigkeit",
            ["battery_capacity"] = "Batteriekapazitaet"
          },
          ["scorp"] = {
            ["esc_mode"] = "ESC-Modus",
            ["min_voltage"] = "Min. Spannung",
            ["rotation"] = "Drehrichtung",
            ["telemetry_protocol"] = "Telemetrieprotokoll",
            ["name"] = "Scorpion",
            ["runup_time"] = "Hochlaufzeit",
            ["motor_startup_sound"] = "Motor-Startton",
            ["gov_integral"] = "Gov Integral",
            ["gov_proportional"] = "Gov Proportional",
            ["cutoff_handling"] = "Abschaltverhalten",
            ["bailout"] = "Bailout",
            ["limits"] = "Grenzwerte",
            ["soft_start_time"] = "Sanftanlauf-Zeit",
            ["advanced"] = "Erweitert",
            ["bec_voltage"] = "BEC-Spannung",
            ["extra_msg_save"] = "Bitte starten Sie den ESC neu, um die Aenderungen zu uebernehmen",
            ["basic"] = "Grundlegend",
            ["max_current"] = "Max. Strom",
            ["max_temperature"] = "Max. Temperatur",
            ["protection_delay"] = "Schutzverzoegerung",
            ["max_used"] = "Max. Nutzung"
          },
          ["yge"] = {
            ["esc_mode"] = "ESC-Modus",
            ["esc"] = "ESC",
            ["current_limit"] = "Strombegrenzung",
            ["f3c_auto"] = "F3C-Autorotation",
            ["name"] = "YGE",
            ["max_start_power"] = "Maximale Startleistung",
            ["lv_bec_voltage"] = "BEC",
            ["pinion_teeth"] = "Ritzel-Zaehne",
            ["auto_restart_time"] = "Neustartzeit",
            ["main_teeth"] = "Hauptzahnrad-Zaehne",
            ["other"] = "Sonstiges",
            ["limits"] = "Grenzwerte",
            ["cell_cutoff"] = "Zellenabschaltung",
            ["throttle_response"] = "Gasannahme",
            ["stick_zero_us"] = "Stick-Nullpunkt",
            ["advanced"] = "Erweitert",
            ["gov_i"] = "Gov-I",
            ["motor_pole_pairs"] = "Motor-Polpaare",
            ["stick_range_us"] = "Stick-Bereich",
            ["basic"] = "Grundlegend",
            ["min_start_power"] = "Minimale Startleistung",
            ["active_freewheel"] = "Aktive Freilaufsteuerung",
            ["direction"] = "Richtung",
            ["timing"] = "Motortiming",
            ["gov_p"] = "Gov-P"
          }
        },
        ["searching"] = "Suche"
      },
      ["pids"] = {
        ["help_p1"] = "FeedForward (Roll/Nick): Beginnen Sie bei 70, erhoehen Sie den Wert, bis Stopps scharf sind und kein Driften auftritt. Halten Sie Roll und Nick gleich.",
        ["o"] = "O",
        ["pitch"] = "Nick",
        ["i"] = "I",
        ["yaw"] = "Gier",
        ["roll"] = "Roll",
        ["help_p5"] = "Testen & Anpassen: Fliegen, beobachten und feinjustieren, um die beste Leistung unter realen Bedingungen zu erreichen.",
        ["p"] = "P",
        ["f"] = "F",
        ["name"] = "PIDs",
        ["help_p2"] = "I-Gain (Roll/Nick): Erhoehen Sie den Wert schrittweise fuer stabile Piro-Pitch-Pumps. Ein zu hoher Wert verursacht Wackeln; Roll- und Nick-Werte sollten uebereinstimmen.",
        ["d"] = "D",
        ["b"] = "B",
        ["help_p4"] = "Heck-Stopp-Gain (CW/CCW): Separat anpassen fuer saubere, Stopps ohne Pendeln in beide Richtungen.",
        ["help_p3"] = "Heck P/I/D-Gains: Erhoehen Sie P, bis leichtes Wackeln in Funnels auftritt, dann leicht reduzieren. Erhoehen Sie I, bis das Heck in harten Manoevern stabil bleibt (zu hoch verursacht langsames Schwingen). D anpassen fuer sanfte Stopps – hoeher fuer langsame Servos, niedriger fuer schnelle."
      },
      ["msp_speed"] = {
        ["seconds_600"] = "  600S  ",
        ["avg_query_time"] = "Durchschnittliche Anfragezeit",
        ["seconds_30"] = "  30S  ",
        ["name"] = "MSP-Geschwindigkeit",
        ["max_query_time"] = "Maximale Anfragezeit",
        ["help_p1"] = "Dieses Tool versucht, die Qualitaet Ihrer MSP-Datenverbindung zu bestimmen, indem es innerhalb von 30 Sekunden so viele grosse MSP-Abfragen wie moeglich durchfuehrt.",
        ["retries"] = "Wiederholungen",
        ["checksum_errors"] = "Pruefsummenfehler",
        ["test_length"] = "Testdauer",
        ["start"] = "Start",
        ["memory_free"] = "Freier Speicher",
        ["start_prompt"] = "Moechten Sie den Test starten? Waehlen Sie unten die Testlaufzeit aus.",
        ["rf_protocol"] = "RF-Protokoll",
        ["min_query_time"] = "Minimale Anfragezeit",
        ["seconds_120"] = "  120S  ",
        ["seconds_300"] = "  300S  ",
        ["testing"] = "Testlauf",
        ["successful_queries"] = "Erfolgreiche Anfragen",
        ["timeouts"] = "Zeitueberschreitungen",
        ["testing_performance"] = "MSP-Leistung wird getestet...",
        ["total_queries"] = "Gesamtanzahl Anfragen"
      },
      ["copyprofiles"] = {
        ["profile_type"] = "Profiltyp",
        ["profile_type_pid"] = "PID",
        ["profile_type_rate"] = "Rate",
        ["msgbox_save"] = "Einstellungen speichern",
        ["name"] = "Profile kopieren",
        ["help_p1"] = "PID-Profil oder Rate-Profil von der Quelle zum Ziel kopieren.",
        ["dest_profile"] = "Zielprofil",
        ["source_profile"] = "Quellprofil",
        ["msgbox_msg"] = "Aktuelle Seite auf dem Flugcontroller speichern?",
        ["help_p2"] = "Waehlen Sie die Quelle und das Ziel aus und speichern Sie, um das Profil zu kopieren."
      },
      ["esc_motors"] = {
        ["min_throttle"] = "0% Gas PWM-Wert",
        ["tail_motor_ratio"] = "Heckmotor",
        ["max_throttle"] = "100% Gas PWM-Wert",
        ["main_motor_ratio"] = "Hauptmotor",
        ["pinion"] = "Ritzel",
        ["main"] = "Haupt",
        ["help_p1"] = "Konfigurieren Sie die Motor- und Governor-Einstellungen.",
        ["rear"] = "Hinten",
        ["front"] = "Vorne",
        ["voltage_correction"] = "Spannungskorrektur",
        ["mincommand"] = "Motor-Stopp PWM-Wert",
        ["name"] = "ESC/Motoren",
        ["motor_pole_count"] = "Anzahl der Motorpole",
        ["current_correction"] = "Stromkorrektur",
        ["consumption_correction"] = "Verbrauchskorrektur"
      },
      ["radio_config"] = {
        ["deflection"] = "Ausschlag",
        ["max_throttle"] = "Max",
        ["stick"] = "Knueppel",
        ["arming"] = "Arm",
        ["yaw_deadband"] = "Gier",
        ["cyclic"] = "Zyklisch",
        ["name"] = "Fernsteuerung",
        ["help_p1"] = "Konfigurieren Sie Ihre Fernsteuerungseinstellungen: Knueppelzentrum, Arm, Gas-Hold und Gas-Abschaltung.",
        ["min_throttle"] = "Min",
        ["throttle"] = "Gas",
        ["deadband"] = "Totzone",
        ["center"] = "Zentrum"
      },
      ["profile_select"] = {
        ["help_p1"] = "Waehlen Sie das aktuelle Flugprofil oder Rate-Profil aus, das Sie verwenden moechten.",
        ["rate_profile"] = "Rate-Profil",
        ["pid_profile"] = "PID-Profil",
        ["save_prompt"] = "Aktuelle Seite auf dem Flugcontroller speichern?",
        ["save_prompt_local"] = "Aktuelle Seite auf der Fernsteuerung speichern?",
        ["cancel"] = "ABBRECHEN",
        ["name"] = "Profil auswaehlen",
        ["save_settings"] = "Einstellungen speichern",
        ["ok"] = "OK",
        ["help_p2"] = "Wenn Sie einen Schalter an Ihrer Fernsteuerung verwenden, um Flug- oder Rate-Modi zu aendern, wird diese Auswahl ueberschrieben, sobald Sie den Schalter umlegen."
      },
      ["profile_governor"] = {
        ["tail_torque_assist"] = "Heck-Drehmoment",
        ["p"] = "P",
        ["i"] = "I",
        ["yaw"] = "Gier",
        ["cyc"] = "Zyk.",
        ["f"] = "F",
        ["name"] = "Governor",
        ["d"] = "D",
        ["help_p1"] = "Volle Drehzahl: Ziel-Drehzahl bei 100 % Gas-Eingang.",
        ["help_p6"] = "Heck-Drehmoment-Unterstuetzung: Fuer motorisierte Heckrotoren. Verstaerkung und Begrenzung der Drehzahlerhoehung beim Gierausgleich durch das Hauptrotordrehmoment.",
        ["help_p4"] = "Vorkompensation: Governor-Vorkompensation fuer Gier-, zyklische und kollektive Eingaben.",
        ["max_throttle"] = "Max. Gas",
        ["full_headspeed"] = "Volle Drehzahl",
        ["precomp"] = "Vorkompensation",
        ["gain"] = "PID-Masterverstaerkung",
        ["disabled_message"] = "Rotorflight-Governor ist nicht aktiviert",
        ["help_p3"] = "Verstaerkungen: Feineinstellung des Governors.",
        ["col"] = "Kol.",
        ["min_throttle"] = "Min. Gas",
        ["tta_limit"] = "Limit",
        ["help_p2"] = "PID-Masterverstaerkung: Wie stark der Regler arbeitet, um die Drehzahl zu halten.",
        ["gains"] = "Verstaerkungen",
        ["help_p5"] = "Max. Gas: Der maximale Gas-%-Wert, den der Governor verwenden darf.",
        ["tta_gain"] = "Gain"
      },
      ["profile_tailrotor"] = {
        ["gain"] = "Verst.",
        ["help_p4"] = "Kollektive FF-Verstaerkung: Heck-Vorkompensation fuer kollektive Eingaben.",
        ["collective_impulse_ff"] = "Kollektiv-Impuls-FF",
        ["help_p2"] = "Vorkompensation Grenzwert: Frequenzgrenze fuer alle Gier-Vorkompensationsaktionen.",
        ["cutoff"] = "Grenzw.",
        ["help_p3"] = "Zyklische FF-Verstaerkung: Heck-Vorkompensation fuer zyklische Eingaben.",
        ["help_p1"] = "Gier-Stopp-Verstaerkung: Eine hoehere Stopp-Verstaerkung fuehrt zu aggressiveren Heckstopps, kann aber bei zu hohen Werten zu Oszillationen fuehren. Passen Sie CW oder CCW an, um gleichmaessige Gierstopps zu erzielen.",
        ["inertia_precomp"] = "Traegheits-Vorkomp.",
        ["cyclic_ff_gain"] = "Zyklische FF-Verstaerkung",
        ["help_p5"] = "Kollektiv-Impuls-FF: Impulsartige Heck-Vorkompensation fuer kollektive Eingaben. Falls zusaetzliche Heck-Vorkompensation zu Beginn einer kollektiven Eingabe erforderlich ist.",
        ["cw"] = "CW",
        ["ccw"] = "CCW",
        ["yaw_stop_gain"] = "Gier-Stopp-Verstaerkung",
        ["precomp_cutoff"] = "Vorkompensation Grenzwert",
        ["collective_ff_gain"] = "Kollektive FF-Verstaerkung",
        ["name"] = "Heckrotor",
        ["decay"] = "Abfall"
      },
      ["profile_pidcontroller"] = {
        ["help_p4"] = "Fehlerrotation: Ermoeglicht das Teilen von Fehlern zwischen allen Achsen.",
        ["ground_error_decay"] = "Fehlerrueckgang am Boden",
        ["yaw"] = "Y",
        ["inflight_error_decay"] = "Fehlerrueckgang im Flug",
        ["help_p2"] = "Fehlergrenze: Winkelbegrenzung fuer den I-Term.",
        ["error_limit"] = "Fehlergrenze",
        ["help_p3"] = "Offset-Grenze: Winkelbegrenzung fuer High Speed Integral (O-Term).",
        ["cutoff_point"] = "Grenzpunkt",
        ["limit"] = "Grenze",
        ["iterm_relax"] = "I-Term-Entspannung",
        ["hsi_offset_limit"] = "HSI-Offset-Grenze",
        ["pitch"] = "P",
        ["name"] = "PID-Regler",
        ["error_rotation"] = "Fehlerrotation",
        ["roll"] = "R",
        ["help_p5"] = "I-Term-Entspannung: Begrenzung der I-Term-Akkumulation bei schnellen Bewegungen – hilft, das Nachschwingen nach schnellen Steuerbewegungen zu reduzieren. Sollte fuer grosse Helis niedriger und fuer kleine Helis hoeher sein. Am besten nur so weit reduzieren, wie es fuer den eigenen Flugstil erforderlich ist.",
        ["time"] = "Zeit",
        ["help_p1"] = "Fehlerrueckgang am Boden: PID-Abfall zur Vermeidung eines Kippens des Helis am Boden."
      },
      ["logs"] = {
        ["help_logs_p2"] = "Hinweis: Um das Logging zu aktivieren, muessen Sie die folgenden Sensoren aktiviert haben.",
        ["name"] = "Logs",
        ["help_logs_p1"] = "Bitte waehlen Sie eine Log-Datei aus der untenstehenden Liste aus.",
        ["msg_no_logs_found"] = "KEINE LOG-DATEIEN GEFUNDEN",
        ["help_logs_tool_p1"] = "Bitte verwenden Sie den Schieberegler, um im Diagramm zu navigieren.",
        ["help_logs_p3"] = "- Arm-Status, Spannung, Drehzahl, Strom, ESC-Temperatur"
      },
      ["battery"] = {
        ["calcfuel_local"] = "Calculate fuel using",
        ["max_cell_voltage"] = "Maximale Zellenspannung",
        ["full_cell_voltage"] = "Volle Zellenspannung",
        ["name"] = "Batterie",
        ["min_cell_voltage"] = "Minimale Zellenspannung",
        ["help_p1"] = "Die Batterieeinstellungen werden verwendet, um den Flugcontroller so zu konfigurieren, dass er die Batteriespannung ueberwacht und Warnungen ausgibt, wenn die Spannung unter ein bestimmtes Niveau faellt.",
        ["battery_capacity"] = "Batteriekapazitaet",
        ["warn_cell_voltage"] = "Warnung Zellenspannung",
        ["cell_count"] = "Zellenanzahl",
        ["consumption_warning_percentage"] = "Verbrauchswarnung %",
        ["timer"] = "Flugzeitalarm",
        ["voltage_multiplier"] = "Spannungsausgleich",
        ["kalman_multiplier"] = "Filter compensation",
        ["alert_type"] = "BEC or Rx Batt Voltage Alert",
        ["bec_voltage_alert"] = "BEC Alert Value",
        ["rx_voltage_alert"] = "RX Batt Alert Value"
      },
      ["profile_mainrotor"] = {
        ["gain"] = "Verstaerkung",
        ["help_p4"] = "Kreuzkopplungs-Frequenzgrenze: Frequenzgrenze fuer die Kompensation – ein hoeherer Wert fuehrt zu einer schnelleren Kompensationsreaktion.",
        ["collective_pitch_comp_short"] = "Kol. Pitch-Kompensation",
        ["cyclic_cross_coupling"] = "Zyklische Kreuzkopplung",
        ["collective_pitch_comp"] = "Kollektive Pitch-Kompensation",
        ["name"] = "Hauptrotor",
        ["cutoff"] = "Grenzwert",
        ["ratio"] = "Verhaeltnis",
        ["help_p1"] = "Kollektive Pitch-Kompensation: Erhoehen Sie diesen Wert, um das Kippmoment durch den Heckrotorzug beim Steigen auszugleichen.",
        ["help_p2"] = "Kreuzkopplungs-Verstaerkung: Entfernt Roll-Kopplung, wenn nur Hoehenruder (Elevator) angewendet wird.",
        ["help_p3"] = "Kreuzkopplungs-Verhaeltnis: Menge der angewandten Kompensation (Nick vs. Roll)."
      },
      ["sbusout"] = {
        ["title"] = "SBUS-Ausgang",
        ["help_fields_source"] = "Quellen-ID fuer den Mix, zaehlt von 0-15.",
        ["help_default_p4"] = "- Fuer Motoren 0, 1000 verwenden.",
        ["ch_prefix"] = "CH",
        ["channel_prefix"] = "KANAL ",
        ["saving"] = "Speichern",
        ["name"] = "SBUS-Ausgang",
        ["channel_page"] = "SBUS-Ausgang / CH",
        ["receiver"] = "Empfaenger",
        ["servo"] = "Servo",
        ["type"] = "Typ",
        ["saving_data"] = "Speichere Daten...",
        ["help_fields_max"] = "Der maximale PWM-Wert, der gesendet wird.",
        ["motor"] = "Motor",
        ["help_default_p5"] = "- Oder Sie koennen Ihre eigene Zuordnung anpassen.",
        ["help_default_p1"] = "Erweiterte Misch- und Kanalzuordnung konfigurieren, wenn SBUS-Ausgang auf einem seriellen Port aktiviert ist.",
        ["max"] = "Max",
        ["save_prompt"] = "Aktuelle Seite zum Flugcontroller speichern?",
        ["help_fields_min"] = "Der minimale PWM-Wert, der gesendet wird.",
        ["mixer"] = "Mischer",
        ["ok"] = "OK",
        ["cancel"] = "ABBRECHEN",
        ["help_default_p2"] = "- Fuer RX-Kanaele oder Servos (Breitband) 1000, 2000 oder 500,1000 fuer Schmalband-Servos verwenden.",
        ["save_settings"] = "Einstellungen speichern",
        ["min"] = "Min",
        ["help_default_p3"] = "- Fuer Mischregeln -1000, 1000 verwenden.",
        ["source"] = "Quelle"
      },
      ["profile_rescue"] = {
        ["help_p4"] = "Schweben: Wie viel Kollektiv benoetigt wird, um ein stabiles Schweben zu halten.",
        ["hover"] = "Schweben",
        ["collective"] = "Kollektiv",
        ["help_p2"] = "Abfangen: Wie viel Kollektiv und wie lange zum Stoppen des Sinkflugs verwendet wird.",
        ["climb"] = "Steigen",
        ["mode_enable"] = "Rettungsmodus aktivieren",
        ["help_p3"] = "Steigen: Wie viel Kollektiv benoetigt wird, um einen gleichmaessigen Steigflug zu halten – und wie lange.",
        ["help_p1"] = "Aufrichten: Richtet den Heli aus dem Rueckenflug auf, wenn der Rettungsmodus aktiviert wird.",
        ["flip_upright"] = "Aufrichten",
        ["flip"] = "Flip",
        ["level_gain"] = "Neigung",
        ["name"] = "Rettung",
        ["exit_time"] = "Abbruch",
        ["help_p5"] = "Flip: Wie lange gewartet wird, bevor der Rettungsmodus abgebrochen wird, falls der Flip (Aufrichten) nicht funktioniert.",
        ["help_p6"] = "Verstaerkungen: Wie stark der Heli kaempft, um in der Waagerechten zu bleiben, wenn der Rettungsmodus aktiviert wird.",
        ["fail_time"] = "Fehlzeit",
        ["pull_up"] = "Abfangen",
        ["rate"] = "Rate",
        ["help_p7"] = "Rate und Beschleunigung: Maximale Rotations- und Beschleunigungswerte beim Aufrichten waehrend der Rettung.",
        ["gains"] = "Verstaerkungen",
        ["time"] = "Zeit",
        ["accel"] = "Beschleunigung"
      },
      ["trim"] = {
        ["disable_mixer_message"] = "Gibt die Steuerung der Servos an den Flugcontroller zurueck.",
        ["tail_motor_idle"] = "Heckmotor-Leerlauf %",
        ["disable_mixer_override"] = "Mischer-Ueberschreibung deaktivieren",
        ["yaw_trim"] = "Gier-Trimmung %",
        ["enable_mixer_message"] = "Alle Servos auf ihre konfigurierte Mittelposition setzen. \r\n\r\nAlle Werte auf dieser Seite werden gespeichert, wenn die Servo-Trimmung angepasst wird.",
        ["mixer_override_disabling"] = "Mischer-Ueberschreibung wird deaktiviert...",
        ["roll_trim"] = "Roll-Trimmung %",
        ["pitch_trim"] = "Nick-Trimmung %",
        ["name"] = "Trimmung",
        ["help_p2"] = "Motorisiertes Heck: Falls ein motorisiertes Heck verwendet wird, koennen Sie hier die minimale Leerlaufdrehzahl und den Null-Gierpunkt einstellen.",
        ["mixer_override"] = "Mischer-Ueberschreibung",
        ["mixer_override_enabling"] = "Mischer-Ueberschreibung wird aktiviert...",
        ["enable_mixer_override"] = "Mischer-Ueberschreibung aktivieren",
        ["collective_trim"] = "Kollektiv-Trimmung %",
        ["help_p1"] = "Trimmungen verknuepfen: Verwenden Sie dies, um kleine Nivellierungsprobleme in Ihrer Taumelscheibe zu korrigieren. Normalerweise nur notwendig, wenn die Taumelscheibenanlenkungen nicht verstellbar sind."
      },
      ["governor"] = {
        ["help_p1"] = "Diese Parameter gelten global fuer den Drehzahlreger (Governor), unabhaengig vom verwendeten Profil.",
        ["handover_throttle"] = "Uebergabe-Gas%",
        ["spoolup_min_throttle"] = "Min. Spoolup-Gas%",
        ["recovery_time"] = "Erholungszeit",
        ["mode"] = "Modus",
        ["help_p2"] = "Jeder Parameter ist einfach ein Zeitwert in Sekunden fuer jede Regleraktion.",
        ["tracking_time"] = "Tracking-Zeit",
        ["name"] = "Governor",
        ["startup_time"] = "Startzeit",
        ["spoolup_time"] = "Spoolup-Zeit"
      },
      ["accelerometer"] = {
        ["help_p1"] = "Der Beschleunigungssensor wird verwendet, um den Winkel des Flugcontrollers in Bezug auf den Horizont zu messen. Diese Daten werden zur Stabilisierung des Fluggeraets und zur Bereitstellung der Selbstnivellierungsfunktion verwendet.",
        ["name"] = "Beschleunigungssensor",
        ["pitch"] = "Nick",
        ["msg_calibrate"] = "Beschleunigungssensor kalibrieren?",
        ["roll"] = "Roll"
      },
      ["gyro_alignment"] = {
        ["name"] = "Board-Ausrichtung",
        ["board_alignment"] = "Board-Ausrichtung",
        ["roll"] = "Roll",
        ["pitch"] = "Nick",
        ["yaw"] = "Gier",
        ["msg_calibrate"] = "Beschleunigungssensor kalibrieren? Dies setzt den Beschleunigungssensor zurueck und wendet die aktuellen Board-Ausrichtungseinstellungen an.",
        ["help_p1"] = "Das Board-Ausrichtungstool ermoeglicht es, die Board-Ausrichtungswinkel zu konfigurieren, um die Montageausrichtung des Flugcontrollers zu kompensieren.",
        ["help_p2"] = "Board-Ausrichtung: Passen Sie Roll-, Nick- und Gierwinkel (in Grad) an die physische Ausrichtung Ihres Flugcontrollers relativ zum Helikopterrahmen an.",
        ["help_p3"] = "Diese Einstellungen kompensieren Faelle, in denen der Flugcontroller nicht perfekt mit den Rahmenachsen des Helikopters ausgerichtet montiert ist.",
        ["help_p4"] = "Verwenden Sie die Tool-Schaltflaeche, um den Beschleunigungssensor nach Ausrichtungsaenderungen zu kalibrieren. Speichern Sie die Einstellungen im EEPROM, wenn Sie fertig sind."
      },
      ["rates"] = {
        ["help_table_5_p2"] = "Maximalrate: Maximale Rotationsgeschwindigkeit bei vollem Knueppelausschlag in Grad pro Sekunde.",
        ["actual"] = "ACTUAL",
        ["max_rate"] = "Max. Rate",
        ["help_table_4_p3"] = "Expo: Verringert die Empfindlichkeit nahe der Knueppelmitte fuer feinere Steuerung.",
        ["rate"] = "Rate",
        ["help_table_5_p1"] = "RC-Rate: Reduziert die Empfindlichkeit um die Knueppelmitte. Eine RC-Rate, die auf die Haelfte der Maximalrate eingestellt ist, ist linear. Ein niedrigerer Wert reduziert die Empfindlichkeit um die Knueppelmitte. Ein hoeherer Wert als die Haelfte der Maximalrate erhoeht auch die Maximalrate.",
        ["help_table_4_p2"] = "Maximalrate: Maximale Rotationsgeschwindigkeit bei vollem Knueppelausschlag in Grad pro Sekunde.",
        ["center_sensitivity"] = "Zentr. Empfindl.",
        ["rc_curve"] = "RC-Kurve",
        ["roll"] = "Roll",
        ["none"] = "KEINE",
        ["betaflight"] = "BETAFLIGHT",
        ["kiss"] = "KISS",
        ["help_table_1_p3"] = "Expo: Verringert die Empfindlichkeit nahe der Knueppelmitte fuer feinere Steuerung.",
        ["help_table_3_p2"] = "Rate: Erhoeht die maximale Rotationsgeschwindigkeit und verringert die Empfindlichkeit in der Mitte des Knueppelwegs.",
        ["help_table_2_p2"] = "Acro+: Erhoeht die maximale Rotationsgeschwindigkeit und verringert die Empfindlichkeit in der Mitte des Knueppelwegs.",
        ["superrate"] = "SuperRate",
        ["help_table_2_p3"] = "Expo: Verringert die Empfindlichkeit nahe der Knueppelmitte fuer feinere Steuerung.",
        ["raceflight"] = "RACEFLIGHT",
        ["yaw"] = "Gier",
        ["collective"] = "Kol",
        ["name"] = "Rates",
        ["help_table_5_p3"] = "Expo: Verringert die Empfindlichkeit nahe der Knueppelmitte fuer feinere Steuerung.",
        ["help_table_3_p3"] = "RC-Kurve: Verringert die Empfindlichkeit nahe der Knueppelmitte fuer feinere Steuerung.",
        ["expo"] = "Expo",
        ["help_table_1_p2"] = "SuperRate: Erhoeht die maximale Rotationsgeschwindigkeit und verringert gleichzeitig die Empfindlichkeit in der Mitte des Knueppelwegs.",
        ["help_default_p2"] = "Wir verwenden die folgenden Unteroptionen.",
        ["help_default_p1"] = "Standard: Diese Option bleibt erhalten, damit der Button fuer die Rates erscheint.",
        ["quick"] = "SCHNELL",
        ["pitch"] = "Nick",
        ["acroplus"] = "Acro+",
        ["help_table_1_p1"] = "RC-Rate: Maximale Rotationsgeschwindigkeit bei vollem Knueppelausschlag.",
        ["rc_rate"] = "RC-Rate",
        ["help_table_2_p1"] = "Rate: Maximale Rotationsgeschwindigkeit bei vollem Knueppelausschlag in Grad pro Sekunde.",
        ["help_table_4_p1"] = "Zentrale Empfindlichkeit: Reduziert die Empfindlichkeit um die Knueppelmitte. Wenn die zentrale Empfindlichkeit auf denselben Wert wie die Maximalrate gesetzt wird, ist die Reaktion linear. Ein niedrigerer Wert als die Maximalrate reduziert die Empfindlichkeit um die Knueppelmitte. Ein hoeherer Wert als die Maximalrate erhoeht die Maximalrate – nicht empfohlen, da dies zu Problemen in den Blackbox-Logs fuehrt.",
        ["help_table_0_p1"] = "Alle Werte sind auf null gesetzt, da keine RATE-TABELLE verwendet wird.",
        ["help_table_3_p1"] = "RC-Rate: Maximale Rotationsgeschwindigkeit bei vollem Knueppelausschlag."
      },
      ["mixer"] = {
        ["help_p1"] = "Passen Sie die Taumelscheibengeometrie, Phasenwinkel und Begrenzungen an.",
        ["collective_tilt_correction_pos"] = "Positiv",
        ["geo_correction"] = "Geo-Korrektur",
        ["swash_tta_precomp"] = "TTA-Vorkompensation",
        ["name"] = "Mischer",
        ["collective_tilt_correction_neg"] = "Negativ",
        ["tail_motor_idle"] = "Heckleerlauf-Gas%",
        ["swash_phase"] = "Phasenwinkel",
        ["collective_tilt_correction"] = "Kollektive Neigungskorrektur",
        ["swash_pitch_limit"] = "Gesamter Pitch-Limit"
      },
      ["about"] = {
        ["help_p1"] = "Diese Seite bietet einige nuetzliche Informationen, die Sie moeglicherweise angeben muessen, wenn Sie Unterstuetzung anfordern.",
        ["msgbox_credits"] = "Danksagungen",
        ["ethos_version"] = "Ethos-Version",
        ["rf_version"] = "Rotorflight-Version",
        ["fc_version"] = "FC-Version",
        ["name"] = "Ueber",
        ["supported_versions"] = "MSP-Versionen",
        ["license"] = "Sie duerfen die Software kopieren, verbreiten und modifizieren, solange Sie Aenderungen und Daten in den Quelldateien nachverfolgen. Jegliche Aenderungen oder Software, die GPL-lizenzierte Codebestandteile enthaelt (ueber den Compiler), muessen ebenfalls unter der GPL verfuegbar gemacht werden, zusammen mit Anleitungen zur Erstellung und Installation.",
        ["simulation"] = "Simulation",
        ["help_p2"] = "Fuer Unterstuetzung lesen Sie bitte zuerst die Hilfeseiten auf www.rotorflight.org",
        ["opener"] = "Rotorflight ist ein Open-Source-Projekt. Beitraege von Gleichgesinnten, die daran interessiert sind, diese Software weiter zu verbessern, sind willkommen und werden ermutigt. Man muss kein Hardcore-Programmierer sein, um zu helfen.",
        ["version"] = "Version",
        ["msp_version"] = "MSP-Version",
        ["credits"] = "Wichtige Mitwirkende, sowohl an der Rotorflight-Firmware als auch an dieser Software sind: Petri Mattila, Egon Lubbers, Rob Thomson, Rob Gayle, Phil Kaighin, Robert Burrow, Keith Williams, Bertrand Songis, Venbs Zhou... und viele weitere, die Stunden mit Tests und Feedback verbracht haben!",
        ["msp_transport"] = "MSP-Transport"
      },
      ["rates_advanced"] = {
        ["dyn_ceiling_gain"] = "DynMaxVerst",
        ["acc_limit"] = "BeschlGrenze",
        ["roll"] = "Roll",
        ["yaw_dynamics"] = "Gier-Dynamik",
        ["pitch"] = "Nick",
        ["col"] = "Kol",
        ["setpoint_boost_cutoff"] = "SollwertBoostGrenze",
        ["yaw_dynamic_deadband_gain"] = "Totband",
        ["rates_type"] = "Raten-Typ",
        ["setpoint_boost_gain"] = "SollwertBoostVerst",
        ["msg_reset_to_defaults"] = "Rate-Typ geaendert. Werte werden auf Standardwerte zurueckgesetzt.",
        ["yaw_dynamic_ceiling_gain"] = "Decke",
        ["yaw_boost"] = "Gier-Verstärkung",
        ["gain"] = "Verst.",
        ["rate_table"] = "Rate Tabelle",
        ["dynamics"] = "Dynamik",
        ["yaw"] = "Gier",
        ["yaw_dynamic_deadband_filter"] = "Filter",
        ["name"] = "Rates",
        ["cutoff"] = "Grenz.",
        ["help_rate_table"] = "Bitte waehlen Sie die Rates aus, den Sie verwenden moechten. Durch das Speichern wird die Auswahl auf das aktive Profil angewendet.",
        ["help_p1"] = "Raten-Typ: Waehlen Sie den Rate-Typ aus, mit dem Sie fliegen moechten. Raceflight und Actual sind die einfachsten.",
        ["pitch_boost"] = "Nick-Verstärkung",
        ["help_p2"] = "Dynamik: Wird unabhaengig vom Rate-Typ angewendet. Anpassen, um Heli-Bewegungen weicher zu machen, z. B. fuer Scale-Helis.",
        ["accel_limit"] = "Beschl.",
        ["dyn_deadband_filter"] = "DynTotzeitFilter",
        ["roll_boost"] = "Roll-Verstärkung",
        ["dyn_deadband_gain"] = "DynTotzeitVerst",
        ["collective_dynamics"] = "Kollektiv-Dynamik",
        ["roll_dynamics"] = "Roll-Dynamik",
        ["collective_boost"] = "Kollektiv-Verstärkung",
        ["pitch_dynamics"] = "Nick-Dynamik",
        ["response_time"] = "Reaktionszeit"
      },
      ["servos"] = {
        ["tbl_yes"] = "JA",
        ["enable_servo_override"] = "Servo-Ueberschreibung aktivieren",
        ["disabling_servo_override"] = "Deaktiviere Servo-Ueberschreibung...",
        ["help_tool_p3"] = "Minimum/Maximum: Passen Sie die Endpunkte des ausgewaehlten Servos an.",
        ["tail"] = "HECK",
        ["scale_negative"] = "Negatives Scaling",
        ["help_tool_p1"] = "Ueberschreiben: [*] Aktivieren Sie die Ueberschreibung, um Echtzeitaktualisierungen der Servo-Mittelposition zu ermoeglichen.",
        ["tbl_no"] = "NEIN",
        ["maximum"] = "Maximum",
        ["help_tool_p6"] = "Geschwindigkeit: Die Geschwindigkeit, mit der sich das Servo bewegt. Wird normalerweise nur fuer zyklische Servos verwendet, um eine gleichmaessige Bewegung der Taumelscheibe zu gewaehrleisten. Optional – alle Werte auf 0 lassen, wenn Sie unsicher sind.",
        ["help_fields_rate"] = "Servo-PWM-Frequenz.",
        ["cyc_pitch"] = "ZYK. PITCH",
        ["center"] = "Zentrum",
        ["minimum"] = "Minimum",
        ["speed"] = "Geschwindigkeit",
        ["help_fields_speed"] = "Servo-Geschwindigkeit in Millisekunden.",
        ["disable_servo_override"] = "Servo-Ueberschreibung deaktivieren",
        ["help_fields_scale_pos"] = "Positives Servo-Scaling.",
        ["saving_data"] = "Speichere Daten...",
        ["cyc_left"] = "ZYK. LINKS",
        ["saving"] = "Speichern",
        ["name"] = "Servos",
        ["help_tool_p5"] = "Rate: Die Frequenz, mit der das Servo am besten arbeitet – pruefen Sie dies beim Hersteller.",
        ["help_tool_p2"] = "Zentrum: Passen Sie die Mittelposition des Servos an.",
        ["enabling_servo_override"] = "Aktiviere Servo-Ueberschreibung...",
        ["servo_prefix"] = "SERVO ",
        ["reverse"] = "Umkehren",
        ["enable_servo_override_msg"] = "Die Servo-Ueberschreibung ermoeglicht es Ihnen, den Servo-Mittelpunkt in Echtzeit zu 'trimmen'.",
        ["cyc_right"] = "ZYK. RECHTS",
        ["help_default_p2"] = "Primaere Flugsteuerungen, die den Rotorflight-Mischer verwenden, werden im Abschnitt 'Mischer' angezeigt.",
        ["scale_positive"] = "Positives Scaling",
        ["help_default_p1"] = "Bitte waehlen Sie das Servo aus, das Sie konfigurieren moechten, aus der untenstehenden Liste.",
        ["servo_override"] = "Servo-Ueberschreibung",
        ["disable_servo_override_msg"] = "Gibt die Steuerung der Servos an den Flugcontroller zurueck.",
        ["help_fields_min"] = "Negativer Reiseweg des Servos.",
        ["help_default_p3"] = "Alle anderen Servos, die nicht vom primaeren Flugmischer gesteuert werden, werden im Abschnitt 'Andere Servos' angezeigt.",
        ["help_fields_mid"] = "Servo-Mittelposition als Pulsbreite.",
        ["help_fields_scale_neg"] = "Negatives Servo-Scaling.",
        ["rate"] = "Rate",
        ["help_tool_p4"] = "Scaling: Passen Sie den Bewegungsbereich des Servos fuer eine bestimmte Eingabe an.",
        ["help_fields_flags"] = "0 = Standard, 1 = Umkehren, 2 = Geo-Korrektur, 3 = Umkehren + Geo-Korrektur",
        ["geometry"] = "Geometrie",
        ["help_fields_max"] = "Positiver Reiseweg des Servos."
      },
      ["profile_autolevel"] = {
        ["acro_trainer"] = "Acro-Trainer",
        ["angle_mode"] = "Winkelmodus",
        ["max"] = "Max",
        ["name"] = "Autonivellierung",
        ["help_p1"] = "Acro-Trainer: Rueckstellkraft des Helikopters zurueck zur Waagerechten im Acro-Trainer-Modus .",
        ["horizon_mode"] = "Horizontmodus",
        ["gain"] = "Verstaerkung",
        ["help_p2"] = "Winkelmodus: Rueckstellkraft des Helikopters zurueck zur Waagerechten im Winkelmodus.",
        ["help_p3"] = "Horizontmodus: Rueckstellkraft des Helikopters zurueck zur Waagerechten im Horizontmodus."
      },
      ["filters"] = {
        ["filter_type"] = "Filtertyp",
        ["help_p4"] = "Dynamische Notch-Filter: Erstellt automatisch Notch-Filter innerhalb des minimalen und maximalen Frequenzbereichs.",
        ["notch_c"] = "Notch Anzahl",
        ["rpm_preset"] = "Typ",
        ["lowpass_1"] = "TP 1",
        ["rpm_min_hz"] = "Min. Freq.",
        ["help_p2"] = "Gyro-Tiefpass: Tiefpassfilter fuer das Gyro-Signal. In der Regel auf Standard belassen.",
        ["cutoff"] = "G-freq.",
        ["notch_1"] = "Notch 1",
        ["max_cutoff"] = "Max. Grenzfreq.",
        ["help_p3"] = "Gyro-Notch-Filter: Wird verwendet, um bestimmte Frequenzbereiche zu filtern. In den meisten Helis normalerweise nicht erforderlich.",
        ["lowpass_2"] = "TP 2",
        ["rpm_filter"] = "Drehzahl Filter",
        ["help_p1"] = "Normalerweise sollten Sie diese Seite nicht bearbeiten, ohne Ihre Blackbox-Logs zu ueberpruefen!",
        ["dyn_notch"] = "Dynamische Filter",
        ["notch_q"] = "Notch Q",
        ["lowpass_1_dyn"] = "TP 1 dyn.",
        ["notch_min_hz"] = "Min",
        ["notch_max_hz"] = "Max",
        ["notch_2"] = "Notch 2",
        ["name"] = "Filter",
        ["min_cutoff"] = "Min. Grenzfreq.",
        ["center"] = "Mitte"
      },
      ["status"] = {
        ["arming_disable_flag_3"] = "RX-Wiederherstellung fehlgeschlagen",
        ["arming_disable_flag_20"] = "RPM-Filter",
        ["arming_disable_flag_11"] = "Auslastung",
        ["arming_disable_flag_22"] = "DSHOT Bitbang",
        ["dataflash_free_space"] = "Freier Datenspeicher",
        ["arming_disable_flag_25"] = "Arming Schalter",
        ["erasing"] = "Loeschen",
        ["arming_disable_flag_9"] = "Boot-Grace-Zeit",
        ["megabyte"] = "MB",
        ["arming_disable_flag_17"] = "Paralyse",
        ["arming_disable_flag_5"] = "Governor",
        ["arming_disable_flag_8"] = "Winkel",
        ["arming_disable_flag_1"] = "Fail Safe",
        ["cpu_load"] = "CPU-Auslastung",
        ["arming_disable_flag_15"] = "BST",
        ["arming_disable_flag_12"] = "Kalibrierung",
        ["arming_disable_flag_19"] = "Rettung",
        ["arming_disable_flag_4"] = "Box Fail Safe",
        ["arming_disable_flag_24"] = "Motor-Protokoll",
        ["real_time_load"] = "Echtzeitauslastung",
        ["help_p2"] = "Um den Dataflash fuer mehr Speicherplatz fuer Log-Dateien zu loeschen, druecken Sie die mit '*' markierte Schaltflaeche im Menue.",
        ["arming_disable_flag_2"] = "RX Fail Safe",
        ["ok"] = "OK",
        ["arming_disable_flag_0"] = "Kein Gyro",
        ["arming_disable_flag_18"] = "GPS",
        ["help_p1"] = "Auf dieser Seite koennen Sie den aktuellen Status Ihres Flugcontrollers anzeigen. Dies kann hilfreich sein, um herauszufinden, warum Ihr Heli nicht schaerft.",
        ["arming_flags"] = "Arming-Flags",
        ["unsupported"] = "Nicht unterstuetzt",
        ["erase_prompt"] = "Moechten Sie den Datenspeicher loeschen?",
        ["erase"] = "Loeschen",
        ["arming_disable_flag_10"] = "Kein Pre-Arm",
        ["arming_disable_flag_21"] = "Neustart erforderlich",
        ["name"] = "Status",
        ["arming_disable_flag_13"] = "CLI",
        ["arming_disable_flag_14"] = "CMS-Menue",
        ["arming_disable_flag_16"] = "MSP",
        ["arming_disable_flag_7"] = "Gas",
        ["erasing_dataflash"] = "Datenspeicher wird geloescht...",
        ["arming_disable_flag_23"] = "ACC-Kalibrierung"
      },
      ["profile_pidbandwidth"] = {
        ["help_p1"] = "PID-Bandbreite: Gesamtbandbreite in Hz, die von der PID-Regler verwendet wird.",
        ["pitch"] = "P",
        ["yaw"] = "Y",
        ["name"] = "PID-Bandbreite",
        ["bterm_cutoff"] = "B-Term Grenzfrequenz",
        ["help_p3"] = "B-Term Grenzfrequenz: Grenzfrequenz des B-Terms in Hz.",
        ["dterm_cutoff"] = "D-Term Grenzfrequenz",
        ["help_p2"] = "D-Term Grenzfrequenz: Grenzfrequenz des D-Terms in Hz.",
        ["roll"] = "R"
      }
    },
    ["navigation_save"] = "SPEICHERN",
    ["menu_section_flight_tuning"] = "Flugabstimmung",
    ["error_timed_out"] = "Fehler: Zeitueberschreitung",
    ["check_rf_module_on"] = "Bitte ueberpruefen Sie, ob Ihr RF-Modul eingeschaltet ist.",
    ["msg_saving"] = "Speichern...",
    ["msg_save_not_commited"] = "Speicherung nicht im EEPROM uebernommen",
    ["menu_section_advanced"] = "Erweitert",
    ["msg_loading_from_fbl"] = "Daten vom Flugcontroller werden geladen...",
    ["msg_reload_settings"] = "Daten vom Flugcontroller neu laden?",
    ["menu_section_tools"] = "Werkzeuge",
    ["msg_connecting"] = "Verbindung wird hergestellt",
    ["msg_save_current_page"] = "Aktuelle Seite auf dem Flugcontroller speichern?",
    ["btn_ok_long"] = "                OK                ",
    ["check_discovered_sensors"] = "Bitte stellen Sie sicher, dass alle Sensoren erkannt wurden.",
    ["msg_loading"] = "Laedt...",
    ["check_heli_on"] = "Bitte ueberpruefen Sie, ob Ihr Heli eingeschaltet und der Empfaenger verbunden ist.",
    ["check_bg_task"] = "Bitte aktivieren Sie die Background-Task.",
    ["navigation_tools"] = "*",
    ["check_supported_version"] = "Diese Version des Lua-Skripts\nkann nicht mit dem ausgewaehlten Modell verwendet werden."
  },
  ["telemetry"] = {
    ["sensors"] = {
      ["attpitch"] = "P.Winkel",
      ["attroll"] = "R.Winkel",
      ["attyaw"] = "Y.Winkel",
      ["accx"] = "Beschl. X",
      ["accy"] = "Beschl. Z",
      ["accz"] = "Beschl. Z",
      ["groundspeed"] = "Boden­geschwindigkeit",
      ["esc_temp"] = "ESC Temperatur",
      ["rate_profile"] = "Rate-Profil",
      ["headspeed"] = "Drehzahl",
      ["altitude"] = "Hoehe",
      ["voltage"] = "Spannung",
      ["bec_voltage"] = "BEC Spannung",
      ["cell_count"] = "Zellenanzahl",
      ["governor"] = "Governor Status",
      ["adj_func"] = "Adj (Funktion)",
      ["fuel"] = "Kraftstoffstand",
      ["smartfuel"] = "Smart Fuel",
      ["rssi"] = "RSSI",
      ["link"] = "Verbindungsqualitaet",
      ["adj_val"] = "Adj (Wert)",
      ["arming_flags"] = "Arming-Flags",
      ["current"] = "Strom",
      ["throttle_pct"] = "Gas %",
      ["consumption"] = "Verbrauch",
      ["smartconsumption"] = "Intelligenter Verbrauch",
      ["pid_profile"] = "PID-Profil",
      ["mcu_temp"] = "MCU Temperatur",
      ["armdisableflags"] = "Arming-Disable"
    }
  },
  ["widgets"] = {
    ["bbl"] = {
      ["erase_dataflash"] = "Blackbox speicher loeschen",
      ["erasing"] = "Lösche...",
      ["display"] = "Anzeige",
      ["display_free"] = "Frei",
      ["display_used"] = "Verbraucht",
      ["display_outof"] = "Verbraucht/Gesamt"
    },
    ["craftimage"] = {
    },
    ["craftname"] = {
      ["txt_enter_craft_name"] = "Fluggeraetname eingeben",
      ["title"] = "FLUGGERAET NAME",
      ["txt_cancel"] = "Abbrechen",
      ["txt_save"] = "Speichern"
    },
    ["dashboard"] = {
      ["theme_load_error"] = "Ihr Design konnte nicht geladen werden. Das Standard-Design wird stattdessen verwendet.",
      ["validate_sensors"] = "BENOETIGTE SENSOREN NICHT VERFUEGBAR",
      ["unsupported_resolution"] = "ZU KLEIN",
      ["loading"] = "ROTORFLIGHT",
      ["waiting_for_connection"] = "VERBINDEN",
      ["check_bg_task"] = "HINTERGRUNDTASK",
      ["check_rf_module_on"] = "HF MODUL",
      ["check_discovered_sensors"] = "SENSOREN",
      ["no_link"] = "KEINE VERBINDUNG",
      ["reset_flight"] = "Flug zuruecksetzen",
      ["reset_flight_ask_title"] = "Flug zuruecksetzen",
      ["reset_flight_ask_text"] = "Sind Sie sicher, dass die den Flug zuruecksetzen moechten?",
      ["voltage"] = "Spannung",
      ["fuel"] = "Kraftstoff",
      ["headspeed"] = "Kopfdrehzahl",
      ["max"] = "Max",
      ["min"] = "Min",
      ["bec_voltage"] = "BEC Spannung",
      ["esc_temp"] = "ESC Temp",
      ["flight_duration"] = "Flugdauer",
      ["total_flight_duration"] = "Modell-Gesamtflugzeit",
      ["rpm_min"] = "RPM Min",
      ["rpm_max"] = "RPM Max",
      ["throttle_max"] = "Gas Max",
      ["current_max"] = "Strom Max",
      ["esc_max_temp"] = "ESC Temp Max",
      ["watts_max"] = "Max Watt",
      ["consumed_mah"] = "Verbrauchte mAh",
      ["fuel_remaining"] = "Verfuegbarer Kraftstoff",
      ["min_volts_cell"] = "Min Volt pro Zelle",
      ["link_min"] = "Verbindung Min",
      ["governor"] = "Governor",
      ["profile"] = "Profil",
      ["rates"] = "Raten",
      ["flights"] = "Fluege",
      ["lq"] = "LQ",
      ["time"] = "Zeit",
      ["blackbox"] = "Blackbox",
      ["throttle"] = "Gas",
      ["flight_time"] = "Flugzeit",
      ["rssi_min"] = "RSSI Min",
      ["current"] = "Strom",
      ["timer"] = "Timer",
      ["rpm"] = "RPM",
      ["min_voltage"] = "Min Spannung",
      ["max_voltage"] = "Max Spannung",
      ["min_current"] = "Min Strom",
      ["max_current"] = "Max Strom",
      ["max_tmcu"] = "Max T.MCU",
      ["max_emcu"] = "Max E.MCU",
      ["altitude"] = "Hoehe",
      ["altitude_max"] = "Hoehe Max",
      ["power"] = "Power",
      ["cell_voltage"] = "Zellenspannung",
      ["volts_per_cell"] = "Volt pro Zelle",
      ["warning"] = "Warnung",
      ["tx_batt"] = "TX Battery",
      ["link_max"] = "Link Max"
    },
    ["governor"] = {
      ["UNKNOWN"] = "UNBEKANNT",
      ["IDLE"] = "LEERLAUF",
      ["DISARMED"] = "DISARMED",
      ["OFF"] = "AUS",
      ["SPOOLUP"] = "HOCHLAUF",
      ["ACTIVE"] = "AKTIV",
      ["RECOVERY"] = "ERHOLUNG",
      ["THROFF"] = "GAS-AUS",
      ["LOSTHS"] = "KEIN SIGNAL",
      ["AUTOROT"] = "AUTOROTATION",
      ["DISABLED"] = "DEAKTIVIERT",
      ["BAILOUT"] = "NOTAUS"
    }
  }
}
