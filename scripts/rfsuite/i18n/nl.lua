--[[
 * Copyright (C) Rotorflight Project
 *
 * License GPLv3: https://www.gnu.org/licenses/gpl-3.0.en.html
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * Please do not edit these files directly. These files are generated
 * from JSON files contained within the folder:
 *
 *   bin/i18n/json/*.json
 *
 * Running the script:
 *
 *   lua54 json-to-lua.lua
 *
 * will create these files for you.
]]

return {
  ["reload"] = "herlaad",
  ["image"] = "foto",
  ["error"] = "fout",
  ["save"] = "opslaan",
  ["ethos"] = "ethos",
  ["version"] = "versie",
  ["bg_task_disabled"] = "bg task uitgeschakeld",
  ["no_link"] = "geen verbinding",
  ["background_task_disabled"] = "background task uitgeschakeld",
  ["no_sensor"] = "geen sensor",
  ["api"] = {
    ["RC_TUNING"] = {
      ["setpoint_boost_cutoff_2"] = "Boostdrempel voor het setpoint.",
      ["response_time_3"] = "Verhoog of verlaag de reactietijd van de rates om de helikopterbewegingen soepeler te maken.",
      ["accel_limit_4"] = "Maximale versnelling van de heli als reactie op een stickbeweging.",
      ["setpoint_boost_gain_4"] = "Boostversterking voor het setpoint.",
      ["yaw_dynamic_deadband_filter"] = "Het maximale filter toegepast op de dynamische yaw dode zone.",
      ["setpoint_boost_gain_3"] = "Boostversterking voor het setpoint.",
      ["response_time_2"] = "Verhoog of verlaag de reactietijd van de rates om de helikopterbewegingen soepeler te maken.",
      ["accel_limit_1"] = "Maximale versnelling van de heli als reactie op een stickbeweging.",
      ["setpoint_boost_cutoff_1"] = "Boostdrempel voor het setpoint.",
      ["setpoint_boost_cutoff_4"] = "Boostdrempel voor het setpoint.",
      ["setpoint_boost_gain_2"] = "Boostversterking voor het setpoint.",
      ["accel_limit_2"] = "Maximale versnelling van de heli als reactie op een stickbeweging.",
      ["yaw_dynamic_deadband_gain"] = "De maximale gain toegepast op de dynamische yaw dode zone.",
      ["accel_limit_3"] = "Maximale versnelling van de heli als reactie op een stickbeweging.",
      ["response_time_4"] = "Verhoog of verlaag de reactietijd van de rates om de helikopterbewegingen soepeler te maken.",
      ["setpoint_boost_cutoff_3"] = "Boostdrempel voor het setpoint.",
      ["setpoint_boost_gain_1"] = "Boostversterking voor het setpoint.",
      ["yaw_dynamic_ceiling_gain"] = "De maximale gain toegepast op het dynamische yaw plafond.",
      ["response_time_1"] = "Verhoog of verlaag de reactietijd van de rates om de helikopterbewegingen soepeler te maken."
    },
    ["RESCUE_PROFILE"] = {
      ["rescue_flip_gain"] = "Bepaald hoe agressief de heli flips bij een inverted rescue.",
      ["rescue_level_gain"] = "Bepaald hoe agressief de heli levels tijdens rescue.",
      ["tbl_off"] = "UIT",
      ["rescue_hover_collective"] = "Collective waarde voor hoover",
      ["rescue_max_setpoint_rate"] = "Limiet de rescue roll/pitch rotatiesnelheid. Grote heli's hebben lagere rotatiesnelheden benodigd.",
      ["tbl_flip"] = "FLIP",
      ["rescue_flip_mode"] = "Als de rescue wordt geactiveerd terwijl inverted, flip positief - of blijf inverted.",
      ["rescue_pull_up_time"] = "Wanneer rescue is geactiveerd, helicopter geeft collective omhoog voor deze tijd voordat heli flipt of overgaat naar klim stap.",
      ["tbl_noflip"] = "GEEN FLIP",
      ["rescue_exit_time"] = "Dit beperkt de snelle toepassing van negatieve collective als de helikopter tijdens de reddingsactie is gerold.",
      ["rescue_pull_up_collective"] = "Collective waarde voor omhoog klimmen.",
      ["rescue_max_setpoint_accel"] = "Limiet de acceleratie in een roll/pitch. Grotere heli's hebben een lagere acceleratie nodig.",
      ["tbl_on"] = "AAN",
      ["rescue_climb_collective"] = "Collective waarde voor rescue klim.",
      ["rescue_flip_time"] = " Wanneer de heli is in rescue en probeert te flippen wat niet binnen deze tijd lukt dan wordt rescue afgebroken.",
      ["rescue_climb_time"] = "Tijdsduur de klim collective wordt gegeven voor omgeschakeld wordt naar hover."
    },
    ["ESC_SENSOR_CONFIG"] = {
      ["hw4_current_offset"] = "Hobbywing v4 stroom afwijking instelling",
      ["tbl_off"] = "Uit",
      ["update_hz"] = "ESC telemetrie update rate",
      ["half_duplex"] = "Half duplex mode voor ESC telemetry",
      ["consumption_correction"] = "Pas het verbruik correctie aan",
      ["current_offset"] = "Stroomsensor afwijking instelling",
      ["voltage_correction"] = "Pas de voltage correctie aan",
      ["hw4_voltage_gain"] = "Hobbywing v4 voltage gain instelling",
      ["tbl_on"] = "Aan",
      ["hw4_current_gain"] = "Hobbywing v4 stroom gain instelling",
      ["current_correction"] = "Pas de stroom correctie aan",
      ["pin_swap"] = "Verwissel de TX en RX pins voor de esc telemetrie"
    },
    ["ESC_PARAMETERS_FLYROTOR"] = {
      ["tbl_alwayson"] = "Altijd Aan",
      ["throttle_min"] = "Minimale throttle waarde",
      ["tbl_disabled"] = "Uit",
      ["tbl_auto"] = "Auto",
      ["starting_torque"] = "Start koppel voor de motor",
      ["cell_count"] = "Aantal cellen in de batterij",
      ["motor_erpm_max"] = "Maximale RPM",
      ["throttle_max"] = "Maximale throttle waarde",
      ["tbl_ccw"] = "CCW",
      ["tbl_escgov"] = "Esc Governor",
      ["temperature_protection"] = "Temperatuur waarbij vermogen wordt verlaagd met 50%",
      ["tbl_automatic"] = "Automatisch",
      ["low_voltage_protection"] = "Voltage waarbij vermogen wordt verlaagd met 50%",
      ["tbl_cw"] = "CW",
      ["soft_start"] = "Soft start waarde",
      ["gov_i"] = "Integral gain voor de governor",
      ["timing_angle"] = "Timing voor de motor",
      ["response_speed"] = "Respons snelheid voor de motor",
      ["current_gain"] = "Gain waarde voor stroomsensor",
      ["tbl_extgov"] = "Externe Governor",
      ["buzzer_volume"] = "Buzzer volume",
      ["gov_d"] = "Derivative gain voor de governor",
      ["tbl_enabled"] = "Aan",
      ["gov_p"] = "Proportional gain voor de governor"
    },
    ["ESC_PARAMETERS_YGE"] = {
      ["tbl_alwayson"] = "Altijd aan",
      ["tbl_off"] = "Uit",
      ["tbl_modestore"] = "Heli Governor Store",
      ["tbl_modefree"] = "Free (Attention!)",
      ["tbl_modeglider"] = "Aero Glider",
      ["tbl_modeext"] = "Heli Ext Governor",
      ["tbl_modeheli"] = "Heli Governor",
      ["tbl_medium"] = "Gemiddeld",
      ["tbl_autonorm"] = "Auto Normaal",
      ["tbl_reverse"] = "Omgekeerd",
      ["tbl_modef3a"] = "Aero F3A",
      ["tbl_auto"] = "Auto",
      ["tbl_slowdown"] = "Vertraging",
      ["tbl_slow"] = "Langzaam",
      ["tbl_modeair"] = "Aero Motor",
      ["tbl_normal"] = "Normaal",
      ["tbl_on"] = "Aan",
      ["tbl_autoextreme"] = "Auto Extreme",
      ["tbl_autoefficient"] = "Auto Efficient",
      ["tbl_smooth"] = "Smooth",
      ["tbl_fast"] = "Snel",
      ["tbl_custom"] = "Custom (PC Defined)",
      ["tbl_cutoff"] = "Afsnijding",
      ["tbl_autopower"] = "Auto Power",
      ["tbl_unused"] = "*Unused*"
    },
    ["GOVERNOR_PROFILE"] = {
      ["governor_tta_gain"] = "TTA gain toegepast om rotortoerental te verhogen om de staart in negatieve richting te sturen (e.g. gemotoriseerde staart onder idle rpm).",
      ["governor_collective_ff_weight"] = "Collective precompensation weight - how much collective is mixed into the feedforward.",
      ["governor_i_gain"] = "PID loop I-term gain.",
      ["governor_cyclic_ff_weight"] = "Gewicht Cyclic precompensation - Hoeveel cyclic is mixed in de feedforward.",
      ["governor_f_gain"] = "Feedforward gain.",
      ["governor_gain"] = "Master PID loop gain.",
      ["governor_headspeed"] = "Doel rotortoerental voor het huidige profiel.",
      ["governor_min_throttle"] = "Minimum throttle de governor mag gebruiken.",
      ["governor_d_gain"] = "PID loop D-term gain.",
      ["governor_p_gain"] = "PID loop P-term gain.",
      ["governor_yaw_ff_weight"] = "Gewicht voor yaw precompensation - Hoeveel yaw is mixed in de feedforward.",
      ["governor_max_throttle"] = "Maximale throttle welke de governor mag gebruiken.",
      ["governor_tta_limit"] = "TTA max rotortoerental boven volledige rotortoerental."
    },
    ["PID_PROFILE"] = {
      ["bterm_cutoff_2"] = "B-term afsnijding in Hz.",
      ["dterm_cutoff_1"] = "D-term-afsnijding in Hz.",
      ["bterm_cutoff_1"] = "B-term afsnijding in Hz.",
      ["gyro_cutoff_1"] = "Totale bandbreedte van de PID-loop in Hz.",
      ["tbl_on"] = "Aan",
      ["dterm_cutoff_2"] = "D-term-afsnijding in Hz.",
      ["yaw_inertia_precomp_cutoff"] = "Afsnijding. Afgeleide cutoff-frequentie in stappen van 1/10 Hz. Bepaalt hoe scherp de precomp is. Hogere waarde is scherper.",
      ["offset_limit_0"] = "Harde limiet voor de High Speed ​​Integral offset in de PID-loop. De O-term zal nooit over deze limieten heen gaan.",
      ["cyclic_cross_coupling_ratio"] = "Hoeveelheid benodigde rol-to-pitch compensatie, versus pitch-roll.",
      ["yaw_precomp_cutoff"] = "Frequentie limiet voor alle yaw precompensation acties.",
      ["error_limit_0"] = "Harde limiet voor de hoekfout in de PID-loop. De absolute fout en dus de I-term zal nooit boven deze limieten uitkomen.",
      ["trainer_gain"] = "Bepaalt hoe agressief de helikopter terugkantelt naar de maximale hoek (indien deze wordt overschreden) in de Acro Trainer-modus.",
      ["tbl_rpy"] = "RPY",
      ["gyro_cutoff_2"] = "Totale bandbreedte van de PID-loop in Hz.",
      ["yaw_ccw_stop_gain"] = "Stop gain (PD) voor tegen de klok in rotatie.",
      ["trainer_angle_limit"] = "Beperk de maximale hoek waarin de helikopter kan rollen/hellen in de Acro Trainer-modus.",
      ["tbl_rp"] = "RP",
      ["yaw_cyclic_ff_gain"] = "Cyclic feedforward mixed in de yaw (cyclic-to-yaw precomp).",
      ["error_decay_time_cyclic"] = "Tijdconstante voor het afkappen van cyclische I-term. Hoger stabiliseert de hover, lager zal driften.",
      ["error_decay_limit_cyclic"] = "Maximale afkap snelheid voor cyclische I-term.",
      ["cyclic_cross_coupling_gain"] = "Hoeveelheid toegepaste compensatie voor pitch-to-roll-koppeling gain.",
      ["yaw_collective_dynamic_decay"] = "Vervaltijd voor de extra yaw-precomp op collectieve invoer.",
      ["pitch_collective_ff_gain"] = "Door de snelheid te verhogen, compenseert u de opwaartse beweging die ontstaat door de staartweerstand tijdens het klimmen.",
      ["iterm_relax_type"] = "Kies de assen waarin dit actief is. RP: Roll, Pitch. RPY: Roll, Pitch, Yaw.",
      ["offset_limit_1"] = "Harde limiet voor de High Speed ​​Integral offset in de PID-loop. De O-term zal nooit over deze limieten heen gaan.",
      ["iterm_relax_cutoff_1"] = "Helpt bounce back te verminderen na snelle stickbewegingen. Kan inconsistentie veroorzaken in kleine stickbewegingen als deze te laag is.",
      ["error_limit_1"] = "Harde limiet voor de hoekfout in de PID-loop. De absolute fout en dus de I-term zal nooit boven deze limieten uitkomen.",
      ["horizon_level_strength"] = "Bepaalt hoe agressief de helikopter terugkantelt naar horizontaal in de Horizonmodus.",
      ["error_limit_2"] = "Harde limiet voor de hoekfout in de PID-loop. De absolute fout en dus de I-term zal nooit boven deze limieten uitkomen.",
      ["iterm_relax_cutoff_2"] = "Helpt bounce back te verminderen na snelle stickbewegingen. Kan inconsistentie veroorzaken in kleine stickbewegingen als deze te laag is.",
      ["tbl_off"] = "Uit",
      ["yaw_collective_ff_gain"] = "Collective feedforward mixed in de yaw (collective-to-yaw precomp).",
      ["gyro_cutoff_0"] = "Totale bandbreedte van de PID-loop in Hz.",
      ["yaw_collective_dynamic_gain"] = "Een extra boost van yaw precomp met collective input.",
      ["cyclic_cross_coupling_cutoff"] = "Frequentielimiet voor de compensatie. Hogere waarde zal de compensatieactie sneller maken.",
      ["error_rotation"] = "Draait de huidige rol- en pitch-fout termen rond de yaw wanneer het toestel roteert. Dit wordt soms Piro-compensatie genoemd.",
      ["angle_level_limit"] = "Beperk de maximale hoek waarin de helikopter kan kantelen/rollen in de anglemodus.",
      ["yaw_cw_stop_gain"] = "Stop gain (PD) voor klok mee rotatie.",
      ["iterm_relax_cutoff_0"] = "Helpt bounce back te verminderen na snelle stickbewegingen. Kan inconsistentie veroorzaken in kleine stickbewegingen als deze te laag is.",
      ["yaw_inertia_precomp_gain"] = "Scalar gain. De sterkte van de main rotor inertia. Hogere waarde betekend meer precomp wordt toegepast op yaw besturing.",
      ["dterm_cutoff_0"] = "D-term-afsnijding in Hz.",
      ["angle_level_strength"] = "Bepaalt hoe agressief de helikopter terug kantelt naar horizontaal in de anglemodus.",
      ["bterm_cutoff_0"] = "B-term afsnijding in Hz.",
      ["error_decay_time_ground"] = "Verwijdert de controllerfout wanneer het toestel niet in de lucht is, om te voorkomen dat het toestel omvalt."
    },
    ["MIXER_CONFIG"] = {
      ["swash_trim_1"] = "Trim voor de tuimelschijf koppelingen. Alleen gebruiken wanneer de koppeling niet verstelbaar is.",
      ["tail_motor_idle"] = "Minimale throttle signaal welke wordt gestuurd naar de staartmotor. Precies hoog genoeg zodat de staartmotor niet stil staat",
      ["tail_center_trim"] = "Stel de trim van de staartrotor in om 0 graden bladhoek te bereiken, of staartmotor throttle voor 0 yaw voor gemotoriseerde staart.",
      ["tbl_cw"] = "CW",
      ["swash_tta_precomp"] = "Mixer precomp voor 0 yaw",
      ["swash_trim_2"] = "Trim voor de tuimelschijf koppelingen. Alleen gebruiken wanneer de koppeling niet verstelbaar is.",
      ["swash_geo_correction"] = "Past de positieve en negatieve collective aan totdat deze gelijk zijn.",
      ["swash_trim_0"] = "Trim voor de tuimelschijf koppelingen. Alleen gebruiken wanneer de koppeling niet verstelbaar is.",
      ["swash_phase"] = "De fase verschuiving voor de tuimelschijf.",
      ["collective_tilt_correction_pos"] = "Past de collective schaal correctie aan voor positive collective pitch.",
      ["collective_tilt_correction_neg"] = "Past de collective schaal correctie aan voor negatieve collective pitch.",
      ["tbl_ccw"] = "CCW",
      ["swash_pitch_limit"] = "Maximale cyclische en collective rotorbladhoek."
    },
    ["MOTOR_CONFIG"] = {
      ["minthrottle"] = "Deze PWM waarde wordt gestuurd naar de ESC/Servo op laag throttle",
      ["motor_pwm_protocol"] = "Het protocol wat gebruikt wordt om met de esc te communiceren",
      ["main_rotor_gear_ratio_0"] = "Motor tandwiel aantal tanden",
      ["maxthrottle"] = "Deze PWM waarde wordt gestuurd naar de ESC/Servo op volledig throttle",
      ["mincommand"] = "Deze PWM waarde wordt gestuurd wanneer de motor gestopt is",
      ["main_rotor_gear_ratio_1"] = "Hoofd tandwiel aantal tanden",
      ["tail_rotor_gear_ratio_1"] = "Autorotatie tandwiel aantal tanden",
      ["motor_pwm_rate"] = "De frequentie snelheid waarmee het throttle signaal wordt gestuurd naar de esc",
      ["tail_rotor_gear_ratio_0"] = "Staart tandwiel aantal tanden",
      ["motor_pole_count_0"] = "Het aantal magneten in de motor."
    },
    ["ESC_PARAMETERS_HW5"] = {
      ["tbl_cw"] = "CW",
      ["tbl_fixedwing"] = "Fixed Wing",
      ["tbl_disabled"] = "Uit",
      ["tbl_ccw"] = "CCW",
      ["tbl_heligov"] = "Heli Governor",
      ["tbl_softcutoff"] = "Soft Cutoff",
      ["tbl_proportional"] = "Proportional",
      ["tbl_heliext"] = "Heli Ext Governor",
      ["tbl_helistore"] = "Heli Governor Store",
      ["tbl_hardcutoff"] = "Hard Cutoff",
      ["tbl_normal"] = "Normaal",
      ["tbl_autocalculate"] = "Auto Calculate",
      ["tbl_enabled"] = "Aan",
      ["tbl_reverse"] = "Omgekeerd"
    },
    ["PILOT_CONFIG"] = {
      ["model_param1_value"] = "Stel deze in op de verwachtte flight tijd in seconds. De zender zal piepen als de tijd bereikt is."
    },
    ["BATTERY_INI"] = {
      ["calcfuel_local"] = "Calculate Fuel Using",
      ["tbl_off"] = "Current Sensor",
      ["tbl_on"] = "Voltage Sensor",
      ["sag_multiplier"] = "Raise or lower to adjust for the amount of voltage sag you see in flight.",
      ["kalman_multiplier"] = "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.",
      ["alert_off"] = "Off",
      ["alert_bec"] = "BEC",
      ["alert_rxbatt"] = "Rx Batt"
    },
    ["BATTERY_CONFIG"] = {
      ["vbatmincellvoltage"] = "Het minimale voltage wanneer laag voltage alarm wordt getriggerd.",
      ["vbatmaxcellvoltage"] = "Het maximale voltage per cel wanneer hoog voltage alarm wordt getriggerd.",
      ["vbatwarningcellvoltage"] = "Het voltage per cel wanneer het laag voltage alarm af gaat.",
      ["batteryCellCount"] = "Het aantal cellen in je batterij.",
      ["vbatfullcellvoltage"] = "Het nominale voltage van een volledig geladen cel.",
      ["batteryCapacity"] = "De Mah capaciteit van de batterij."
    },
    ["ACC_TRIM"] = {
      ["pitch"] = "Gebruik de trim als de heli niet stil hangt in de stabilisatie modes (angle, horizon, etc.).",
      ["roll"] = "Gebruik de trim als de heli niet stil hangt in de stabilisatie modes (angle, horizon, etc.)."
    },
    ["PID_TUNING"] = {
      ["pid_1_I"] = "Hoe nauwkeurig het systeem zijn positie vasthoudt.",
      ["pid_2_P"] = "Hoe nauwkeurig het systeem de gewenste instelwaarde volgt.",
      ["pid_2_I"] = "Hoe nauwkeurig het systeem zijn positie vasthoudt.",
      ["pid_1_O"] = "Wordt gebruikt om te voorkomen dat de heli gaat rollen bij gebruik van veel collective.",
      ["pid_1_F"] = "Helpt P-term te pushen op basis van stick input. Verhogen zal de respons scherper maken, maar kan overshoot veroorzaken.",
      ["pid_0_D"] = "Sterkte van demping van elke beweging op het systeem, inclusief externe invloeden. Vermindert ook overshoot.",
      ["pid_1_P"] = "Hoe nauwkeurig het systeem de gewenste instelwaarde volgt.",
      ["pid_0_I"] = "Hoe nauwkeurig het systeem zijn positie vasthoudt.",
      ["pid_2_B"] = "Extra boost op de feedforward zodat de heli beter reageert op snelle stickbewegingen.",
      ["pid_0_O"] = "Wordt gebruikt om te voorkomen dat de heli gaat rollen bij gebruik van veel collective.",
      ["pid_0_F"] = "Helpt P-term te pushen op basis van stick input. Verhogen zal de respons scherper maken, maar kan overshoot veroorzaken.",
      ["pid_2_F"] = "Helpt P-term te pushen op basis van stick input. Verhogen zal de respons scherper maken, maar kan overshoot veroorzaken.",
      ["pid_2_D"] = "Sterkte van demping van elke beweging op het systeem, inclusief externe invloeden. Vermindert ook overshoot.",
      ["pid_0_P"] = "Hoe nauwkeurig het systeem de gewenste instelwaarde volgt.",
      ["pid_1_D"] = "Sterkte van demping van elke beweging op het systeem, inclusief externe invloeden. Vermindert ook overshoot.",
      ["pid_0_B"] = "Extra boost op de feedforward zodat de heli beter reageert op snelle stickbewegingen.",
      ["pid_1_B"] = "Extra boost op de feedforward zodat de heli beter reageert op snelle stickbewegingen."
    },
    ["GOVERNOR_CONFIG"] = {
      ["tbl_govmode_standard"] = "STANDAARD",
      ["tbl_govmode_mode2"] = "MODE2",
      ["gov_tracking_time"] = "Tijdconstante voor rotortoerental veranderingen in seconden, van nul tot volledig rotortoerental.",
      ["tbl_govmode_passthrough"] = "PASSTHROUGH",
      ["tbl_govmode_mode1"] = "MODE1",
      ["gov_recovery_time"] = "Tijdconstante voor herstel opstart in seconden, van nul tot volledig rotortoerental.",
      ["gov_startup_time"] = "Tijdconstante voor een langzame opstart in seconden, van nul tot volledig rotortoerental, zonder rpm signaal.",
      ["gov_handover_throttle"] = "Governor activeert als throttle hoger is als deze waarde. Als throttle lager is wordt deze doorgestuurd naar de esc",
      ["gov_spoolup_time"] = "Tijdconstante voor een langzame opstart in seconden, van nul tot volledig rotortoerental, met rpm signaal.",
      ["gov_spoolup_min_throttle"] = "Minimale throttle wat gebruikt wordt voor de langzame opstart in procent. Voor elektrisch 5%, voor nitro zo hoog dat de clutch net aangrijpt meestal rond 10-15%.",
      ["tbl_govmode_off"] = "Uit"
    },
    ["RC_CONFIG"] = {
      ["rc_deflection"] = "Stick afwijking van het midden in microseconds (us).",
      ["rc_min_throttle"] = "Minimaal verwachte throttle (0% throttleoutput) van de radio, in microseconden (VS).",
      ["rc_max_throttle"] = "Maximum verwachte throttle (100% throttle output) van de radio, in microseconds (us).",
      ["rc_arm_throttle"] = "Throttle moet op of onder deze waarde in microseconden (us) staan ​​om arming toe te staan. Moet ten minste 10us lager zijn dan minimum throttle.",
      ["rc_yaw_deadband"] = "Deadband voor yaw control in microseconds (us).",
      ["rc_deadband"] = "Deadband voor cyclic control in microseconds (us).",
      ["rc_center"] = "Stick center in microseconds (us)."
    },
    ["FILTER_CONFIG"] = {
      ["gyro_soft_notch_cutoff_2"] = "Breedte van het filter in Hz.",
      ["gyro_lpf1_static_hz"] = "Lowpass filter cutoff frequentie in Hz.",
      ["tbl_none"] = "Geen",
      ["dyn_notch_max_hz"] = "Maximum frequenctie tot waar het filter stopt met filteren.",
      ["tbl_1st"] = "1ST",
      ["rpm_min_hz"] = "Minimum frequenctie voor het rpm filter.",
      ["dyn_notch_min_hz"] = "Minimum frequentie waar het filter start met filteren.",
      ["gyro_lpf1_dyn_max_hz"] = "Dynamisch filter maximale frequentie in Hz.",
      ["gyro_soft_notch_hz_2"] = "Frequentie waar het filter wordt toegepast.",
      ["gyro_soft_notch_cutoff_1"] = "Breedte van de het filter in Hz.",
      ["gyro_soft_notch_hz_1"] = "Frequentie waar het filter wordt toegepast.",
      ["dyn_notch_count"] = "Number of notches to apply.",
      ["dyn_notch_q"] = "Breedte van het notch filter in het spectrum.",
      ["gyro_lpf2_static_hz"] = "Lowpass filter cutoff frequentie in Hz.",
      ["gyro_lpf1_dyn_min_hz"] = "Dynamisch filter minimale frequentie in Hz.",
      ["tbl_2nd"] = "2ND",
      ["tbl_custom"] = "CUSTOM",
      ["tbl_low"] = "Laag",
      ["tbl_medium"] = "MEDIUM",
      ["tbl_high"] = "Hoog"
    },
    ["ESC_PARAMETERS_XDFLY"] = {
      ["tbl_jadegreen"] = "JADE GREEN",
      ["tbl_off"] = "Uit",
      ["tbl_low"] = "Low",
      ["tbl_orange"] = "ORANGE",
      ["tbl_fmfw"] = "Fixed Wing",
      ["tbl_ccw"] = "CCW",
      ["tbl_medium"] = "Medium",
      ["tbl_yellow"] = "YELLOW",
      ["tbl_reverse"] = "Omgekeerd",
      ["tbl_red"] = "Red",
      ["tbl_high"] = "High",
      ["tbl_auto"] = "Auto",
      ["tbl_cw"] = "CW",
      ["tbl_fmheli"] = "Helicopter",
      ["tbl_purple"] = "PURPLE",
      ["tbl_green"] = "GREEN",
      ["tbl_blue"] = "BLUE",
      ["tbl_slow"] = "Langzaam",
      ["tbl_normal"] = "Normaal",
      ["tbl_fast"] = "Snel",
      ["tbl_escgov"] = "ESC Governor",
      ["tbl_white"] = "WHITE",
      ["tbl_cyan"] = "CYAN",
      ["tbl_vslow"] = "Heel langzaam",
      ["tbl_extgov"] = "External Governor",
      ["tbl_pink"] = "PINK",
      ["tbl_fwgov"] = "Fixed Wing",
      ["tbl_on"] = "Aan"
    },
    ["ESC_PARAMETERS_SCORPION"] = {
      ["tbl_airplane"] = "Airplane mode",
      ["tbl_cw"] = "CW",
      ["tbl_off"] = "Uit",
      ["tbl_quad"] = "Quad mode",
      ["tbl_heligov"] = "Heli Governor",
      ["tbl_exbus"] = "Jeti Exbus",
      ["tbl_boat"] = "Boat mode",
      ["tbl_unsolicited"] = "Unsolicited",
      ["tbl_futsbus"] = "Futaba SBUS",
      ["tbl_ccw"] = "CCW",
      ["tbl_helistore"] = "Heli Governor (stored)",
      ["tbl_standard"] = "Standaard",
      ["tbl_on"] = "Aan",
      ["tbl_vbar"] = "VBar",
      ["tbl_vbargov"] = "VBar Governor",
      ["tbl_extgov"] = "External Governor"
    }
  },
  ["app"] = {
    ["btn_ok"] = "          OK           ",
    ["btn_close"] = "SLUITEN",
    ["navigation_menu"] = "MENU",
    ["menu_section_hardware"] = "Hardware",
    ["msg_please_disarm_to_save_warning"] = "Instellingen worden alleen opgeslagen in EEPROM na uitschakelen",
    ["msg_saving_settings"] = "Instellingen opslaan...",
    ["msg_saving_to_fbl"] = "Data opslaan op vliegcontroller...",
    ["navigation_reload"] = "Reload",
    ["menu_section_developer"] = "Ontwikkelaar",
    ["check_msp_version"] = "Het is niet mogelijk om de MSP versie te bepalen.",
    ["menu_section_about"] = "Over",
    ["msg_please_disarm_to_save"] = "Disarm voor opslaan om data integriteit te verzekeren.",
    ["unit_hertz"] = "Hz",
    ["msg_rebooting"] = "Opnieuw opstarten...",
    ["msg_save_settings"] = "Instellingen opslaan",
    ["btn_cancel"] = "ANNULEREN",
    ["msg_connecting_to_fbl"] = "Verbinden met vliegcontroller...",
    ["navigation_help"] = "?",
    ["modules"] = {
      ["stats"] = {
        ["name"] = "Status",
        ["totalflighttime"] = "Totale Flight tijd",
        ["flightcount"] = "Aantal vluchten",
        ["lastflighttime"] = "Tijd laatste vlucht",
        ["help_p1"] = "Gebruik deze module om de flight statistieken te updaten op de flight controller."
      },
      ["settings"] = {
        ["name"] = "Instellingen",
        ["no_themes_available_to_configure"] = "Geen themes beschikbaar om in te stellen",
        ["txt_audio_timer"] = "Timer",
        ["txt_audio_events"] = "Gebeurtenissen",
        ["txt_audio_switches"] = "Schakelaars",
        ["txt_iconsize"] = "Icon grootte",
        ["txt_general"] = "General",
        ["txt_text"] = "TEKST",
        ["txt_small"] = "KLEIN",
        ["txt_large"] = "Groot",
        ["txt_syncname"] = "Sync model naam",
        ["txt_devtools"] = "Developer Tools",
        ["txt_apiversion"] = "API Versie",
        ["txt_logging"] = "Loggen",
        ["txt_compilation"] = "Compileren",
        ["txt_loglocation"] = "Log locatie",
        ["txt_console"] = "CONSOLE",
        ["txt_consolefile"] = "CONSOLE & BESTAND",
        ["txt_loglevel"] = "Log niveau",
        ["txt_off"] = "uit",
        ["txt_info"] = "INFO",
        ["txt_debug"] = "DEBUG",
        ["txt_mspdata"] = "Log msp data",
        ["txt_queuesize"] = "Log MSP wachtlijst grootte",
        ["txt_memusage"] = "Log geheugengebruik",
        ["txt_batttype"] = "Tx Battery Options",
        ["txt_battdef"] = "Default",
        ["txt_batttext"] = "Text",
        ["txt_battdig"] = "Digital",
        ["dashboard"] = "Dashboard",
        ["dashboard_theme"] = "Thema",
        ["dashboard_theme_panel_global"] = "Standaard thema voor alle modellen",
        ["dashboard_theme_panel_model"] = "Optioneel thema voor dit model",
        ["dashboard_theme_panel_model_disabled"] = "Uitgeschakeld",
        ["dashboard_settings"] = "Instellingen",
        ["dashboard_theme_preflight"] = "Preflight Thema",
        ["dashboard_theme_inflight"] = "Inflight Thema",
        ["dashboard_theme_postflight"] = "Postflight Thema",
        ["audio"] = "Geluid",
        ["localizations"] = "Localisatie",
        ["txt_development"] = "Development",
        ["temperature_unit"] = "Temperatuur unit",
        ["altitude_unit"] = "Hoogte unit",
        ["celcius"] = "Celsius",
        ["fahrenheit"] = "Fahrenheit",
        ["meters"] = "Meters",
        ["feet"] = "Feet",
        ["warning"] = "Waarschuwing",
        ["governor_state"] = "Governor Status",
        ["arming_flags"] = "Arming Flags",
        ["voltage"] = "Voltage",
        ["pid_rates_profile"] = "PID/Rates Profiel",
        ["pid_profile"] = "PID Profiel",
        ["rate_profile"] = "Rate Profiel",
        ["esc_temperature"] = "ESC Temperatuur",
        ["esc_threshold"] = "Limiet (°)",
        ["bec_voltage"] = "BEC Voltage",
        ["bec_threshold"] = "Limiet (V)",
        ["fuel"] = "Brandstof",
        ["fuel_callout_default"] = "Standaard (Alleen per 10%)",
        ["fuel_callout_10"] = "Elke 10%",
        ["fuel_callout_20"] = "Elke 20%",
        ["fuel_callout_25"] = "Elke 25%",
        ["fuel_callout_50"] = "Elke 50%",
        ["fuel_callout_percent"] = "Oproep %",
        ["fuel_repeats_below"] = "Herhaalt onder 0%",
        ["fuel_haptic_below"] = "Trillen onder 0%",
        ["timer_alerting"] = "Timer Waarschuwing",
        ["timer_elapsed_alert_mode"] = "Timer afgelopen waarschuwing",
        ["timer_prealert_options"] = "Voor-timer waarschuwing opties",
        ["timer_prealert"] = "Voor-timer Waarschuwing",
        ["timer_alert_period"] = "Waarschuwings periode",
        ["timer_postalert_options"] = "Na-timer Waarschuwing opties",
        ["timer_postalert"] = "Na-timer waarschuwing",
        ["timer_postalert_period"] = "Waarschuwing periode",
        ["timer_postalert_interval"] = "Waarschuwing interval"
      },
      ["validate_sensors"] = {
        ["help_p1"] = "Deze tool probeert een beknopt overzicht te geven van alle sensoren die u niet ontvangt.",
        ["invalid"] = "INVALID",
        ["name"] = "Sensors",
        ["msg_repair"] = "Zet de benodigde sensoren aan in de vliegcontroller?",
        ["msg_repair_fin"] = "Is de vliegcontroller geconfigureerd? Het is waarschijnlijk benodigd om opnieuw 'Vind nieuwe sensoren' te gebruiken",
        ["ok"] = "OK",
        ["help_p2"] = "Met deze tool kunt u controleren of u de juiste sensoren verzendt."
      },
      ["msp_exp"] = {
        ["help_p1"] = "Deze tool biedt de mogelijkheid om een ​​aangepaste bytestring naar de flight controller te sturen. Het is handig voor ontwikkelaars bij het debuggen van waarden.",
        ["name"] = "MSP Experimenteel",
        ["help_p2"] = "Als je niet begrijpt wat je doet, gebruik het dan niet, want er kunnen rare dingen gebeuren."
      },
      ["esc_tools"] = {
        ["unknown"] = "Onbekend",
        ["name"] = "ESC Tools",
        ["please_powercycle"] = "Restart de esc...",
        ["mfg"] = {
          ["hw5"] = {
            ["esc"] = "ESC",
            ["brake_force"] = "Remkracht %",
            ["rotation"] = "Motor Draairichting",
            ["soft_start"] = "Langzame opstart",
            ["name"] = "Hobbywing V5",
            ["limits"] = "Limieten",
            ["bec_voltage"] = "BEC Spanning",
            ["gov_i_gain"] = "I-Gain",
            ["startup_time"] = "Opstart tijd",
            ["lipo_cell_count"] = "LiPo aantal cellen",
            ["restart_time"] = "Herstart tijd",
            ["volt_cutoff_type"] = "Lage spanning limiet",
            ["motor"] = "Motor",
            ["brake_type"] = "Rem type",
            ["brake"] = "Rem",
            ["governor"] = "Governor",
            ["advanced"] = "Geavanceerd",
            ["basic"] = "Basis",
            ["flight_mode"] = "Vlucht modus",
            ["auto_restart"] = "Auto Herstart",
            ["active_freewheel"] = "Active Freewheel",
            ["cutoff_voltage"] = "Uitschakelingsspanning",
            ["startup_power"] = "Opstart vermogen",
            ["other"] = "Ander",
            ["timing"] = "Motor timing",
            ["gov_p_gain"] = "P-Gain"
          },
          ["xdfly"] = {
            ["hv_bec_voltage"] = "HV BEC Spanning",
            ["gov"] = "Governor",
            ["brake_force"] = "Remkracht",
            ["sr_function"] = "SR Function",
            ["name"] = "XDFLY",
            ["lv_bec_voltage"] = "LV BEC Spanning",
            ["auto_restart_time"] = "Auto herstart tijd",
            ["acceleration"] = "Acceleratie",
            ["motor_direction"] = "Motor draairichting",
            ["smart_fan"] = "Smart Fan",
            ["governor"] = "Governor",
            ["advanced"] = "Geavanceerd",
            ["gov_i"] = "Gov-I",
            ["cell_cutoff"] = "Cell Cutoff",
            ["led_color"] = "LED Kleur",
            ["basic"] = "Basis",
            ["startup_power"] = "Opstart geluid",
            ["motor_poles"] = "Motor Polen",
            ["capacity_correction"] = "Capaciteit correctie",
            ["timing"] = "Motortiming",
            ["gov_p"] = "Gov-P"
          },
          ["flrtr"] = {
            ["gov"] = "Governor",
            ["motor_temp_sensor"] = "Motor temp sensor",
            ["starting_torque"] = "Start koppel",
            ["cell_count"] = "Aantal cellen",
            ["gov_p"] = "Gov-P",
            ["motor_erpm_max"] = "Motor ERPM max",
            ["name"] = "FLYROTOR",
            ["low_voltage_protection"] = "Lage spanning beveil.",
            ["gov_d"] = "Gov-D",
            ["telemetry_protocol"] = "Telemetry protocol",
            ["motor_direction"] = "Motor draairichting",
            ["throttle_protocol"] = "Throttle protocol",
            ["soft_start"] = "Langzame opstart",
            ["other"] = "Ander",
            ["temperature_protection"] = "Temperatuur beveilig.",
            ["buzzer_volume"] = "Piep volume",
            ["timing_angle"] = "Motor Timing",
            ["governor"] = "Governor",
            ["advanced"] = "Geavanceerd",
            ["gov_i"] = "Gov-I",
            ["bec_voltage"] = "BEC spanning",
            ["fan_control"] = "Fan besturing",
            ["basic"] = "Basis",
            ["current_gain"] = "Stroom gain",
            ["led_color"] = "LED kleur",
            ["motor_temp"] = "Motor temperatuur",
            ["response_speed"] = "Reactiesnelheid",
            ["battery_capacity"] = "Batterij capaciteit"
          },
          ["scorp"] = {
            ["esc_mode"] = "ESC Modus",
            ["min_voltage"] = "Min Spanning",
            ["rotation"] = "Motor draairichting",
            ["telemetry_protocol"] = "Telemetrie Protocol",
            ["name"] = "Scorpion",
            ["runup_time"] = "Opstarttijd",
            ["motor_startup_sound"] = "Motor opstart geluid",
            ["gov_integral"] = "Gov Integral",
            ["gov_proportional"] = "Gov Proportional",
            ["cutoff_handling"] = "Stop beveiliging",
            ["bailout"] = "Bailout",
            ["limits"] = "Limieten",
            ["soft_start_time"] = "Langzame opstart tijd",
            ["advanced"] = "Geavanceerd",
            ["bec_voltage"] = "BEC Spanning",
            ["extra_msg_save"] = "Herstart de ESC om de wijzigingen op te slaan",
            ["basic"] = "Basis",
            ["max_current"] = "Max Stroom",
            ["max_temperature"] = "Max Temperatuur",
            ["protection_delay"] = "Beveiliging delay",
            ["max_used"] = "Max Gebruikt"
          },
          ["yge"] = {
            ["esc_mode"] = "ESC Modus",
            ["esc"] = "ESC",
            ["current_limit"] = "Stroom limiet",
            ["f3c_auto"] = "F3C Autorotation",
            ["name"] = "YGE",
            ["max_start_power"] = "Max Start vermogen",
            ["lv_bec_voltage"] = "BEC Spanning",
            ["pinion_teeth"] = "Pinion Tabdeb",
            ["auto_restart_time"] = "Auto herstart tijd",
            ["main_teeth"] = "Hoofdtandwiel tanden",
            ["other"] = "Andere",
            ["limits"] = "Limieten",
            ["cell_cutoff"] = "Cell Cutoff",
            ["throttle_response"] = "Throttle Reactie",
            ["stick_zero_us"] = "Stick nul",
            ["advanced"] = "Geavanceerd",
            ["gov_i"] = "Gov-I",
            ["motor_pole_pairs"] = "Motor Polen pair",
            ["stick_range_us"] = "Stick bereik",
            ["basic"] = "Basis",
            ["min_start_power"] = "Min Start vermogen",
            ["active_freewheel"] = "Active Freewheel",
            ["direction"] = "Motor draairichting",
            ["timing"] = "Motor Timing",
            ["gov_p"] = "Gov-P"
          }
        },
        ["searching"] = "Zoeken"
      },
      ["pids"] = {
        ["help_p1"] = "FeedForward (Roll/Pitch): Begin bij 70, verhoog tot stops scherp zijn zonder drift. Houd roll en pitch gelijk.",
        ["o"] = "O",
        ["pitch"] = "Pitch",
        ["i"] = "I",
        ["yaw"] = "Yaw",
        ["roll"] = "Roll",
        ["help_p5"] = "Test & Adjust: Vlieg, observeer en optimaliseer voor de beste prestaties onder reële omstandigheden.\n\n",
        ["p"] = "P",
        ["f"] = "F",
        ["name"] = "PIDs",
        ["help_p2"] = "I Gain (Roll/Pitch): Verhoog geleidelijk voor stabiele piro-pitch pumps. Te hoog veroorzaakt wiebelen; match rol-/pitchwaarden.",
        ["d"] = "D",
        ["b"] = "B",
        ["help_p4"] = "Tail Stop Gain (CW/CCW): Afzonderlijk instelbaar voor een zuivere stop in beide richtingen.",
        ["help_p3"] = "Tail P/I/D Gains: Verhoog P tot een lichte wag in hurricanes, en ga dan iets terug. Verhoog I tot de staart locked-in is in harde bewegingen (te hoog veroorzaakt langzame wag). Pas D aan voor soepele stops: hoger voor langzame servo's, lager voor snelle."
      },
      ["msp_speed"] = {
        ["seconds_600"] = "  600S  ",
        ["avg_query_time"] = "Average query time",
        ["seconds_30"] = "  30S  ",
        ["name"] = "MSP Speed",
        ["max_query_time"] = "Maximum query time",
        ["help_p1"] = "Deze tool probeert de kwaliteit van uw MSP-datalink te bepalen door binnen 30 seconden zoveel mogelijk grote MSP-query's uit te voeren.\n\n",
        ["retries"] = "Retries",
        ["checksum_errors"] = "Checksum errors",
        ["test_length"] = "Test length",
        ["start"] = "Start",
        ["memory_free"] = "Memory free",
        ["start_prompt"] = "Would you like to start the test? Choose the test run time below.",
        ["rf_protocol"] = "RF protocol",
        ["min_query_time"] = "Minimum query time",
        ["seconds_120"] = "  120S  ",
        ["seconds_300"] = "  300S  ",
        ["testing"] = "Testing",
        ["successful_queries"] = "Successful queries",
        ["timeouts"] = "Timeouts",
        ["testing_performance"] = "Testing MSP performance...",
        ["total_queries"] = "Total queries"
      },
      ["copyprofiles"] = {
        ["profile_type"] = "Profiel Type",
        ["profile_type_pid"] = "PID",
        ["profile_type_rate"] = "Rate",
        ["msgbox_save"] = "Instellingen opslaan",
        ["name"] = "Kopieer Profiel",
        ["help_p1"] = "Kopieer PID profiel of Hoeksnelheid profiel van bron naar doel.",
        ["dest_profile"] = "Doel. Profiel",
        ["source_profile"] = "Bron Profiel",
        ["msgbox_msg"] = "Sla huidige pagina op in de vliegcontroller?",
        ["help_p2"] = "Kies de bron en doel en klik op opslaan om het kopieren te starten.\n\n"
      },
      ["esc_motors"] = {
        ["min_throttle"] = "0% Throttle PWM waarde",
        ["tail_motor_ratio"] = "Tail Ratio",
        ["max_throttle"] = "100% Throttle PWM waarde",
        ["main_motor_ratio"] = "Main Ratio",
        ["pinion"] = "Pinion",
        ["main"] = "Main gear",
        ["help_p1"] = "Motor en esc functies.",
        ["rear"] = "Achter",
        ["front"] = "Voor",
        ["voltage_correction"] = "Spanning correctie",
        ["mincommand"] = "Motor Stop PWM waarde",
        ["name"] = "ESC/Motor",
        ["motor_pole_count"] = "Motor Polen",
        ["current_correction"] = "Stroom correctie",
        ["consumption_correction"] = "Verbruik correctie"
      },
      ["radio_config"] = {
        ["deflection"] = "Deflection",
        ["max_throttle"] = "Max",
        ["stick"] = "Stick",
        ["arming"] = "Arming",
        ["yaw_deadband"] = "Yaw",
        ["cyclic"] = "Cyclic",
        ["name"] = "Radio Config",
        ["help_p1"] = "Configureer uw radio settings. Stick center, arm, throttle hold, en throttle cut.\n",
        ["min_throttle"] = "Min",
        ["throttle"] = "Throttle",
        ["deadband"] = "Deadband",
        ["center"] = "Center"
      },
      ["profile_select"] = {
        ["help_p1"] = "Stel het huidige pid profiel of rateprofiel in dat u wilt gebruiken.",
        ["rate_profile"] = "Rate Profiel",
        ["pid_profile"] = "PID profiel",
        ["save_prompt"] = "Sla huidige pagina op in de vliegcontroller?",
        ["save_prompt_local"] = "Sla huidige instellingen op?",
        ["cancel"] = "Afbreken",
        ["name"] = "Selecteer profiel",
        ["save_settings"] = "Instellingen opslaan",
        ["ok"] = "OK",
        ["help_p2"] = "Als u een schakelaar op uw radio gebruikt om de pid- of ratemodus te wijzigen, wordt deze keuze genegeerd zodra u de schakelaar omzet.\n"
      },
      ["profile_governor"] = {
        ["tail_torque_assist"] = "Tail Torque Assist",
        ["p"] = "P",
        ["i"] = "I",
        ["yaw"] = "Yaw",
        ["cyc"] = "Cyc",
        ["f"] = "F",
        ["name"] = "Governor",
        ["d"] = "D",
        ["help_p1"] = "Max rotortoerental: Rotortoerental doel bij 100% throttle",
        ["help_p6"] = "Tail Torque Assist: Voor gemotoriseerde staart. Gains en minimale rotortoerental nemen toe voorhoofdrotorkoppel voor yaw-assistentie./n",
        ["help_p4"] = "Precomp: Governor precomp gain voor yaw, cyclic, en collective inputs.",
        ["max_throttle"] = "Max throttle",
        ["full_headspeed"] = "Max rotortoerental",
        ["precomp"] = "Precomp",
        ["gain"] = "PID master gain",
        ["disabled_message"] = "Rotorflight governor is niet aangezet",
        ["help_p3"] = "Gains: Fijntuning van de governor.",
        ["col"] = "Col",
        ["min_throttle"] = "Min throttle",
        ["tta_limit"] = "Limit",
        ["help_p2"] = "PID master gain: Hoe hard de governor werkt om de RPM in stand te houden.",
        ["gains"] = "Gains",
        ["help_p5"] = "Max throttle: Het maximale throttle % de governor mag gebruiken.",
        ["tta_gain"] = "Gain"
      },
      ["profile_tailrotor"] = {
        ["gain"] = "Gain",
        ["help_p4"] = "Collective FF Gain: Tail precompensation voor collective inputs.",
        ["collective_impulse_ff"] = "Collective Impulse FF",
        ["help_p2"] = "Precomp Cutoff: Frequentie limiet voor alle staart precompensatie acties.",
        ["cutoff"] = "Cutoff",
        ["help_p3"] = "Cyclic FF Gain: Tail precompensation voor cyclic inputs.",
        ["help_p1"] = "Staart Stop Gain: Hogere stop gain zal de tail stop agressiever maken, maar kan oscillaties veroorzaken als deze te hoog is. Pas CW of CCW aan om de yaw stops gelijkmatiger te maken.",
        ["inertia_precomp"] = "Inertia Precomp",
        ["cyclic_ff_gain"] = "Cyclic FF gain",
        ["help_p5"] = "Collective Impuls FF: Impuls staart precompensatie voor collectieve inputs. Als u extra precompensatie nodig met begin van de klim.\n\n",
        ["cw"] = "CW",
        ["ccw"] = "CCW",
        ["yaw_stop_gain"] = "Staart stop gain",
        ["precomp_cutoff"] = "Precomp Cutoff",
        ["collective_ff_gain"] = "Collective FF gain",
        ["name"] = "Staart Rotor",
        ["decay"] = "Decay"
      },
      ["profile_pidcontroller"] = {
        ["help_p4"] = "Error rotation: Zorg ervoor dat fouten tussen alle assen worden gedeeld.",
        ["ground_error_decay"] = "Ground Error Decay",
        ["yaw"] = "Y",
        ["inflight_error_decay"] = "Inflight Error Decay",
        ["help_p2"] = "Error limit: Hoek limiet voor I-term.",
        ["error_limit"] = "Error limit",
        ["help_p3"] = "Offset limit: Hoek limiet voor High Speed Integral (O-term).",
        ["cutoff_point"] = "Cut-off point",
        ["limit"] = "Limit",
        ["iterm_relax"] = "I-term relax",
        ["hsi_offset_limit"] = "HSI Offset limit",
        ["pitch"] = "P",
        ["name"] = "PID Controller",
        ["error_rotation"] = "Error rotation",
        ["roll"] = "R",
        ["help_p5"] = "I-term relax: Beperk de ophoping van I-term tijdens snelle bewegingen - helpt de bounce back na snelle stickbewegingen te verminderen. Moet over het algemeen lager zijn voor grote heli's en kan hoger zijn voor kleine heli's. Het beste is om alleen zoveel te verminderen als nodig is voor uw vliegstijl.\n\n",
        ["time"] = "Time",
        ["help_p1"] = "Error decay ground: PID-verval om te voorkomen dat de helikopter omvalt als hij op de grond staat."
      },
      ["logs"] = {
        ["help_logs_p2"] = "Let op. Om logging in te schakelen, is het essentieel dat u de volgende sensoren hebt ingeschakeld.",
        ["name"] = "Logs",
        ["help_logs_p1"] = "Selecteer een logbestand uit onderstaande lijst.",
        ["msg_no_logs_found"] = "Geen log bestand gevonden",
        ["help_logs_tool_p1"] = "Gebruik de schuifbalk om door de grafiek te navigeren.\n",
        ["help_logs_p3"] = "- arm status, voltage, headspeed, current, esc temperature"
      },
      ["battery"] = {
        ["calcfuel_local"] = "Calculate fuel using",
        ["max_cell_voltage"] = "Max Cel Spanning",
        ["full_cell_voltage"] = "Volle Cell Spanning",
        ["name"] = "Batterij",
        ["min_cell_voltage"] = "Min Cel Spanning",
        ["help_p1"] = "De batterij instellingen worden ingesteld zodat de vliegcontroller de batterij spanning kan monitoren en waarschuwingen kan geven als de spanning onder een bepaald niveau komt.\n\n",
        ["battery_capacity"] = "Batterij Capaciteit",
        ["warn_cell_voltage"] = "Waarschuwing Cel Spanning",
        ["cell_count"] = "Aantal cellen",
        ["consumption_warning_percentage"] = "Verbruik Waarschuwing %",
        ["timer"] = "Flight Tijd",
        ["voltage_multiplier"] = "Sag compensatie",
        ["kalman_multiplier"] = "Filter compensation",
        ["alert_type"] = "BEC or Rx Batt Voltage Alert",
        ["bec_voltage_alert"] = "BEC Alert Value",
        ["rx_voltage_alert"] = "RX Batt Alert Value"
      },
      ["profile_mainrotor"] = {
        ["gain"] = "Gain",
        ["help_p4"] = "Cross Coupling Freq. Limit: Frequentie limiet voor de compensatie, Hogere waarde wil de compensatie actie sneller.\n\n",
        ["collective_pitch_comp_short"] = "Col. Pitch Compensation",
        ["cyclic_cross_coupling"] = "Cyclic Cross coupling",
        ["collective_pitch_comp"] = "Collective Pitch Compensation",
        ["name"] = "Hoofdrotor",
        ["cutoff"] = "Cutoff",
        ["ratio"] = "Ratio",
        ["help_p1"] = "Collective Pitch Compensation: Verhogen compenseert voor de pitch beweging wat wordt veroorzaakt door staart drag bij het klimmen.",
        ["help_p2"] = "Cross Coupling Gain: Verwijderd Roll koppeling wanneer alleen elevator wordt gebruikt.",
        ["help_p3"] = "Cross Coupling Ratio: Hoeveelheid compensatie (pitch vs roll) toe te passen."
      },
      ["sbusout"] = {
        ["title"] = "SBUS Output",
        ["help_fields_source"] = "Source id voor de mix, optellend van 0-15.",
        ["help_default_p4"] = "- For motoren, gebruik 0, 1000.",
        ["ch_prefix"] = "CH",
        ["channel_prefix"] = "CHANNEL ",
        ["saving"] = "Opslaan",
        ["name"] = "SBUS Out",
        ["channel_page"] = "Sbus out / CH",
        ["receiver"] = "Receiver",
        ["servo"] = "Servo",
        ["type"] = "Type",
        ["saving_data"] = "Opslaan...",
        ["help_fields_max"] = "De maximum pwm waarde welke gestuurd wordt.\n\n",
        ["motor"] = "Motor",
        ["help_default_p5"] = "- Of je kunt een eigen mapping gebruiken.",
        ["help_default_p1"] = "Configureer geavanceerde mixing en kanaal mapping als je SBUS uitgang aangezet hebt op een seriele poort.",
        ["max"] = "Max",
        ["save_prompt"] = "Sla huidige pagina op in de vliegcontroller?",
        ["help_fields_min"] = "De minimum pwm waarde welke gestuurd wordt.",
        ["mixer"] = "Mixer",
        ["ok"] = "OK",
        ["cancel"] = "Afbreken",
        ["help_default_p2"] = "- Voor RX kanalen of servos (wideband), gebruik 1000, 2000 of 500,1000 voor narrow band servos.",
        ["save_settings"] = "Instellingen opslaan",
        ["min"] = "Min",
        ["help_default_p3"] = "- Voor mixer rules, gebruik -1000, 1000.",
        ["source"] = "Source"
      },
      ["profile_rescue"] = {
        ["help_p4"] = "Hover: Hoeveel collective nodig is voor stabiele hover.",
        ["hover"] = "Hover",
        ["collective"] = "Collective",
        ["help_p2"] = "Omhoog: Hoeveel collective en voor hoelang.",
        ["climb"] = "Klimmen",
        ["mode_enable"] = "Rescue mode Aan",
        ["help_p3"] = "Klimmen: Hoeveel collective om continue te klimmen - en hoelang.",
        ["help_p1"] = "Omkeren naar rechtop: Keert de heli rechtop als rescue wordt geactiveerd.",
        ["flip_upright"] = "Omkeren naar rechtop",
        ["flip"] = "Omkeren",
        ["level_gain"] = "Level",
        ["name"] = "Rescue",
        ["exit_time"] = "Exit tijd",
        ["help_p5"] = "Flip: Hoe lang moet je wachten voordat rescue afgebroken wordt omdat de flip niet werkte?.",
        ["help_p6"] = "Gains: Hoe hard de heli moet vechten om de heli horizontaal te houden als je rescue inschakelt?",
        ["fail_time"] = "Fail tijd",
        ["pull_up"] = "Omhoog",
        ["rate"] = "Rate",
        ["help_p7"] = "Rate and Accel: Maximale rotatie- en acceleratiesnelheden bij het nivelleren tijdens een reddingsactie./n/n",
        ["gains"] = "Gains",
        ["time"] = "Tijd",
        ["accel"] = "Accel"
      },
      ["trim"] = {
        ["disable_mixer_message"] = "Geef de besturing van de servo's terug aan de vliegcontroller.",
        ["tail_motor_idle"] = "Tail Motor idle  %",
        ["disable_mixer_override"] = "Schakel mixer overschrijven uit",
        ["yaw_trim"] = "Yaw. trim %",
        ["enable_mixer_message"] = "Zet alle servo's in de geconfigureerde middenpositie. \r\n\r\nDit heeft tot gevolg dat alle waarden op deze pagina worden opgeslagen bij het aanpassen van de servo-trim.",
        ["mixer_override_disabling"] = "Uitschakelen mixer overschrijven...",
        ["roll_trim"] = "Roll trim %",
        ["pitch_trim"] = "Pitch trim %",
        ["name"] = "Trim",
        ["help_p2"] = "Gemotoriseerde staart: Als u een gemotoriseerde staart gebruikt, kunt u hiermee het minimale stationair toerental en de nul yaw instellen.\n\n",
        ["mixer_override"] = "Mixer Override",
        ["mixer_override_enabling"] = "Inschakelen mixer overschrijven...",
        ["enable_mixer_override"] = "Schakel mixer overschrijven in",
        ["collective_trim"] = "Col. trim %",
        ["help_p1"] = "Link trims: Gebruiken om kleine afwijkingen in uw swashplate bij te werken. Wordt doorgaans alleen gebruikt als de swashlinks niet verstelbaar zijn."
      },
      ["governor"] = {
        ["help_p1"] = "Deze parameters zijn van toepassing op de governor, ongeacht het gebruikte profiel.",
        ["handover_throttle"] = "Overdracht throttle%",
        ["spoolup_min_throttle"] = "Min opstart throttle%",
        ["recovery_time"] = "Herstel tijd",
        ["mode"] = "Mode",
        ["help_p2"] = "Elke parameter is een tijdswaarde in seconden voor elke governor actie.\n\n",
        ["tracking_time"] = "Tracking time",
        ["name"] = "Governor",
        ["startup_time"] = "Opstarttijd",
        ["spoolup_time"] = "Optoer tijd"
      },
      ["accelerometer"] = {
        ["help_p1"] = "De accelerometer wordt gebruikt om de hoek van de flight controller te meten ten opzichte van de horizon. Deze data wordt gebruikt om het vliegtuig te stabiliseren en zelf leveling functionaliteit te bieden.",
        ["name"] = "Versnellingsmeter",
        ["pitch"] = "Pitch",
        ["msg_calibrate"] = "Kalibreer de versnellingsmeter?",
        ["roll"] = "Roll"
      },
      ["rates"] = {
        ["help_table_5_p2"] = "Max Rate: Maximale rotatiesnelheid bij volledige stickuitslag in graden per seconde.",
        ["actual"] = "ACTUAL",
        ["max_rate"] = "Max Rate",
        ["help_table_4_p3"] = "Expo: Vermindert de gevoeligheid in het midden van de stick, waar nauwkeurige bediening nodig is.",
        ["rate"] = "Rate",
        ["help_table_5_p1"] = "RC Rate: Gebruik om de gevoeligheid rond de center stick te verminderen. RC Rate ingesteld op de helft van de Max Rate is lineair. Een lager getal zal de gevoeligheid rond de center stick verminderen. Hoger dan de helft van de Max Rate zal ook de Max Rate verhogen.",
        ["help_table_4_p2"] = "Max Rate: Maximale rotatiesnelheid bij volledige stickuitslag in graden per seconde.",
        ["center_sensitivity"] = "Cntr. Sens.",
        ["rc_curve"] = "RC Curve",
        ["roll"] = "Roll",
        ["none"] = "NONE",
        ["betaflight"] = "BETAFLIGHT",
        ["kiss"] = "KISS",
        ["help_table_1_p3"] = "Expo: Vermindert de gevoeligheid in het midden van de stick, waar nauwkeurige bediening nodig is.",
        ["help_table_3_p2"] = "Rate: Verhoogt de maximale rotatiesnelheid en vermindert de gevoeligheid tot ongeveer de helft van de stick.",
        ["help_table_2_p2"] = "Acro+: Verhoogt de maximale rotatiesnelheid terwijl de gevoeligheid met ongeveer de helft wordt verlaagd.",
        ["superrate"] = "SuperRate",
        ["help_table_2_p3"] = "Expo: Vermindert de gevoeligheid in het midden van de stick, waar nauwkeurige bediening nodig is.",
        ["raceflight"] = "RACEFLIGHT",
        ["yaw"] = "Yaw",
        ["collective"] = "Col",
        ["name"] = "Rates",
        ["help_table_5_p3"] = "Expo: Vermindert de gevoeligheid in het midden van de stick, waar nauwkeurige bediening nodig is.\n\n",
        ["help_table_3_p3"] = "RC Curve: Vermindert de gevoeligheid in het midden van de stick, waar nauwkeurige bediening nodig is.",
        ["expo"] = "Expo",
        ["help_table_1_p2"] = "SuperRate: Verhoogt de maximale rotatiesnelheid en vermindert de gevoeligheid tot ongeveer de helft van de stick.",
        ["help_default_p2"] = "We gebruiken de onderstaande subsleutels.",
        ["help_default_p1"] = "Default: We gebruiken dit zodat de knop voor de rates zichtbaar is.",
        ["quick"] = "QUICK",
        ["pitch"] = "Pitch",
        ["acroplus"] = "Acro+",
        ["help_table_1_p1"] = "RC Rate: Maximale rotatie rate met volledige stick input.",
        ["rc_rate"] = "RC Rate",
        ["help_table_2_p1"] = "Rate: Maximale rotatiesnelheid bij volledige stickuitslag in graden per seconde.",
        ["help_table_4_p1"] = "Center Sensitivity: Gebruik om de gevoeligheid rond de center stick te verminderen. Stel Center Sensitivity in op hetzelfde als Max Rate voor een lineaire respons. Een lager getal dan Max Rate zal de gevoeligheid rond de center stick verminderen. Let op dat hoger dan Max Rate de Max Rate zal verhogen - niet aanbevolen omdat het problemen veroorzaakt in het Blackbox-logboek.",
        ["help_table_0_p1"] = "Alle waarden zijn op nul gezet omdat er geen RATE TABEL in gebruik is.",
        ["help_table_3_p1"] = "RC Rate: Maximale rotatie bij volledige stick inut."
      },
      ["mixer"] = {
        ["help_p1"] = "Adust swash plate geometry, phase angles, and limits.",
        ["collective_tilt_correction_pos"] = "Positive",
        ["geo_correction"] = "Geo Correction",
        ["swash_tta_precomp"] = "TTA Precomp",
        ["name"] = "Mixer",
        ["collective_tilt_correction_neg"] = "Negative",
        ["tail_motor_idle"] = "Tail Idle Thr%",
        ["swash_phase"] = "Phase Angle",
        ["collective_tilt_correction"] = "Collective Tilt Correction",
        ["swash_pitch_limit"] = "Total Pitch Limit"
      },
      ["about"] = {
        ["help_p1"] = "Op deze pagina vindt u nuttige informatie die u mogelijk wordt gevraagd wanneer u ondersteuning nodig heeft",
        ["msgbox_credits"] = "Credits",
        ["ethos_version"] = "Ethos Versie",
        ["rf_version"] = "Rotorflight Versie",
        ["fc_version"] = "FC Versie",
        ["name"] = "Over",
        ["supported_versions"] = "Ondersteunde MSP Versies",
        ["license"] = "You may copy, distribute, and modify the software as long as you track changes/dates in source files. Any modifications to or software including (via compiler) GPL-licensed code must also be made available under the GPL along with build & install instructions.",
        ["simulation"] = "Simulatie",
        ["help_p2"] = "Voor ondersteuning, lees eerst de helppagina's op www.rotorflight.org\n\n",
        ["opener"] = "Rotorflight is een open source project. Bijdragen van andere mensen, die graag willen helpen deze software nog beter te maken, zijn welkom en worden aangemoedigd. Je hoeft geen hardcore programmeur te zijn om te helpen.",
        ["version"] = "Versie",
        ["msp_version"] = "MSP Versie",
        ["credits"] = "Bekende personen die aan de Rotorflight-firmware en deze software hebben bijgedragen zijn: Petri Mattila, Egon Lubbers, Rob Thomson, Rob Gayle, Phil Kaighin, Robert Burrow, Keith Williams, Bertrand Songis, Venbs Zhou... en nog veel meer mensen die urenlang hebben getest en feedback hebben gegeven!",
        ["msp_transport"] = "MSP Transport"
      },
      ["rates_advanced"] = {
        ["dyn_ceiling_gain"] = "DynPlafVerst",
        ["acc_limit"] = "VersnGrens",
        ["roll"] = "Roll",
        ["yaw_dynamics"] = "Yaw dynamics",
        ["pitch"] = "Nick",
        ["col"] = "Kol",
        ["setpoint_boost_cutoff"] = "SetpAfkap",
        ["yaw_dynamic_deadband_gain"] = "Dode band",
        ["rates_type"] = "Rates Type",
        ["setpoint_boost_gain"] = "SetpVersterk",
        ["msg_reset_to_defaults"] = "Rate type changed. Values will be reset to defaults.",
        ["yaw_dynamic_ceiling_gain"] = "Plafond",
        ["yaw_boost"] = "Yaw boost",
        ["gain"] = "Versterk.",
        ["rate_table"] = "Tarieftabel",
        ["dynamics"] = "Dynamics",
        ["yaw"] = "Gier",
        ["yaw_dynamic_deadband_filter"] = "Filter",
        ["name"] = "Rates",
        ["cutoff"] = "Drempel",
        ["help_rate_table"] = "Selecteer het tarief dat u wilt gebruiken. Opslaan zal de keuze toepassen op het actieve profiel.",
        ["help_p1"] = "Rates type: Choose the rate type you prefer flying with. Raceflight and Actual are the most straightforward.",
        ["pitch_boost"] = "Pitch boost",
        ["help_p2"] = "Dynamics: Applied regardless of rates type. Typically left on defaults but can be adjusted to smooth heli movements, like with scale helis.\n\n",
        ["accel_limit"] = "Accel",
        ["dyn_deadband_filter"] = "DynDodeFilter",
        ["roll_boost"] = "Rolboost",
        ["dyn_deadband_gain"] = "DynDodeVerst",
        ["collective_dynamics"] = "Collective dynamics",
        ["roll_dynamics"] = "Roll dynamics",
        ["collective_boost"] = "Collective boost",
        ["pitch_dynamics"] = "Pitch dynamics",
        ["response_time"] = "ReactieTijd"
      },
      ["servos"] = {
        ["tbl_yes"] = "YES",
        ["enable_servo_override"] = "Servo overschrijven inschakelen",
        ["disabling_servo_override"] = "Servo overschrijven uitschakelen...",
        ["help_tool_p3"] = "Minimum/Maximum: Pas de eindpunten van de geselecteerde servo aan.",
        ["tail"] = "TAIL",
        ["scale_negative"] = "Schaal negatief",
        ["help_tool_p1"] = "Override: [*] Schakel override in om realtime veranderingen van het servo-middenpunt toe te staan.",
        ["tbl_no"] = "NO",
        ["maximum"] = "Maximum",
        ["help_tool_p6"] = "Snelheid: De snelheid waarmee de servo beweegt. Wordt over het algemeen alleen gebruikt voor cyclische servo's om de swash gelijkmatig te laten bewegen. Optioneel - laat alles op 0 staan ​​als u het niet zeker weet.",
        ["help_fields_rate"] = "Servo PWM snelheid.",
        ["cyc_pitch"] = "CYC. PITCH",
        ["center"] = "Center",
        ["minimum"] = "Minimum",
        ["speed"] = "Snelheid",
        ["help_fields_speed"] = "Servo bewegingssnelheid in milliseconds.",
        ["disable_servo_override"] = "Servo overschrijven uitschakelen",
        ["help_fields_scale_pos"] = "Servo positief schalen.",
        ["saving_data"] = "Opslaan data...",
        ["cyc_left"] = "CYC. LEFT",
        ["saving"] = "Opslaan",
        ["name"] = "Servos",
        ["help_tool_p5"] = "Rotatiesnelheid: Op welke frequentie de servo het beste werkt, kunt u navragen bij de fabrikant.",
        ["help_tool_p2"] = "Center: Pas de middenpositie van de servo aan.",
        ["enabling_servo_override"] = "Servo overschrijven inschakelen...",
        ["servo_prefix"] = "SERVO ",
        ["reverse"] = "Omgekeerd",
        ["enable_servo_override_msg"] = "Servo overschrijven Hiermee kunt u het middelpunt van uw servo in realtime 'trimmen'.",
        ["cyc_right"] = "CYC. RIGHT",
        ["help_default_p2"] = "Primaire vluchtbesturingen die gebruikmaken van de rotorflight-mixer worden weergegeven in het gedeelte 'mixer'.",
        ["scale_positive"] = "Schaal positief",
        ["help_default_p1"] = "Selecteer de servo die u wilt configureren uit de onderstaande lijst.",
        ["servo_override"] = "Servo Overschrijven",
        ["disable_servo_override_msg"] = "Geef de besturing van de servo's terug aan de vliegcontroller.",
        ["help_fields_min"] = "Servo negatief limiet.",
        ["help_default_p3"] = "Alle andere servo's die niet door de primaire vluchtmixer worden aangestuurd, worden weergegeven in het gedeelte 'Andere servos'.",
        ["help_fields_mid"] = "Pulsbreedte van de servomiddenpositie.",
        ["help_fields_scale_neg"] = "Servo negatief schalen.",
        ["rate"] = "PWM snelheid",
        ["help_tool_p4"] = "Schaal: Pas de mate aan waarin de servo beweegt voor een bepaalde invoer.",
        ["help_fields_flags"] = "0 = Default, 1=Reverse, 2 = Geo Correction, 3 = Reverse + Geo Correction\n\n",
        ["geometry"] = "Geometrie",
        ["help_fields_max"] = "Servo positief limiet."
      },
      ["profile_autolevel"] = {
        ["acro_trainer"] = "Acro trainer",
        ["angle_mode"] = "Angle mode",
        ["max"] = "Max",
        ["name"] = "Autolevel",
        ["help_p1"] = "Acro Trainer: Hoe agressief de heli terugkantelt naar een horizontale positie tijdens het vliegen in de Acro Trainer-modus.",
        ["horizon_mode"] = "Horizon mode",
        ["gain"] = "Gain",
        ["help_p2"] = "Angle Mode: Hoe agressief de heli terug kantelt naar een horizontaal wanneer  in de angled mode gevlogen word.",
        ["help_p3"] = "Horizon Mode: Hoe agressief de heli terug kantelt naar horizontaal tijdens het vliegen in de Horizon modus.\n\n"
      },
      ["filters"] = {
        ["filter_type"] = "Filter type",
        ["help_p4"] = "Dynamic Notch Filters: Maakt automatisch notchfilters aan binnen het minimale en maximale frequentiebereik.\n\n",
        ["notch_c"] = "Notch aantal",
        ["rpm_preset"] = "Type",
        ["lowpass_1"] = "Lowpass 1",
        ["rpm_min_hz"] = "Min. Frequentie",
        ["help_p2"] = "Gyro lowpass: Lowpassfilters voor het gyrosignaal. Meestal op standaard gelaten.",
        ["cutoff"] = "Cutoff",
        ["notch_1"] = "Notch 1",
        ["max_cutoff"] = "Max cutoff",
        ["help_p3"] = "Gyro notch filters: Gebruik voor het filteren van specifieke frequentiebereiken. Meestal niet nodig in de meeste heli's.",
        ["lowpass_2"] = "Lowpass 2",
        ["rpm_filter"] = "RPM filter",
        ["help_p1"] = "Normaal gesproken zou u deze pagina niet bewerken zonder eerst uw Blackbox log te controleren!",
        ["dyn_notch"] = "Dynamische Filters",
        ["notch_q"] = "Notch Q",
        ["lowpass_1_dyn"] = "Lowpass 1 dyn.",
        ["notch_min_hz"] = "Min",
        ["notch_max_hz"] = "Max",
        ["notch_2"] = "Notch 2",
        ["name"] = "Filters",
        ["min_cutoff"] = "Min cutoff",
        ["center"] = "Center"
      },
      ["status"] = {
        ["arming_disable_flag_3"] = "Bad RX Recovery",
        ["arming_disable_flag_20"] = "RPM Filter",
        ["arming_disable_flag_11"] = "Belasting",
        ["arming_disable_flag_22"] = "DSHOT Bitbang",
        ["dataflash_free_space"] = "Dataflash vrije ruimte",
        ["arming_disable_flag_25"] = "Arm Schakelaar",
        ["erasing"] = "Wissen",
        ["arming_disable_flag_9"] = "Boot Grace Time",
        ["megabyte"] = "MB",
        ["arming_disable_flag_17"] = "Paralyze",
        ["arming_disable_flag_5"] = "Governor",
        ["arming_disable_flag_8"] = "Angle",
        ["arming_disable_flag_1"] = "Fail Safe",
        ["cpu_load"] = "CPU belasting",
        ["arming_disable_flag_15"] = "BST",
        ["arming_disable_flag_12"] = "Calibreren",
        ["arming_disable_flag_19"] = "Resc",
        ["arming_disable_flag_4"] = "Box Fail Safe",
        ["arming_disable_flag_24"] = "Motor Protocol",
        ["real_time_load"] = "Real-time belasting",
        ["help_p2"] = "Om de dataflash te wissen voor meer opslag van logbestanden, drukt u op de knop in het menu die wordt aangegeven met een '*'.\n\n",
        ["arming_disable_flag_2"] = "RX Fail Safe",
        ["ok"] = "OK",
        ["arming_disable_flag_0"] = "No Gyro",
        ["arming_disable_flag_18"] = "GPS",
        ["help_p1"] = "Gebruik deze pagina om uw huidige flight controller status te bekijken. Dit kan handig zijn om te bepalen waarom heli niet wilt armen",
        ["arming_flags"] = "Arming Flags",
        ["unsupported"] = "Niet ondersteund",
        ["erase_prompt"] = "Wilt u de dataflash wissen?",
        ["erase"] = "Wissen",
        ["arming_disable_flag_10"] = "No Pre Arm",
        ["arming_disable_flag_21"] = "Herstart vereist",
        ["name"] = "Status",
        ["arming_disable_flag_13"] = "CLI",
        ["arming_disable_flag_14"] = "CMS Menu",
        ["arming_disable_flag_16"] = "MSP",
        ["arming_disable_flag_7"] = "Throttle",
        ["erasing_dataflash"] = "Wissen dataflash...",
        ["arming_disable_flag_23"] = "Acc Calibratie"
      },
      ["profile_pidbandwidth"] = {
        ["help_p1"] = "PID Bandwidth: Totale bandbreedte in Hz die door de PID-loop wordt gebruikt.",
        ["pitch"] = "P",
        ["yaw"] = "Y",
        ["name"] = "PID Bandbreedte",
        ["bterm_cutoff"] = "B-term cut-off",
        ["help_p3"] = "B-term cutoff: B-term afsnijdingsfrequentie in HZ.\n\n",
        ["dterm_cutoff"] = "D-term cut-off",
        ["help_p2"] = "D-term cutoff: D-term afsnijdingsfrequentie in HZ.",
        ["roll"] = "R"
      }
    },
    ["navigation_save"] = "Save",
    ["menu_section_flight_tuning"] = "Vlucht Tuning",
    ["error_timed_out"] = "Error: timed out",
    ["check_rf_module_on"] = "Controleer of de rf module aanstaat.",
    ["msg_saving"] = "Opslaan...",
    ["msg_save_not_commited"] = "Opslaan niet gelukt naar EEPROM",
    ["menu_section_advanced"] = "Geavanceerd",
    ["msg_loading_from_fbl"] = "Laden data van vliegcontroller...",
    ["msg_reload_settings"] = "Herlaad data van vliegcontroller?",
    ["menu_section_tools"] = "Tools",
    ["msg_connecting"] = "Verbinden",
    ["msg_save_current_page"] = "Sla huidige pagina op in de vliegcontroller?",
    ["btn_ok_long"] = "                OK                ",
    ["check_discovered_sensors"] = "Controleer of je alle sensors discovered hebt.",
    ["msg_loading"] = "Laden...",
    ["check_heli_on"] = "Controleer of de heli aanstaat en de radio is verbonden.",
    ["check_bg_task"] = "Schakel de achtergrond taak in.",
    ["navigation_tools"] = "*",
    ["check_supported_version"] = "Deze versie van het lua script \n kan niet gebruikt worden met het geselecteerde model"
  },
  ["telemetry"] = {
    ["sensors"] = {
      ["attpitch"] = "P.hoek",
      ["attroll"] = "R.hoek",
      ["attyaw"] = "Y.hoek",
      ["accx"] = "Accel X",
      ["accy"] = "Accel Z",
      ["accz"] = "Accel Z",
      ["groundspeed"] = "Grond snelheid",
      ["esc_temp"] = "ESC Temperatuur",
      ["rate_profile"] = "Rate Profiel",
      ["headspeed"] = "Rotortoerental",
      ["altitude"] = "Hoogte",
      ["voltage"] = "Voltage",
      ["bec_voltage"] = "Bec voltage",
      ["cell_count"] = "Aantal cellen",
      ["governor"] = "Governor Status",
      ["adj_func"] = "Adj (Functies)",
      ["fuel"] = "Laad niveau",
      ["smartfuel"] = "Smart Fuel",
      ["rssi"] = "RSSI",
      ["link"] = "Linkkwaliteit",
      ["adj_val"] = "Adj (Waarde)",
      ["arming_flags"] = "Arming Flags",
      ["current"] = "Stroom",
      ["throttle_pct"] = "Throttle %",
      ["consumption"] = "Verbruik",
      ["smartconsumption"] = "Slim verbruik",
      ["pid_profile"] = "PID Profiel",
      ["mcu_temp"] = "MCU Temperatuur",
      ["armdisableflags"] = "Arming Disable"
    }
  },
  ["widgets"] = {
    ["bbl"] = {
      ["erase_dataflash"] = "Gegevensflash wissen",
      ["erasing"] = "Bezig met wissen...",
      ["display"] = "Weergave",
      ["display_free"] = "Vrij",
      ["display_used"] = "Gebruikt",
      ["display_outof"] = "Gebruikt/Totaal"
    },
    ["craftimage"] = {
    },
    ["craftname"] = {
      ["txt_enter_craft_name"] = "Vul Model naam in",
      ["title"] = "Model naam",
      ["txt_cancel"] = "Cancel",
      ["txt_save"] = "Opslaan"
    },
    ["dashboard"] = {
      ["theme_load_error"] = "Je Thema werd niet juist geladen. Standaard thema wordt gebruikt.",
      ["validate_sensors"] = "CONTROLEER SENSORS",
      ["unsupported_resolution"] = "TE KLEIN",
      ["loading"] = "ROTORFLIGHT",
      ["waiting_for_connection"] = "VERBINDEN",
      ["check_bg_task"] = "BG TASK",
      ["check_rf_module_on"] = "RF MODULE",
      ["check_discovered_sensors"] = "SENSOREN",
      ["no_link"] = "Geen link",
      ["reset_flight"] = "Reset flight",
      ["reset_flight_ask_title"] = "Reset flight",
      ["reset_flight_ask_text"] = "Weet u zeker dat je flight wilt resetten?",
      ["voltage"] = "Voltage",
      ["fuel"] = "Brandstof",
      ["headspeed"] = "Rotortoerental",
      ["max"] = "Max",
      ["min"] = "Min",
      ["bec_voltage"] = "BEC Voltage",
      ["esc_temp"] = "ESC Temp",
      ["flight_duration"] = "Flight Duur",
      ["total_flight_duration"] = "Totaal model flight duur",
      ["rpm_min"] = "RPM Min",
      ["rpm_max"] = "RPM Max",
      ["throttle_max"] = "Throttle Max",
      ["current_max"] = "Huidig Max",
      ["esc_max_temp"] = "ESC Temp Max",
      ["watts_max"] = "Max Watts",
      ["consumed_mah"] = "Verbruikt mAh",
      ["fuel_remaining"] = "Brandstof over",
      ["min_volts_cell"] = "Min volts per cel",
      ["link_min"] = "Link min",
      ["governor"] = "Governor",
      ["profile"] = "Profiel",
      ["rates"] = "Rates",
      ["flights"] = "Flights",
      ["lq"] = "LQ",
      ["time"] = "Tijd",
      ["blackbox"] = "Blackbox",
      ["throttle"] = "Throttle",
      ["flight_time"] = "Flight Tijd",
      ["rssi_min"] = "RSSI min",
      ["current"] = "Stroom",
      ["timer"] = "Timer",
      ["rpm"] = "RPM",
      ["min_voltage"] = "Min voltage",
      ["max_voltage"] = "Max voltage",
      ["min_current"] = "Min stroom",
      ["max_current"] = "Max stroom",
      ["max_tmcu"] = "Max T.MCU",
      ["max_emcu"] = "Max E.MCU",
      ["altitude"] = "Hoogte",
      ["altitude_max"] = "Hoogte max",
      ["power"] = "Vermogen",
      ["cell_voltage"] = "Cel voltage",
      ["volts_per_cell"] = "Voltage per cel",
      ["warning"] = "Waarschuwing",
      ["tx_batt"] = "TX Battery",
      ["link_max"] = "Link Max"
    },
    ["governor"] = {
      ["UNKNOWN"] = "ONBEKEND",
      ["IDLE"] = "IDLE",
      ["DISARMED"] = "DISARMED",
      ["OFF"] = "UIT",
      ["SPOOLUP"] = "OPSTARTEN",
      ["ACTIVE"] = "ACTIEF",
      ["RECOVERY"] = "HERSTELLEN",
      ["THROFF"] = "THR-UIT",
      ["LOSTHS"] = "LOST-HS",
      ["AUTOROT"] = "AUTOROT",
      ["DISABLED"] = "UITGESCHAKELD",
      ["BAILOUT"] = "BAILOUT"
    }
  }
}
