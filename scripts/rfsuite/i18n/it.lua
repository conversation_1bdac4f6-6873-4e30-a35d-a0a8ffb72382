--[[
 * Copyright (C) Rotorflight Project
 *
 * License GPLv3: https://www.gnu.org/licenses/gpl-3.0.en.html
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * Please do not edit these files directly. These files are generated
 * from JSON files contained within the folder:
 *
 *   bin/i18n/json/*.json
 *
 * Running the script:
 *
 *   lua54 json-to-lua.lua
 *
 * will create these files for you.
]]

return {
  ["reload"] = "ricarica",
  ["image"] = "immagine",
  ["error"] = "errore",
  ["save"] = "salva",
  ["ethos"] = "ethos",
  ["version"] = "versione",
  ["bg_task_disabled"] = "bg task disabilitato",
  ["no_link"] = "no link",
  ["background_task_disabled"] = "background task disabilitato",
  ["no_sensor"] = "nessun sensore",
  ["api"] = {
    ["RC_TUNING"] = {
      ["setpoint_boost_cutoff_2"] = "Boost taglio setpoint.",
      ["response_time_3"] = "Aumenta o diminuisce il tempo di risposta dei rate per addolcire movimenti heli.",
      ["accel_limit_4"] = "Accelerazione Massima del velivolo in risposta al movimento stick.",
      ["setpoint_boost_gain_4"] = "Boost gain setpoint.",
      ["yaw_dynamic_deadband_filter"] = "Filtro massimo applicato alla deadband dinamica dello yaw.",
      ["setpoint_boost_gain_3"] = "Boost guadagno setpoint.",
      ["response_time_2"] = "Aumenta o diminuisce il tempo di risposta dei rate per addolcire movimenti heli.",
      ["accel_limit_1"] = "Accelerazione Massima del velivolo in risposta al movimento stick.",
      ["setpoint_boost_cutoff_1"] = "Boost taglio setpoint.",
      ["setpoint_boost_cutoff_4"] = "Boost taglio setpoint.",
      ["setpoint_boost_gain_2"] = "Boost guadagno setpoint.",
      ["accel_limit_2"] = "Accelerazione Massima del velivolo in risposta al movimento stick.",
      ["yaw_dynamic_deadband_gain"] = "Massimo guadagno applicato alla deadband dinamica dello yaw.",
      ["accel_limit_3"] = "Accelerazione Massima del velivolo in risposta al movimento stick.",
      ["response_time_4"] = "Aumenta o diminuisce il tempo di risposta dei rate per addolcire movimenti heli.",
      ["setpoint_boost_cutoff_3"] = "Boost taglio setpoint.",
      ["setpoint_boost_gain_1"] = "Boost guadagno setpoint.",
      ["yaw_dynamic_ceiling_gain"] = "Guadagno massimo applicato alla soglia dinamica dello yaw.",
      ["response_time_1"] = "Aumenta o diminuisce il tempo di risposta dei rate per addolcire movimenti heli."
    },
    ["RESCUE_PROFILE"] = {
      ["rescue_flip_gain"] = "Determina aggresivita' del flip del heli nel salvataggio rovescio.",
      ["rescue_level_gain"] = "Determina aggressivita' del livellamento nel salvataggio.",
      ["tbl_off"] = "OFF",
      ["rescue_hover_collective"] = "Valore Collectivo per hover.",
      ["rescue_max_setpoint_rate"] = "Limite rateo salvataggio roll/pitch. Elicotteri grandi usano ratei piu lenti di rotazione.",
      ["tbl_flip"] = "FLIP",
      ["rescue_flip_mode"] = "Se Salvataggio viene attivato in volo rovescio, flip a volo dritto - o rimane invertito.",
      ["rescue_pull_up_time"] = "Quando salvataggio attivato, l'elicottero applica un pull-up collettivo per questo periodo di tempo prima di eseguire flip o fase di ascesa.",
      ["tbl_noflip"] = "NO FLIP",
      ["rescue_exit_time"] = "Questi limiti di applicazione di collettivo negativo se l'elicottero ha rollato durante salvataggio.",
      ["rescue_pull_up_collective"] = "Valore Collettivo di pull-up salita.",
      ["rescue_max_setpoint_accel"] = "Limite di quanto veloce l'elicottero accelera in un roll/pitch. Elicotteri grandi necessitano di accelarioni lente.",
      ["tbl_on"] = "ON",
      ["rescue_climb_collective"] = "Valore Collettivo per l'ascesa di salvataggio.",
      ["rescue_flip_time"] = "Se l'hely e' in modo salvataggio e sta provando un flip dritto e non esegue in questo tempo, il salvataggio viene annullato.",
      ["rescue_climb_time"] = "Durata del tempo di ascesa collettivo prima di passare ad hover."
    },
    ["ESC_SENSOR_CONFIG"] = {
      ["hw4_current_offset"] = "Hobbywing v4 taratura offset corrente",
      ["tbl_off"] = "Off",
      ["update_hz"] = "Telemetria ESC rateo aggiornamento",
      ["half_duplex"] = "Modo Half duplex per telemetria ESC",
      ["consumption_correction"] = "Tara la correzione consumo",
      ["current_offset"] = "Tara offset sensore Corrente",
      ["voltage_correction"] = "Tara correzione Voltaggio",
      ["hw4_voltage_gain"] = "Hobbywing v4 tara guadagno voltaggio",
      ["tbl_on"] = "On",
      ["hw4_current_gain"] = "Hobbywing v4 tara guadagno corrente",
      ["current_correction"] = "Tara correzione Corrente",
      ["pin_swap"] = "Scambia i pin della TX e RX per telemetria ESC"
    },
    ["ESC_PARAMETERS_FLYROTOR"] = {
      ["tbl_alwayson"] = "Attivo",
      ["throttle_min"] = "Valore Minimo gas",
      ["tbl_disabled"] = "Disabilitato",
      ["tbl_auto"] = "Automatique",
      ["starting_torque"] = "Coppia iniziale per il motore",
      ["cell_count"] = "Numero di celle nella batteria",
      ["motor_erpm_max"] = "RPM Massimi",
      ["throttle_max"] = "Valore Massimo Gas",
      ["tbl_ccw"] = "Antiorario",
      ["tbl_escgov"] = "Esc Governor",
      ["temperature_protection"] = "Temperatura alla quale taglio potenza al 50%",
      ["tbl_automatic"] = "Automatico",
      ["low_voltage_protection"] = "Voltaggio al quale si taglia potenza al 50%",
      ["tbl_cw"] = "Orario",
      ["soft_start"] = "Valore Soft start",
      ["gov_i"] = "Valore Integrale per il governor",
      ["timing_angle"] = "Angolo Timing per il motore",
      ["response_speed"] = "Velocita' di Risposta per il motore",
      ["current_gain"] = "Valore Guadagno per il sensore corrente",
      ["tbl_extgov"] = "Governor Esterno",
      ["buzzer_volume"] = "Volume Buzzer",
      ["gov_d"] = "Valore Derivativo per il governor",
      ["tbl_enabled"] = "Abilitato",
      ["gov_p"] = "Valore Proporzionale per il governor"
    },
    ["ESC_PARAMETERS_YGE"] = {
      ["tbl_alwayson"] = "Attivo",
      ["tbl_off"] = "Off",
      ["tbl_modestore"] = "Heli Governor Store",
      ["tbl_modefree"] = "Libero (Attenzione!)",
      ["tbl_modeglider"] = "Aero Glider",
      ["tbl_modeext"] = "Governor Est. Heli",
      ["tbl_modeheli"] = "Governor Heli",
      ["tbl_medium"] = "Medio",
      ["tbl_autonorm"] = "Auto Normale",
      ["tbl_reverse"] = "Inverti",
      ["tbl_modef3a"] = "Aero F3A",
      ["tbl_auto"] = "Auto",
      ["tbl_slowdown"] = "Rallenta",
      ["tbl_slow"] = "Lento",
      ["tbl_modeair"] = "Motore Aero",
      ["tbl_normal"] = "Normale",
      ["tbl_on"] = "On",
      ["tbl_autoextreme"] = "Auto Estremo",
      ["tbl_autoefficient"] = "Auto Efficiente",
      ["tbl_smooth"] = "Morbido",
      ["tbl_fast"] = "Veloce",
      ["tbl_custom"] = "Custom (Definito da PC)",
      ["tbl_cutoff"] = "Cutoff",
      ["tbl_autopower"] = "Auto Power",
      ["tbl_unused"] = "*non Usato*"
    },
    ["GOVERNOR_PROFILE"] = {
      ["governor_tta_gain"] = "Guadagno TTA applicato per aumentare la velocita' del rotore per controllare la coda nelle direzioni negative (es: coda piu' lenta del minimo).",
      ["governor_collective_ff_weight"] = "Valore Precompensazione Collettivo - quanto collettivo e' mixato nel feedforward.",
      ["governor_i_gain"] = "Guadagno PID loop I-term gain.",
      ["governor_cyclic_ff_weight"] = "Valore Precompensazione Ciclico - quanto ciclico e' mixato nel feedforward.",
      ["governor_f_gain"] = "Guadagno Feedforward.",
      ["governor_gain"] = "Guadagno Master PID loop.",
      ["governor_headspeed"] = "Velocita' Target Rotore per il profilo corrente.",
      ["governor_min_throttle"] = "Gas Minimo utilizzabile dal governor.",
      ["governor_d_gain"] = "Guadagno PID loop D-term.",
      ["governor_p_gain"] = "Guadagno PID loop P-term.",
      ["governor_yaw_ff_weight"] = "Valore Precompensazione Yaw - Quanto yaw e' mixato nel feedforward.",
      ["governor_max_throttle"] = "Gas Massimo utilizzabile dal governor.",
      ["governor_tta_limit"] = "Aumento Velocita' TTA max oltre la velocita massima rotore."
    },
    ["PID_PROFILE"] = {
      ["bterm_cutoff_2"] = "B-term cutoff in Hz.",
      ["dterm_cutoff_1"] = "D-term cutoff in Hz.",
      ["bterm_cutoff_1"] = "B-term cutoff in Hz.",
      ["gyro_cutoff_1"] = "PID loop overall bandwidth in Hz.",
      ["tbl_on"] = "ON",
      ["dterm_cutoff_2"] = "D-term cutoff in Hz.",
      ["yaw_inertia_precomp_cutoff"] = "Cutoff. Frequenza Cutoff Derivativa in passi da 1/10Hz. Controlla quanto preciso e' il precomp. Valori Alti maggior precisione.",
      ["offset_limit_0"] = "Limite massimo per l'angolo di offset integrale ad alta velocita' nel loop PID. O-term non superera' mai questi limiti.",
      ["cyclic_cross_coupling_ratio"] = "Quantita' di compensazione del roll-to-pitch necessaria, vs. pitch-to-roll.",
      ["yaw_precomp_cutoff"] = "Limite di Frequenza di tutte le compensazioni di Yaw.",
      ["error_limit_0"] = "Limite massimo per l'errore angolare nel loop PID. L'errore assoluto e quindi l' I-term non supereranno mai questi limiti.",
      ["trainer_gain"] = "Determina l'aggressivita' con cui l'elicottero si inclina all'indietro fino all'angolo massimo (se superato) mentre e' in modalita' Acro Trainer.",
      ["tbl_rpy"] = "RPY",
      ["gyro_cutoff_2"] = "Larghezza di banda complessiva del loop PID in Hz.",
      ["yaw_ccw_stop_gain"] = "Guadagno di Stop (PD) per rotazione antioraria.",
      ["trainer_angle_limit"] = "Limitare l'angolo massimo di beccheggio/rollio dell'elicottero in modalita' Acro Trainer.",
      ["tbl_rp"] = "RP",
      ["yaw_cyclic_ff_gain"] = "Feedforward ciclico mixato nello yaw (cyclic-to-yaw precomp).",
      ["error_decay_time_cyclic"] = "Costante di tempo per lo spurgo dell' I-term ciclico. Piu' alto stabilizzera' il volo stazionario, piu' basso lo fara' andare alla deriva.",
      ["error_decay_limit_cyclic"] = "Velocita' massima di spurgo per I-term ciclico.",
      ["cyclic_cross_coupling_gain"] = "Quantita' di compensazione applicata per il disaccoppiamento passo-rollio.",
      ["yaw_collective_dynamic_decay"] = "Tempo di decadimento per il precomp di imbardata extra sull'input collettivo.",
      ["pitch_collective_ff_gain"] = "L'aumento compensera' il movimento di beccheggio causato dal trascinamento della coda durante la salita.",
      ["iterm_relax_type"] = "Scegli gli assi in cui e' attivo. RP: Roll, Pitch. RPY: Roll, Pitch, Yaw.",
      ["offset_limit_1"] = "Limite massimo per l'angolo di offset integrale ad alta velocita' nel ciclo PID. L' O-term non superera' mai questi limiti.",
      ["iterm_relax_cutoff_1"] = "Aiuta a ridurre il rimbalzo dopo movimenti rapidi del joystick. Se troppo basso, puo' causare incoerenza nei piccoli movimenti del joystick.",
      ["error_limit_1"] = "Limite massimo per l'errore angolare nel circuito PID. L'errore assoluto e quindi l' I-term I non superera' mai questi limiti.",
      ["horizon_level_strength"] = "Determina l'aggressivita' con cui l'elicottero si inclina all'indietro per tornare in piano mentre e' in modalita' Horizon.",
      ["error_limit_2"] = "Limite massimo per l'errore angolare nel circuito PID. L'errore assoluto e quindi l' I-term I non superera' mai questi limiti.",
      ["iterm_relax_cutoff_2"] = "Aiuta a ridurre il rimbalzo dopo movimenti rapidi del joystick. Se troppo basso, puo' causare incoerenza nei piccoli movimenti del joystick.",
      ["tbl_off"] = "OFF",
      ["yaw_collective_ff_gain"] = "Feedforward Collettivo mixato in yaw (collective-to-yaw precomp).",
      ["gyro_cutoff_0"] = "Loop PID larghezza di banda complessiva in Hz.”",
      ["yaw_collective_dynamic_gain"] = "Un impulso extra di precomando imbardata sull'ingresso collettivo.",
      ["cyclic_cross_coupling_cutoff"] = "Limite di frequenza per la compensazione. Un valore piu' alto rendera' piu' rapida l'azione di compensazione.",
      ["error_rotation"] = "Ruota i termini di errore di rollio e beccheggio attuali attorno all'imbardata quando l'aeromobile ruota. Questo e' talvolta chiamato Compensazione Piro.",
      ["angle_level_limit"] = "Limita l'angolo massimo di beccheggio/rollio dell'elicottero mentre e' in modalita' Angle.",
      ["yaw_cw_stop_gain"] = "Guadagno Stop (PD) per rotazioni orarie.",
      ["iterm_relax_cutoff_0"] = "Aiuta a ridurre il rimbalzo dopo movimenti rapidi del joystick. Se troppo basso, puo' causare incoerenza nei piccoli movimenti del joystick.",
      ["yaw_inertia_precomp_gain"] = "Guadagno scalare. La forza dell'inerzia del rotore principale. Un valore piu' alto significa che viene applicato piu' precomp al controllo di imbardata.",
      ["dterm_cutoff_0"] = "D-term cutoff in Hz.",
      ["angle_level_strength"] = "Determina l'aggressivita' con cui l'elicottero si inclina all'indietro per tornare in piano mentre e' in modalita' angle.",
      ["bterm_cutoff_0"] = "B-term cutoff in Hz.",
      ["error_decay_time_ground"] = "Scarica l'errore del controller corrente quando l'aeromobile non e' in volo per evitare che si ribalti."
    },
    ["MIXER_CONFIG"] = {
      ["swash_trim_1"] = "Regolazione del piatto oscillante per livellare il piatto quando si utilizzano collegamenti fissi.",
      ["tail_motor_idle"] = "Segnale minimo di accelerazione inviato al motore di coda. Questo dovrebbe essere impostato appena abbastanza alto da non far fermare il motore.",
      ["tail_center_trim"] = "Imposta il trim del rotore di coda per yaw 0 per passo variabile, o l'acceleratore del motore di coda per Yaw 0 per motorizzato.",
      ["tbl_cw"] = "Antiorario",
      ["swash_tta_precomp"] = "Mixer precomp per Yaw 0.",
      ["swash_trim_2"] = "Regolazione del piatto oscillante per livellare il piatto quando si utilizzano collegamenti fissi.",
      ["swash_geo_correction"] = "Regolare se c'e' troppo negativo o troppo positivo nel collettivo.",
      ["swash_trim_0"] = "Regolazione del piatto oscillante per livellare il piatto quando si utilizzano collegamenti fissi.",
      ["swash_phase"] = "Offset di Fase per controlli piatto oscillante.",
      ["collective_tilt_correction_pos"] = "Regolare la scala di correzione dell'inclinazione collettiva per il passo collettivo positivo.",
      ["collective_tilt_correction_neg"] = "Regolare la scala di correzione dell'inclinazione collettiva per il passo collettivo negativo.",
      ["tbl_ccw"] = "antiorario",
      ["swash_pitch_limit"] = "Massima quantita' di passo ciclico e collettivo combinato delle pale."
    },
    ["MOTOR_CONFIG"] = {
      ["minthrottle"] = "Questo valore PWM viene inviato all'ESC/servo a bassa accelerazione",
      ["motor_pwm_protocol"] = "Il protocollo utilizzato per comunicare con l'ESC",
      ["main_rotor_gear_ratio_0"] = "Numero Denti Pinione Motore",
      ["maxthrottle"] = "Questo valore PWM viene inviato all'ESC/servo a massima accelerazione",
      ["mincommand"] = "Questo valore PWM viene inviato quando il motore e' fermo",
      ["main_rotor_gear_ratio_1"] = "Numero denti ruotacorona primaria",
      ["tail_rotor_gear_ratio_1"] = "Numero Denti corona autorotazione",
      ["motor_pwm_rate"] = "La frequenza con cui l'ESC invia segnali PWM al motore",
      ["tail_rotor_gear_ratio_0"] = "Numero denti rotore di coda",
      ["motor_pole_count_0"] = "Il numero di magneti nella campana motore."
    },
    ["ESC_PARAMETERS_HW5"] = {
      ["tbl_cw"] = "Orario",
      ["tbl_fixedwing"] = "Ala Fissa",
      ["tbl_disabled"] = "Disabilita",
      ["tbl_ccw"] = "antiorario",
      ["tbl_heligov"] = "Heli Governor",
      ["tbl_softcutoff"] = "Soft Cutoff",
      ["tbl_proportional"] = "Proporzionale",
      ["tbl_heliext"] = "Heli Ext Governor",
      ["tbl_helistore"] = "Heli Governor Store",
      ["tbl_hardcutoff"] = "Hard Cutoff",
      ["tbl_normal"] = "Normal",
      ["tbl_autocalculate"] = "Auto Calcola",
      ["tbl_enabled"] = "Abilita",
      ["tbl_reverse"] = "Inverti"
    },
    ["PILOT_CONFIG"] = {
      ["model_param1_value"] = "Reglez cette valeur sur la duree de vol prevue, en secondes. La radiocommande emettra un bip lorsque cette duree sera atteinte."
    },
    ["BATTERY_INI"] = {
      ["calcfuel_local"] = "Calculate Fuel Using",
      ["tbl_off"] = "Current Sensor",
      ["tbl_on"] = "Voltage Sensor",
      ["sag_multiplier"] = "Raise or lower to adjust for the amount of voltage sag you see in flight.",
      ["kalman_multiplier"] = "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.",
      ["alert_off"] = "Off",
      ["alert_bec"] = "BEC",
      ["alert_rxbatt"] = "Rx Batt"
    },
    ["BATTERY_CONFIG"] = {
      ["vbatmincellvoltage"] = "La tensione minima per cella prima che scatti l'allarme di bassa tensione.",
      ["vbatmaxcellvoltage"] = "La tensione massima per cella prima che scatti l'allarme di alta tensione.",
      ["vbatwarningcellvoltage"] = "La tensione per cella alla quale l'allarme di bassa tensione iniziera' a suonare.",
      ["batteryCellCount"] = "Il numero di celle della batteria.",
      ["vbatfullcellvoltage"] = "La tensione nominale di una cella completamente carica.",
      ["batteryCapacity"] = "La capacita' in milliampere ora della batteria."
    },
    ["ACC_TRIM"] = {
      ["pitch"] = "Utilizzare per regolare se l'elicottero si sposta in una delle modalita' stabilizzate (angle, horizon, etc.).",
      ["roll"] = "Utilizzare per regolare se l'elicottero si sposta in una delle modalita' stabilizzate (angle, horizon, etc.)."
    },
    ["PID_TUNING"] = {
      ["pid_1_I"] = "Quanto il sistema mantiene la sua posizione.",
      ["pid_2_P"] = "Quanto strettamente il sistema segue il setpoint desiderato.",
      ["pid_2_I"] = "Quanto il sistema mantiene la sua posizione.",
      ["pid_1_O"] = "Utilizzato per evitare che il velivolo beccheggi quando si usa il collettivo alto.",
      ["pid_1_F"] = "Aiuta a spingere il P-term in base all'input del joystick. Aumentando si rende la risposta piu' netta, ma puo' causare un overshoot.",
      ["pid_0_D"] = "Forza di smorzamento di qualsiasi movimento sul sistema, incluse le influenze esterne. Riduce anche l'overshoot.",
      ["pid_1_P"] = "Quanto strettamente il sistema segue il setpoint desiderato.",
      ["pid_0_I"] = "Quanto il sistema mantiene la sua posizione.",
      ["pid_2_B"] = "Aumento dell'anticipo per far reagire maggiormente l'elicottero ai rapidi movimenti del joystick.",
      ["pid_0_O"] = "Utilizzato per evitare che il velivolo si rovesci quando si usa il collettivo alto.",
      ["pid_0_F"] = "Aiuta a spingere il P-term in base all'input del joystick. Aumentando si rende la risposta piu' netta, ma puo' causare un overshoot.",
      ["pid_2_F"] = "Aiuta a spingere il P-term in base all'input del joystick. Aumentando si rende la risposta piu' netta, ma puo' causare un overshoot.",
      ["pid_2_D"] = "Forza di smorzamento di qualsiasi movimento sul sistema, incluse le influenze esterne. Riduce anche l'overshoot.",
      ["pid_0_P"] = "Quanto strettamente il sistema segue il setpoint desiderato.",
      ["pid_1_D"] = "Forza di smorzamento di qualsiasi movimento sul sistema, incluse le influenze esterne. Riduce anche l'overshoot.",
      ["pid_0_B"] = "Aumento dell'anticipo per far reagire maggiormente l'elicottero ai rapidi movimenti del joystick.",
      ["pid_1_B"] = "Aumento dell'anticipo per far reagire maggiormente l'elicottero ai rapidi movimenti del joystick."
    },
    ["GOVERNOR_CONFIG"] = {
      ["tbl_govmode_standard"] = "STANDARD",
      ["tbl_govmode_mode2"] = "MODE2",
      ["gov_tracking_time"] = "Costante di tempo per le variazioni di velocita' della Rotore, in secondi, che misura il tempo da zero alla massima velocita' della Rotore.",
      ["tbl_govmode_passthrough"] = "PASSTHROUGH",
      ["tbl_govmode_mode1"] = "MODE1",
      ["gov_recovery_time"] = "Costante di tempo per il recupero della velocita' di rotazione, in secondi, misurando il tempo da zero alla massima velocita'.",
      ["gov_startup_time"] = "Costante di tempo per l'avvio lento, in secondi, che misura il tempo da zero alla massima velocita' della Rotore.",
      ["gov_handover_throttle"] = "Il governor si attiva al di sopra di %. Al di sotto di questa, il gas viene passato all'ESC.",
      ["gov_spoolup_time"] = "Costante di tempo per avviamento lento, in secondi, che misura il tempo da zero alla massima velocita' di rotazione.",
      ["gov_spoolup_min_throttle"] = "Acceleratore minimo per un avvio lento, in percentuale. Per i motori elettrici il valore e' 5%, per i nitro andrebbe impostato in modo che la frizione innesti gradualmente al 10-15%.",
      ["tbl_govmode_off"] = "OFF"
    },
    ["RC_CONFIG"] = {
      ["rc_deflection"] = "Deflessione dello stick dal centro in microsecondi (us).",
      ["rc_min_throttle"] = "Accelerazione minima (0% di potenza di accelerazione) prevista dalla radio, in microsecondi (us).",
      ["rc_max_throttle"] = "Accelerazione massima (0% di potenza di accelerazione) prevista dalla radio, in microsecondi (us).",
      ["rc_arm_throttle"] = "L'acceleratore deve essere a questo valore o inferiore in microsecondi (us) per consentire l'inserimento. Deve essere almeno 10 us inferiore all'acceleratore minimo.",
      ["rc_yaw_deadband"] = "Deadband per il controllo dell'imbardata in microsecondi (us).",
      ["rc_deadband"] = "Deadband per il controllo ciclico in microsecondi (us).",
      ["rc_center"] = "Centro Stick in microsecondi (us)."
    },
    ["FILTER_CONFIG"] = {
      ["gyro_soft_notch_cutoff_2"] = "Larghezza del filtro notch in Hz.",
      ["gyro_lpf1_static_hz"] = "Frequenza di taglio del filtro passa-basso in Hz.",
      ["tbl_none"] = "NONE",
      ["dyn_notch_max_hz"] = "Frequenza massima a cui e' applicata il notch.",
      ["tbl_1st"] = "1°",
      ["rpm_min_hz"] = "Frequenza minima per il filtro RPM.",
      ["dyn_notch_min_hz"] = "Frequenza minima a cui e' applicato il notch.",
      ["gyro_lpf1_dyn_max_hz"] = "Filtro dinamico taglio massimo in Hz.",
      ["gyro_soft_notch_hz_2"] = "Frequenza centrale a cui e' applicato il notch.",
      ["gyro_soft_notch_cutoff_1"] = "Ampiezza del filtro notch in Hz.",
      ["gyro_soft_notch_hz_1"] = "Frequenza centrale a cui e' applicato il notch.",
      ["dyn_notch_count"] = "Numero di Notch (tacche) da applicare.",
      ["dyn_notch_q"] = "Fattore Qualita' del filtro notch.",
      ["gyro_lpf2_static_hz"] = "Frequenza di taglio del filtro passa-basso in Hz.",
      ["gyro_lpf1_dyn_min_hz"] = "Taglio Minimo del Filtro Dinamico in Hz.",
      ["tbl_2nd"] = "2ND",
      ["tbl_custom"] = "CUSTOM",
      ["tbl_low"] = "BASSO",
      ["tbl_medium"] = "MEDIO",
      ["tbl_high"] = "ALTO"
    },
    ["ESC_PARAMETERS_XDFLY"] = {
      ["tbl_jadegreen"] = "VERDE GIADA",
      ["tbl_off"] = "Off",
      ["tbl_low"] = "basso",
      ["tbl_orange"] = "ARANCIO",
      ["tbl_fmfw"] = "Ala Fissa",
      ["tbl_ccw"] = "CCW",
      ["tbl_medium"] = "Medio",
      ["tbl_yellow"] = "GIALLO",
      ["tbl_reverse"] = "Inverti",
      ["tbl_red"] = "Rosso",
      ["tbl_high"] = "Alto",
      ["tbl_auto"] = "Auto",
      ["tbl_cw"] = "CW",
      ["tbl_fmheli"] = "Elicottero",
      ["tbl_purple"] = "VIOLA",
      ["tbl_green"] = "VERDE",
      ["tbl_blue"] = "BLU",
      ["tbl_slow"] = "Lento",
      ["tbl_normal"] = "Normale",
      ["tbl_fast"] = "Veloce",
      ["tbl_escgov"] = "ESC Governor",
      ["tbl_white"] = "BIANCO",
      ["tbl_cyan"] = "CYANO",
      ["tbl_vslow"] = "Molto Lento",
      ["tbl_extgov"] = "Governor Esterno",
      ["tbl_pink"] = "ROSA",
      ["tbl_fwgov"] = "Ala Fissa",
      ["tbl_on"] = "On"
    },
    ["ESC_PARAMETERS_SCORPION"] = {
      ["tbl_airplane"] = "Modo Aereoplano",
      ["tbl_cw"] = "CW",
      ["tbl_off"] = "Off",
      ["tbl_quad"] = "Modo Quad",
      ["tbl_heligov"] = "Heli Governor",
      ["tbl_exbus"] = "Jeti Exbus",
      ["tbl_boat"] = "Modo Barca",
      ["tbl_unsolicited"] = "Non Sollecitato",
      ["tbl_futsbus"] = "Futaba SBUS",
      ["tbl_ccw"] = "CCW",
      ["tbl_helistore"] = "Heli Governor (stored)",
      ["tbl_standard"] = "Standard",
      ["tbl_on"] = "On",
      ["tbl_vbar"] = "VBar",
      ["tbl_vbargov"] = "VBar Governor",
      ["tbl_extgov"] = "Governor Esterno"
    }
  },
  ["app"] = {
    ["btn_ok"] = "          OK           ",
    ["btn_close"] = "CHIUDI",
    ["navigation_menu"] = "MENU",
    ["menu_section_hardware"] = "Hardware",
    ["msg_please_disarm_to_save_warning"] = "I Settaggi saranno salvati su eeprom al disarmo",
    ["msg_saving_settings"] = "Salvo settiggi...",
    ["msg_saving_to_fbl"] = "Salvo dati al flight controller...",
    ["navigation_reload"] = "RICARICA",
    ["menu_section_developer"] = "Sviluppatore",
    ["check_msp_version"] = "Impossibile determinare la MSP in uso.",
    ["menu_section_about"] = "A proposito",
    ["msg_please_disarm_to_save"] = "Prego disarmare per salvare",
    ["unit_hertz"] = "Hz",
    ["msg_rebooting"] = "Rebooting...",
    ["msg_save_settings"] = "Salva settaggi",
    ["btn_cancel"] = "Configure the motor and speed controller features.",
    ["msg_connecting_to_fbl"] = "Connessione al flight controller...",
    ["navigation_help"] = "?",
    ["modules"] = {
      ["stats"] = {
        ["name"] = "Stats",
        ["totalflighttime"] = "Totale Tempo Volo",
        ["flightcount"] = "Conteggio Voli",
        ["lastflighttime"] = "Tempo ultimo Volo",
        ["help_p1"] = "Usa questo modulo per aggiornare le statistiche di volo sulla radio."
      },
      ["settings"] = {
        ["name"] = "Impostazioni",
        ["no_themes_available_to_configure"] = "Nessun tema disponibile configurabile",
        ["txt_audio_timer"] = "Timer",
        ["txt_audio_events"] = "Eventi",
        ["txt_audio_switches"] = "Interruttori",
        ["txt_iconsize"] = "Dimensione Icone",
        ["txt_general"] = "Generale",
        ["txt_text"] = "TESTO",
        ["txt_small"] = "SMALL",
        ["txt_large"] = "LARGE",
        ["txt_syncname"] = "Sync nome modello",
        ["txt_devtools"] = "Tools Sviluppatori",
        ["txt_apiversion"] = "API Versione",
        ["txt_logging"] = "Logging",
        ["txt_compilation"] = "Compilazione",
        ["txt_loglocation"] = "Percorso Log",
        ["txt_console"] = "CONSOLE",
        ["txt_consolefile"] = "CONSOLE & FILE",
        ["txt_loglevel"] = "Livello Log",
        ["txt_off"] = "OFF",
        ["txt_info"] = "INFO",
        ["txt_debug"] = "DEBUG",
        ["txt_mspdata"] = "Log msp data",
        ["txt_queuesize"] = "Dimensione Coda Log MSP",
        ["txt_memusage"] = "Uso Memoria Log",
        ["txt_batttype"] = "Tx Battery Options",
        ["txt_battdef"] = "Default",
        ["txt_batttext"] = "Text",
        ["txt_battdig"] = "Digital",
        ["dashboard"] = "Dashboard",
        ["dashboard_theme"] = "Tema",
        ["dashboard_theme_panel_global"] = "Globale",
        ["dashboard_theme_panel_model"] = "Modello",
        ["dashboard_theme_panel_model_disabled"] = "Disabilitato",
        ["dashboard_settings"] = "Settiggi",
        ["dashboard_theme_preflight"] = "Tema PreVolo",
        ["dashboard_theme_inflight"] = "Tema InVolo",
        ["dashboard_theme_postflight"] = "Tema PostVolo",
        ["audio"] = "Audio",
        ["localizations"] = "Localizzazione",
        ["txt_development"] = "Sviluppo",
        ["temperature_unit"] = "Unità Temperatura",
        ["altitude_unit"] = "Unità Altitudine",
        ["celcius"] = "Celsius",
        ["fahrenheit"] = "Fahrenheit",
        ["meters"] = "Metri",
        ["feet"] = "Piedi",
        ["warning"] = "Attenzione",
        ["governor_state"] = "Stato Governor",
        ["arming_flags"] = "Arming Flags",
        ["voltage"] = "Voltaggio",
        ["pid_rates_profile"] = "Profilo PID/escursioni",
        ["pid_profile"] = "Profilo PID",
        ["rate_profile"] = "Profilo Escursioni",
        ["esc_temperature"] = "ESC Temperatura",
        ["esc_threshold"] = "Soglia (°)",
        ["bec_voltage"] = "Voltaggio BEC",
        ["bec_threshold"] = "Soglia (V)",
        ["fuel"] = "Carburante",
        ["fuel_callout_default"] = "Default (Solo al 10%)",
        ["fuel_callout_10"] = "Ogni 10%",
        ["fuel_callout_20"] = "Ogni 20%",
        ["fuel_callout_25"] = "Ogni 25%",
        ["fuel_callout_50"] = "Ogni 50%",
        ["fuel_callout_percent"] = "Callout %",
        ["fuel_repeats_below"] = "Ripeti sotto 0%",
        ["fuel_haptic_below"] = "Haptic sotto 0%",
        ["timer_alerting"] = "Avviso Timer",
        ["timer_elapsed_alert_mode"] = "Avviso Timer Trascorso",
        ["timer_prealert_options"] = "Opzioni Avviso Pre-timer",
        ["timer_prealert"] = "Avviso Pre-timer",
        ["timer_alert_period"] = "Periodo Avviso",
        ["timer_postalert_options"] = "Opzioni Avvisi Post-timer",
        ["timer_postalert"] = "Avviso Post-timer",
        ["timer_postalert_period"] = "Periodo Avviso",
        ["timer_postalert_interval"] = "Intervallo Avviso"
      },
      ["validate_sensors"] = {
        ["help_p1"] = "Questo strumento prova a elencare tutti i sensori che non ricevi in una lista concisa.",
        ["invalid"] = "INVALIDO",
        ["name"] = "Sensori",
        ["msg_repair"] = "Abilito i sensori richiesti sul flight controller?",
        ["msg_repair_fin"] = "Il flight controller e' stato configurato? Potresti necessitare di riscoprire i sensori per vedere i cambiamenti.",
        ["ok"] = "OK",
        ["help_p2"] = "Usa questo strumento per assicurarti di inviare i sensori corretti."
      },
      ["msp_exp"] = {
        ["help_p1"] = "Questo strumento permette di inviare una stringa byte custom al flight controller. E' utile in fase di sviluppo/Debug",
        ["name"] = "MSP Sperimentale",
        ["help_p2"] = "Se non sai cosa stai facendo, non usare, le cose brutte accadono."
      },
      ["esc_tools"] = {
        ["unknown"] = "SCONOSCIUTO",
        ["name"] = "Strumenti ESC",
        ["please_powercycle"] = "Prego spegnere e riaccendere l'ESC...",
        ["mfg"] = {
          ["hw5"] = {
            ["esc"] = "ESC",
            ["brake_force"] = "Force Freno%",
            ["rotation"] = "Rotazione",
            ["soft_start"] = "Soft Start",
            ["name"] = "Hobbywing V5",
            ["limits"] = "Limiti",
            ["bec_voltage"] = "Voltaggio BEC",
            ["gov_i_gain"] = "I-Gain",
            ["startup_time"] = "Tempo di Startup",
            ["lipo_cell_count"] = "Conto Celle LiPo",
            ["restart_time"] = "Reimposta Tempo",
            ["volt_cutoff_type"] = "Tipo Volt Cutoff",
            ["motor"] = "Motore",
            ["brake_type"] = "Tipo Freno",
            ["brake"] = "Freno",
            ["governor"] = "Governor",
            ["advanced"] = "Avanzato",
            ["basic"] = "Basic",
            ["flight_mode"] = "Modo Volo",
            ["auto_restart"] = "Auto Riavvia",
            ["active_freewheel"] = "Attiva Freewheel",
            ["cutoff_voltage"] = "Voltaggio Taglio",
            ["startup_power"] = "Startup Power",
            ["other"] = "Altro",
            ["timing"] = "Timing",
            ["gov_p_gain"] = "P-Gain"
          },
          ["xdfly"] = {
            ["hv_bec_voltage"] = "Voltaggio HV BEC",
            ["gov"] = "Governor",
            ["brake_force"] = "Forza Freno",
            ["sr_function"] = "Funzione SR",
            ["name"] = "XDFLY",
            ["lv_bec_voltage"] = "Voltaggio LV BEC",
            ["auto_restart_time"] = "Tempo di Auto Riavvio",
            ["acceleration"] = "Accelerazione",
            ["motor_direction"] = "Direzione Motore",
            ["smart_fan"] = "Ventola Smart",
            ["governor"] = "Governor",
            ["advanced"] = "Avanzato",
            ["gov_i"] = "Gov-I",
            ["cell_cutoff"] = "Taglio Cella",
            ["led_color"] = "Colore LED",
            ["basic"] = "Base",
            ["startup_power"] = "Startup Power",
            ["motor_poles"] = "Poli Motore",
            ["capacity_correction"] = "Correzione Capacita'",
            ["timing"] = "Timing",
            ["gov_p"] = "Gov-P"
          },
          ["flrtr"] = {
            ["gov"] = "Governor",
            ["motor_temp_sensor"] = "Sensore temp Motore",
            ["starting_torque"] = "Coppia di avvio",
            ["cell_count"] = "Numero Celle",
            ["gov_p"] = "Gov-P",
            ["motor_erpm_max"] = "ERPM max motore",
            ["name"] = "FLYROTOR",
            ["low_voltage_protection"] = "Protezione Voltaggio basso",
            ["gov_d"] = "Gov-D",
            ["telemetry_protocol"] = "Protocollo Telemetria",
            ["motor_direction"] = "Direzione Motore",
            ["throttle_protocol"] = "Protocollo Gas/Throttle",
            ["soft_start"] = "Soft start",
            ["other"] = "Altro",
            ["temperature_protection"] = "Temperatura di protezione",
            ["buzzer_volume"] = "Volume Buzzer",
            ["timing_angle"] = "Timing angle",
            ["governor"] = "Governor",
            ["advanced"] = "Avanzato",
            ["gov_i"] = "Gov-I",
            ["bec_voltage"] = "Voltaggio BEC",
            ["fan_control"] = "Controllo Ventola",
            ["basic"] = "Base",
            ["current_gain"] = "Guadagno Corrente",
            ["led_color"] = "Colore LED",
            ["motor_temp"] = "Temperatura Motore",
            ["response_speed"] = "Velocita' risposta",
            ["battery_capacity"] = "Capacita' Batteria"
          },
          ["scorp"] = {
            ["esc_mode"] = "Modo ESC",
            ["min_voltage"] = "Voltaggio Min",
            ["rotation"] = "Rotazione",
            ["telemetry_protocol"] = "Protocollo Telemetria",
            ["name"] = "Scorpion",
            ["runup_time"] = "Tempo Runup",
            ["motor_startup_sound"] = "Suono di avvio Motore",
            ["gov_integral"] = "Gov Integrale",
            ["gov_proportional"] = "Gov Proporzionale",
            ["cutoff_handling"] = "Cutoff Handling",
            ["bailout"] = "Soccorso",
            ["limits"] = "Limiti",
            ["soft_start_time"] = "Tempos di Soft Start",
            ["advanced"] = "Avanzato",
            ["bec_voltage"] = "Voltaggio BEC",
            ["extra_msg_save"] = "Prego riavviare l'ESC per applicare i cambiamenti",
            ["basic"] = "Base",
            ["max_current"] = "Corrente Max",
            ["max_temperature"] = "Temperatura Max",
            ["protection_delay"] = "Ritardo protezion",
            ["max_used"] = "Max Usato"
          },
          ["yge"] = {
            ["esc_mode"] = "Modo ESC",
            ["esc"] = "ESC",
            ["current_limit"] = "Limite Corrente",
            ["f3c_auto"] = "F3C Autorotazione",
            ["name"] = "YGE",
            ["max_start_power"] = "Max Start Power",
            ["lv_bec_voltage"] = "BEC",
            ["pinion_teeth"] = "Denti Pinione",
            ["auto_restart_time"] = "Tempo Auto Restart",
            ["main_teeth"] = "Denti Corona",
            ["other"] = "Altro",
            ["limits"] = "Limiti",
            ["cell_cutoff"] = "Taglio Cella",
            ["throttle_response"] = "Rispsota Accelerazione",
            ["stick_zero_us"] = "Zero Stick",
            ["advanced"] = "Avanzato",
            ["gov_i"] = "Gov-I",
            ["motor_pole_pairs"] = "Paio Poli Motore",
            ["stick_range_us"] = "Range Stick",
            ["basic"] = "Base",
            ["min_start_power"] = "Min Start Power",
            ["active_freewheel"] = "Attiva RuotaLibera",
            ["direction"] = "Direzione",
            ["timing"] = "Timing Motore",
            ["gov_p"] = "Gov-P"
          }
        },
        ["searching"] = "Cerco..."
      },
      ["pids"] = {
        ["help_p1"] = "FeedForward (rollio/beccheggio): iniziare a 70, aumentare fino a quando gli arresti sono netti senza deriva. Mantenere il rollio e il beccheggio uguali.",
        ["o"] = "O",
        ["pitch"] = "Pitch",
        ["i"] = "I",
        ["yaw"] = "Yaw",
        ["roll"] = "Roll",
        ["help_p5"] = "Prova & regola: vola, osserva e regola per ottenere le migliori prestazioni in condizioni reali.",
        ["p"] = "P",
        ["f"] = "F",
        ["name"] = "PIDs",
        ["help_p2"] = "I guadagno (rollio/beccheggio): aumentare gradualmente per stabilizzare le pompe del beccheggio. Valori troppo alti causano oscillazioni; abbinare i valori di rollio/beccheggio.",
        ["d"] = "D",
        ["b"] = "B",
        ["help_p4"] = "Guadagno di arresto della coda (CW/CCW): Regolare separatamente per arresti puliti e senza rimbalzi in entrambe le direzioni.",
        ["help_p3"] = "Guadagni P/I/D della coda: aumentare P fino a quando non si nota una leggera oscillazione negli imbuti, quindi diminuire leggermente. Aumentare I fino a quando la coda non rimane ferma nei movimenti bruschi (troppo alto provoca un'oscillazione lenta). Regolare D per arresti fluidi: piu' alto per i servi lenti, piu' basso per quelli veloci."
      },
      ["msp_speed"] = {
        ["seconds_600"] = "  600S  ",
        ["avg_query_time"] = "Tempo medio query",
        ["seconds_30"] = "  30S  ",
        ["name"] = "Velocita' MSP",
        ["max_query_time"] = "Tempo massimo query",
        ["help_p1"] = "Questo strumento tenta di determinare la qualita' del collegamento dati MSP eseguendo il maggior numero possibile di query MSP di grandi dimensioni entro 30 secondi.",
        ["retries"] = "Riprova",
        ["checksum_errors"] = "Checksum errors",
        ["test_length"] = "Test lunghezza",
        ["start"] = "Inizio",
        ["memory_free"] = "Memoria Libera",
        ["start_prompt"] = "Vuoi iniziare il test? Scegli qui sotto il tempo di prova.",
        ["rf_protocol"] = "Protocollo RF",
        ["min_query_time"] = "Tempo Minimo query",
        ["seconds_120"] = "  120S  ",
        ["seconds_300"] = "  300S  ",
        ["testing"] = "Test in corso",
        ["successful_queries"] = "Query Valide",
        ["timeouts"] = "Timeouts",
        ["testing_performance"] = "Test performance MSP...",
        ["total_queries"] = "Query Totali"
      },
      ["copyprofiles"] = {
        ["profile_type"] = "Tipo Profilo",
        ["profile_type_pid"] = "PID",
        ["profile_type_rate"] = "Rate",
        ["msgbox_save"] = "Salva settaggi",
        ["name"] = "Copia Profili",
        ["help_p1"] = "Copia il profilo PID o il profilo di velocita' dalla sorgente alla destinazione.",
        ["dest_profile"] = "Profilo Dest.",
        ["source_profile"] = "Prof. Sorgente",
        ["msgbox_msg"] = "Salvare la pagina corrente sul controller di volo?",
        ["help_p2"] = "Scegli la sorgente e le destinazioni e salva per copiare il profilo."
      },
      ["esc_motors"] = {
        ["min_throttle"] = "Valore PWM 0% Throttle",
        ["tail_motor_ratio"] = "Rateo Motore Coda",
        ["max_throttle"] = "Valore PWM 100% Throttle",
        ["main_motor_ratio"] = "Rateo Motore Primario",
        ["pinion"] = "Pinione",
        ["main"] = "Principale",
        ["help_p1"] = "Configurare le funzioni del motore e del regolatore di velocita'.",
        ["rear"] = "Dietro",
        ["front"] = "Fronte",
        ["voltage_correction"] = "Correzione Voltaggio",
        ["mincommand"] = "Valore PWM Stop Motore",
        ["name"] = "ESC/Motori",
        ["motor_pole_count"] = "Numero Magneti Motore",
        ["current_correction"] = "Correzione Corrente",
        ["consumption_correction"] = "Correzione Consumo"
      },
      ["radio_config"] = {
        ["deflection"] = "Deflessione",
        ["max_throttle"] = "Max",
        ["stick"] = "Stick",
        ["arming"] = "Armo",
        ["yaw_deadband"] = "Yaw",
        ["cyclic"] = "Ciclico",
        ["name"] = "Radio Config",
        ["help_p1"] = "Configurare le impostazioni della radio. Centro Stick, attivazione, blocco acceleratore e disattivazione acceleratore.",
        ["min_throttle"] = "Min",
        ["throttle"] = "Throttle",
        ["deadband"] = "Deadband",
        ["center"] = "Centro"
      },
      ["profile_select"] = {
        ["help_p1"] = "Imposta il profilo di volo o il profilo di velocita' corrente che desideri utilizzare.",
        ["rate_profile"] = "Profilo Escurioni",
        ["pid_profile"] = "Profilo PID",
        ["save_prompt"] = "Salvare la pagina corrente sul controller di volo?",
        ["save_prompt_local"] = "Salva i settaggi correnti?",
        ["cancel"] = "CANCELLA",
        ["name"] = "Seleziona Profilo",
        ["save_settings"] = "Salva settaggi",
        ["ok"] = "OK",
        ["help_p2"] = "Se si utilizza un interruttore sulla radio per cambiare le modalita' di volo o di velocita', questa scelta verra' ignorata non appena si aziona l'interruttore."
      },
      ["profile_governor"] = {
        ["tail_torque_assist"] = "Assistenza Coppia Rot.coda",
        ["p"] = "P",
        ["i"] = "I",
        ["yaw"] = "Yaw",
        ["cyc"] = "Cyc",
        ["f"] = "F",
        ["name"] = "Governor",
        ["d"] = "D",
        ["help_p1"] = "Velocita' massima della Rotore: velocita' target della Rotore quando si e' al 100% dell'input dell'acceleratore.",
        ["help_p6"] = "Assistenza alla coppia di coda: per code motorizzate. Guadagno e limite di aumento della velocita' utilizzando la coppia del rotore principale per l'assistenza yaw",
        ["help_p4"] = "Precomp: guadagno precomp per gli input di imbardata, ciclico e collettivo.",
        ["max_throttle"] = "Max throttle",
        ["full_headspeed"] = "Piena Velocita'Rotore",
        ["precomp"] = "Precomp",
        ["gain"] = "Guadagno Master PID",
        ["disabled_message"] = "Governor Rotorflight disabilitato",
        ["help_p3"] = "Guadagni: tuning fine del governor.",
        ["col"] = "Col",
        ["min_throttle"] = "Min throttle",
        ["tta_limit"] = "Limite",
        ["help_p2"] = "Guadagno principale PID: quanto lavora il regolatore per mantenere i RPM.",
        ["gains"] = "Guadagni",
        ["help_p5"] = "Max throttle (Accelerazione massima): La massima percentuale di accelerazione che il regolatore puo' utilizzare.",
        ["tta_gain"] = "Guadagno"
      },
      ["profile_tailrotor"] = {
        ["gain"] = "Guadagno",
        ["help_p4"] = "Guadagno FF collettivo: Precompensazione della coda per gli input collettivi.",
        ["collective_impulse_ff"] = "Impulso FF Collettivo",
        ["help_p2"] = "Precomp Cutoff: limite di frequenza per tutte le azioni di precompensazione dell'imbardata.",
        ["cutoff"] = "Cutoff",
        ["help_p3"] = "Guadagno FF ciclico: Precompensazione di coda per ingressi ciclici.",
        ["help_p1"] = "Guadagno di Stop YAW: uno stop piu' elevato fara' fermare la coda in modo piu' aggressivo, ma potrebbe causare oscillazioni se troppo alto. Regolare CW o CCW per rendere gli stop yaw uniformi.",
        ["inertia_precomp"] = "Inerzia Precomp",
        ["cyclic_ff_gain"] = "Guadagno FF Ciclico",
        ["help_p5"] = "Impulso collettivo FF: Precompensazione di coda dell'impulso per input collettivi. Se e' necessaria una precompensazione di coda extra all'inizio dell'input collettivo.",
        ["cw"] = "CW",
        ["ccw"] = "CCW",
        ["yaw_stop_gain"] = "Guadagno Yaw stop",
        ["precomp_cutoff"] = "Precomp Cutoff",
        ["collective_ff_gain"] = "Guadagno FF Collettivo",
        ["name"] = "Rotore Coda",
        ["decay"] = "Decadimento"
      },
      ["profile_pidcontroller"] = {
        ["help_p4"] = "Errore rotazione: consente di condividere gli errori tra tutti gli assi.",
        ["ground_error_decay"] = "Errore Decadimento Suolo",
        ["yaw"] = "Y",
        ["inflight_error_decay"] = "Errore Decadimento InVolo",
        ["help_p2"] = "Errore limite: Angolo limite per I-term.",
        ["error_limit"] = "Errore limite",
        ["help_p3"] = "Limite Offset: Angolo limite per Integrale Alta Velocita' (O-term).",
        ["cutoff_point"] = "Punto Cut-off",
        ["limit"] = "Limite",
        ["iterm_relax"] = "I-term relax",
        ["hsi_offset_limit"] = "Limite Offset HSI",
        ["pitch"] = "P",
        ["name"] = "Controller PID",
        ["error_rotation"] = "Errore rotazione",
        ["roll"] = "R",
        ["help_p5"] = "I-term relax: Limitare l'accumulo di I-term durante i movimenti rapidi: Riduce il rimbalzo dopo i movimenti rapidi dello stick. Generalmente deve essere piu' basso per gli elicotteri grandi e puo' essere piu' alto per quelli piccoli. e' meglio ridurlo solo quanto necessario per il proprio stile di volo.",
        ["time"] = "Tempo",
        ["help_p1"] = "Errore decadimento suolo: Decadimento PID per evitare che l'elicottero si ribalti quando e' a terra."
      },
      ["logs"] = {
        ["help_logs_p2"] = "Nota. Per abilitare la registrazione e' essenziale che tu abbia abilitato i seguenti sensori.",
        ["name"] = "Logs",
        ["help_logs_p1"] = "Selezionare un file log dall'elenco seguente.",
        ["msg_no_logs_found"] = "LOG FILES ASSENTI",
        ["help_logs_tool_p1"] = "Utilizza lo slider per navigare nel grafico.",
        ["help_logs_p3"] = "- stato armo, voltaggio, Vel.Rotore, corrente, esc temperatura"
      },
      ["battery"] = {
        ["calcfuel_local"] = "Calculate fuel using",
        ["max_cell_voltage"] = "Voltaggio Max Cella",
        ["full_cell_voltage"] = "Voltaggio Cella pieno",
        ["name"] = "Batteria",
        ["min_cell_voltage"] = "Voltaggio Min Cella",
        ["help_p1"] = "Le impostazioni della batteria utilizzate per configurare il controller di volo in modo da monitorare la tensione e fornire avvisi quando la tensione scende al di sotto di un certo livello.",
        ["battery_capacity"] = "Capacita' Batteria",
        ["warn_cell_voltage"] = "Avviso tensione cella",
        ["cell_count"] = "Conto Celle",
        ["consumption_warning_percentage"] = "Avviso Consumo %",
        ["timer"] = "Tempo Volo",
        ["voltage_multiplier"] = "Compensazione di caduta",
        ["kalman_multiplier"] = "Filter compensation",
        ["alert_type"] = "BEC or Rx Batt Voltage Alert",
        ["bec_voltage_alert"] = "BEC Alert Value",
        ["rx_voltage_alert"] = "RX Batt Alert Value"
      },
      ["profile_mainrotor"] = {
        ["gain"] = "Guadagno",
        ["help_p4"] = "Limite di frequenza Cross Coupling: limite di frequenza per la compensazione, un valore piu' alto rendera' piu' veloce l'azione di compensazione.",
        ["collective_pitch_comp_short"] = "Compensazione Col. Pitch",
        ["cyclic_cross_coupling"] = "Cross coupling ciclico",
        ["collective_pitch_comp"] = "Compensazione Pitch Collettivo",
        ["name"] = "Rotore prim.",
        ["cutoff"] = "Cutoff",
        ["ratio"] = "Ratio",
        ["help_p1"] = "Compensazione collettiva del pitch: l'aumento compensera' il movimento di pitch causato dal trascinamento della coda durante la salita.",
        ["help_p2"] = "Guadagno cross coupling: rimuove l'accoppiamento di rollio quando viene applicato solo l'elevatore.",
        ["help_p3"] = "Cross Coupling Ratio: Quantita di compensazione (pitch vs roll) da applicare."
      },
      ["sbusout"] = {
        ["title"] = "SBUS Output",
        ["help_fields_source"] = "id sorgente per il mix, contate da 0-15.",
        ["help_default_p4"] = "- Per motori, usa 0, 1000.",
        ["ch_prefix"] = "CH",
        ["channel_prefix"] = "CANALE ",
        ["saving"] = "Salva",
        ["name"] = "SBUS Out",
        ["channel_page"] = "Sbus out / CH",
        ["receiver"] = "Ricevente",
        ["servo"] = "Servo",
        ["type"] = "Tipo",
        ["saving_data"] = "Salva dati...",
        ["help_fields_max"] = "Valore PWM massimo da inviare",
        ["motor"] = "Motore",
        ["help_default_p5"] = "- Oppure puoi personalizzare la mappatura.",
        ["help_default_p1"] = "Configurare la miscelazione avanzata e la mappatura dei canali se si dispone di SBUS Out abilitato su una porta seriale.",
        ["max"] = "Max",
        ["save_prompt"] = "Salvare la pagina corrente sul controller di volo?",
        ["help_fields_min"] = "Il valore pwm minimo da inviare.",
        ["mixer"] = "Mixer",
        ["ok"] = "OK",
        ["cancel"] = "CANCELLA",
        ["help_default_p2"] = "- Per i canali RX o i servi (banda larga), utilizzare 1000, 2000 o 500,1000 per i servi a banda stretta.",
        ["save_settings"] = "Salva settaggi",
        ["min"] = "Min",
        ["help_default_p3"] = "- Per le regole del mixer, usa -1000, 1000.",
        ["source"] = "Sorgente"
      },
      ["profile_rescue"] = {
        ["help_p4"] = "Hover: Quanto collettivo per mantenere un volo stazionario costante.",
        ["hover"] = "Hover",
        ["collective"] = "Collettivo",
        ["help_p2"] = "Pull-up: Quanto collettivo e per quanto tempo arrestare la caduta.",
        ["climb"] = "Sali",
        ["mode_enable"] = "Abilita Modo Soccorso",
        ["help_p3"] = "Salita: quanto collettivo per mantenere una salita costante e per quanto tempo.",
        ["help_p1"] = "Capovolgi in posizione verticale: capovolgi l'elicottero in posizione verticale quando viene attivato il soccorso.",
        ["flip_upright"] = "Capovolgere in posizione verticale",
        ["flip"] = "Flip",
        ["level_gain"] = "Livello",
        ["name"] = "Soccorso",
        ["exit_time"] = "Tempo di Uscita",
        ["help_p5"] = "Flip:Quanto tempo aspettare prima di interrompere perché il ribaltamento non ha funzionato.",
        ["help_p6"] = "Guadagni: Quanto e' arduo mantenere l'elicottero in piano quando si attiva la modalita' di salvataggio.",
        ["fail_time"] = "Tempo di Fallimento",
        ["pull_up"] = "Pull-up",
        ["rate"] = "Rate",
        ["help_p7"] = "Velocita' e accelerazione: velocita' massima di rotazione e accelerazione durante il livellamento durante il soccorso.",
        ["gains"] = "Guadagni",
        ["time"] = "Tempo",
        ["accel"] = "Accel"
      },
      ["trim"] = {
        ["disable_mixer_message"] = "Riportare il controllo dei servocomandi al controller di volo.",
        ["tail_motor_idle"] = "Minimo rotore coda  %",
        ["disable_mixer_override"] = "Disabilita mixer override",
        ["yaw_trim"] = "Yaw. trim %",
        ["enable_mixer_message"] = "Impostare tutti i servocomandi nella posizione centrale configurata. In questo modo, tutti i valori di questa pagina verranno salvati quando si regola il trim del servocomando.",
        ["mixer_override_disabling"] = "Disabilita mixer override...",
        ["roll_trim"] = "Roll trim %",
        ["pitch_trim"] = "Pitch trim %",
        ["name"] = "Trim",
        ["help_p2"] = "Coda motorizzata: se si utilizza una coda motorizzata, impostarla per il minimo e l'imbardata zero.",
        ["mixer_override"] = "Mixer Override",
        ["mixer_override_enabling"] = "Abilita mixer override...",
        ["enable_mixer_override"] = "Abilita mixer override",
        ["collective_trim"] = "Col. trim %",
        ["help_p1"] = "Link trims: Utilizzare per correggere piccoli problemi di livellamento nel piatto oscillante. In genere utilizzato solo se i link oscillanti non sono regolabili."
      },
      ["governor"] = {
        ["help_p1"] = "Questi parametri si applicano globalmente al regolatore, indipendentemente dal profilo in uso.",
        ["handover_throttle"] = "Trasferimento acceleratore%",
        ["spoolup_min_throttle"] = "Rampa di accelerazione % min",
        ["recovery_time"] = "Tempo Recupero",
        ["mode"] = "Modo",
        ["help_p2"] = "Ogni parametro e' semplicemente un valore temporale in secondi per ogni azione del regolatore.",
        ["tracking_time"] = "Tempo Tracciatura",
        ["name"] = "Governor",
        ["startup_time"] = "Tempo Startup",
        ["spoolup_time"] = "Tempo Rampa di accelerazione"
      },
      ["accelerometer"] = {
        ["help_p1"] = "L'accelerometro e' utilizzato per misurare l'angolo del controller di volo rispetto all'orizzonte. Questi dati sono utilizzati per stabilizzare l'aeromobile e fornire la funzionalita' di autolivellamento.",
        ["name"] = "Accelerometro",
        ["pitch"] = "Pitch",
        ["msg_calibrate"] = "Calibra accelerometro?",
        ["roll"] = "Roll"
      },
      ["gyro_alignment"] = {
        ["name"] = "Allineamento Scheda",
        ["board_alignment"] = "Allineamento Scheda",
        ["roll"] = "Roll",
        ["pitch"] = "Pitch",
        ["yaw"] = "Yaw",
        ["msg_calibrate"] = "Calibrare l'accelerometro? Questo resetta l'accelerometro e applica le impostazioni di allineamento correnti della scheda.",
        ["help_p1"] = "Lo strumento di allineamento della scheda consente di configurare gli angoli di allineamento della scheda per compensare l'orientamento di montaggio del controller di volo.",
        ["help_p2"] = "Allineamento Scheda: Regola gli angoli di roll, pitch e yaw (in gradi) per corrispondere all'orientamento fisico del tuo controller di volo rispetto al telaio dell'elicottero.",
        ["help_p3"] = "Queste impostazioni compensano i casi in cui il controller di volo non e' montato perfettamente allineato con gli assi del telaio dell'elicottero.",
        ["help_p4"] = "Usa il pulsante Strumento per calibrare l'accelerometro dopo aver apportato modifiche all'allineamento. Salva le impostazioni nell'EEPROM quando completato."
      },
      ["rates"] = {
        ["help_table_5_p2"] = "Velocita' massima: velocita' di rotazione massima alla massima escursione dello stick in gradi al secondo.",
        ["actual"] = "ATTUALE",
        ["max_rate"] = "Max Rate",
        ["help_table_4_p3"] = "Expo: riduce la sensibilita' vicino al centro del joystick, dove sono necessari controlli precisi.",
        ["rate"] = "Rate",
        ["help_table_5_p1"] = "Rate RC: riduce la sensibilita' intorno al joystick centrale. I rate RC impostata a meta' della velocita' massima e' lineare. Un numero piu' basso ridurra' la sensibilita' intorno al joystick centrale. Un numero superiore alla meta' della velocita' massima aumentera' anche la velocita' massima.",
        ["help_table_4_p2"] = "Max Rate: Rateo massimo di rotazione alla deflessione piena dello stick in gradi secondo.",
        ["center_sensitivity"] = "Cntr. Sens.",
        ["rc_curve"] = "Curva RC",
        ["roll"] = "Roll",
        ["none"] = "NIENTE",
        ["betaflight"] = "BETAFLIGHT",
        ["kiss"] = "KISS",
        ["help_table_1_p3"] = "Expo: Riduce la sensibilita' vicino al centro del joystick, dove sono necessari controlli precisi.",
        ["help_table_3_p2"] = "Rate: Aumenta la velocita' di rotazione massima riducendo la sensibilita' intorno alla meta' del joystick.",
        ["help_table_2_p2"] = "Acro+: Aumenta la velocita' di rotazione massima riducendo la sensibilita' intorno alla meta' del joystick.",
        ["superrate"] = "SuperRate",
        ["help_table_2_p3"] = "Expo: Riduce la sensibilita' vicino al centro del joystick, dove sono necessari controlli precisi.",
        ["raceflight"] = "RACEFLIGHT",
        ["yaw"] = "Yaw",
        ["collective"] = "Col",
        ["name"] = "Rates",
        ["help_table_5_p3"] = "Expo: Riduce la sensibilita' vicino al centro del joystick, dove sono necessari controlli precisi.",
        ["help_table_3_p3"] = "RC Curve: Riduce la sensibilita' vicino al centro del joystick, dove sono necessari controlli precisi.",
        ["expo"] = "Expo",
        ["help_table_1_p2"] = "SuperRate: Aumenta la velocita' di rotazione massima riducendo la sensibilita' intorno alla meta' del joystick.",
        ["help_default_p2"] = "Useremo le sottoclassificazioni seguenti.",
        ["help_default_p1"] = "Default: Manteniamo questo per far apparire il pulsante per le escursioni.",
        ["quick"] = "VELOCE",
        ["pitch"] = "Pitch",
        ["acroplus"] = "Acro+",
        ["help_table_1_p1"] = "RC Rate: Velocita' di rotazione massima a piena deflessione dello stick.",
        ["rc_rate"] = "RC Rate",
        ["help_table_2_p1"] = "Rate: Velocita' di rotazione massima a piena deflessione dello stick in gradi secondo.",
        ["help_table_4_p1"] = "Sensibilita' al centro: da utilizzare per ridurre la sensibilita' attorno alla leva centrale. Impostare la sensibilita' al centro allo stesso valore della velocita' massima per una risposta lineare. Un numero inferiore alla velocita' massima ridurra' la sensibilita' attorno alla leva centrale. Si noti che un valore superiore alla velocita' massima aumentera' la velocita' massima - non raccomandato in quanto causa problemi nel log della Blackbox.",
        ["help_table_0_p1"] = "Tutti i valori sono impostati su zero perché non e' in uso alcuna TABELLA DI ESCURSIONI.",
        ["help_table_3_p1"] = "RC Rate: Velocita' di rotazione massima a piena deflessione dello Stick."
      },
      ["mixer"] = {
        ["help_p1"] = "Regolare la geometria del piatto oscillante, gli angoli di fase e i limiti.",
        ["collective_tilt_correction_pos"] = "Positivo",
        ["geo_correction"] = "Correzione Geo",
        ["swash_tta_precomp"] = "TTA Precomp",
        ["name"] = "Mixer",
        ["collective_tilt_correction_neg"] = "Negativo",
        ["tail_motor_idle"] = "Minimo Thr coda%",
        ["swash_phase"] = "Angolo Fase",
        ["collective_tilt_correction"] = "Correzione dell'inclinazione collettiva",
        ["swash_pitch_limit"] = "Limite Totale Pitch"
      },
      ["about"] = {
        ["help_p1"] = "Questa pagina fornisce alcune informazioni utili che potrebbero esserti richieste quando richiedi assistenza.",
        ["msgbox_credits"] = "Crediti",
        ["ethos_version"] = "Versione Ethos",
        ["rf_version"] = "Versione Rotorflight",
        ["fc_version"] = "Versione FC",
        ["name"] = "A Proposito",
        ["supported_versions"] = "Versioni MSP Supportate",
        ["license"] = "e' possibile copiare, distribuire e modificare il software a condizione di tenere traccia delle modifiche/date nei file sorgente. Qualsiasi modifica o software che includa (tramite compilatore) codice con licenza GPL deve essere reso disponibile anche sotto la GPL insieme alle istruzioni di compilazione e installazione.",
        ["simulation"] = "Simulazione",
        ["help_p2"] = "Per assistenza, leggere prima le pagine di aiuto su www.rotorflight.org",
        ["opener"] = "Rotorflight e' un progetto open source. Il contributo di altre persone che condividono la stessa mentalita' e che desiderano contribuire a migliorare ulteriormente questo software e' ben accetto e incoraggiato. Non e' necessario essere un programmatore esperto per dare una mano.",
        ["version"] = "Versione",
        ["msp_version"] = "Versione MSP",
        ["credits"] = "I principali contributori sia al firmware Rotorflight che a questo software sono: Petri Mattila, Egon Lubbers, Rob Thomson, Rob Gayle, Phil Kaighin, Robert Burrow, Keith Williams, Bertrand Songis, Venbs Zhou... e molti altri che hanno trascorso ore a Rotorere e fornire feedback.!",
        ["msp_transport"] = "Trasporto MSP"
      },
      ["rates_advanced"] = {
        ["dyn_ceiling_gain"] = "Guadagno soglia Dinamico",
        ["acc_limit"] = "Limite Accelerometro",
        ["roll"] = "Roll",
        ["yaw_dynamics"] = "Dinamiche Yaw",
        ["pitch"] = "Pitch",
        ["col"] = "Col",
        ["setpoint_boost_cutoff"] = "Punto di taglio boost",
        ["yaw_dynamic_deadband_gain"] = "D. Band",
        ["rates_type"] = "Tipo Rates",
        ["setpoint_boost_gain"] = "Guadagno punto boost",
        ["msg_reset_to_defaults"] = "Tipo Rate variato. I Valori saranno riportati a Default.",
        ["yaw_dynamic_ceiling_gain"] = "Tetto/Soglia",
        ["yaw_boost"] = "Yaw boost",
        ["gain"] = "Guadagno",
        ["rate_table"] = "Tavola Rate",
        ["dynamics"] = "Dinamiche",
        ["yaw"] = "Yaw",
        ["yaw_dynamic_deadband_filter"] = "Filtro",
        ["name"] = "Rates",
        ["cutoff"] = "Cutoff",
        ["help_rate_table"] = "Selezionare la tariffa che si desidera utilizzare. Salvando si applichera' la scelta al profilo attivo.",
        ["help_p1"] = "Tipo di rateo: Scegli il tipo di rateo con cui preferisci volare. Raceflight e Actual sono i piu' semplici.",
        ["pitch_boost"] = "Pitch boost",
        ["help_p2"] = "Dinamica: applicata indipendentemente dal tipo di curve. In genere lasciata sui valori predefiniti, ma puo' essere regolata per smussare i movimenti dell'elicottero, come con gli elicotteri in scala.",
        ["accel_limit"] = "Accel",
        ["dyn_deadband_filter"] = "Filtro Dinamico deadband",
        ["roll_boost"] = "Roll boost",
        ["dyn_deadband_gain"] = "Guadagno deadband dinamica",
        ["collective_dynamics"] = "Dinamica Collettivo",
        ["roll_dynamics"] = "Dinamica Roll",
        ["collective_boost"] = "Boost Collettivo",
        ["pitch_dynamics"] = "Dinamica Pitch",
        ["response_time"] = "Tempoo Risposta"
      },
      ["servos"] = {
        ["tbl_yes"] = "SI",
        ["enable_servo_override"] = "Abilita servo override",
        ["disabling_servo_override"] = "Disabilita servo override...",
        ["help_tool_p3"] = "Minimo/Massimo: Regola il finecorsa del servo selezionato.",
        ["tail"] = "CODA",
        ["scale_negative"] = "Scala Negativa",
        ["help_tool_p1"] = "Override: [*] Abilita override per permettere aggiornamento in tempo reale del centro del servo.",
        ["tbl_no"] = "NO",
        ["maximum"] = "Massimo",
        ["help_tool_p6"] = "Velocita': La velocita' con cui si muove il servo. Generalmente viene usata solo per i servo ciclici per aiutare il piatto ciclico a muoversi in modo uniforme. Opzionale: lasciare tutto a 0 se non si e' sicuri.",
        ["help_fields_rate"] = "Curva PWM Servo.",
        ["cyc_pitch"] = "CYC.PITCH",
        ["center"] = "Centro",
        ["minimum"] = "Minimo",
        ["speed"] = "Velocita'",
        ["help_fields_speed"] = "Velocita' movimento Servo in millisecondi.",
        ["disable_servo_override"] = "Disabilita servo override",
        ["help_fields_scale_pos"] = "Scalatura Positiva Servo.",
        ["saving_data"] = "Salva dati...",
        ["cyc_left"] = "CYC.LEFT",
        ["saving"] = "Salva",
        ["name"] = "Servi",
        ["help_tool_p5"] = "Rateo: La frequenza alla quale il servo funziona meglio - verificare con il produttore.",
        ["help_tool_p2"] = "Centro: Regola la posizione della meta' corsa del Servo.",
        ["enabling_servo_override"] = "Abilita servo override...",
        ["servo_prefix"] = "SERVO ",
        ["reverse"] = "Inverti",
        ["enable_servo_override_msg"] = "Override del Servo permette di trimmare la meta' corsa in tempo reale.",
        ["cyc_right"] = "CYC.RIGHT",
        ["help_default_p2"] = "I controlli di volo primari che utilizzano il mixer di volo del rotore verranno visualizzati nella sezione denominata “mixer”.",
        ["scale_positive"] = "Scala Positiva",
        ["help_default_p1"] = "Selezionare il servo che si desidera configurare dall'elenco sottostante.",
        ["servo_override"] = "Servo Override",
        ["disable_servo_override_msg"] = "Riportare il controllo dei servocomandi al controller di volo.",
        ["help_fields_min"] = "Limite corsa negativo del Servo.",
        ["help_default_p3"] = "Tutti gli altri servi che non sono controllati dal mixer di volo primario saranno visualizzati nella sezione denominata “Altri servi”.",
        ["help_fields_mid"] = "Larghezza impulso posizione centrale servo.",
        ["help_fields_scale_neg"] = "Scalatura negativa Servo.",
        ["rate"] = "Valori",
        ["help_tool_p4"] = "Scala: regola la quantita' di movimento del servo per un dato input.",
        ["help_fields_flags"] = "0 = Default, 1=Inverti, 2 = Correzione Geo, 3 = Inverti + Correzione Geo",
        ["geometry"] = "Geometria",
        ["help_fields_max"] = "Limite corsa Servo positiva."
      },
      ["profile_autolevel"] = {
        ["acro_trainer"] = "Acro trainer",
        ["angle_mode"] = "Angle mode",
        ["max"] = "Max",
        ["name"] = "Autolivello",
        ["help_p1"] = "Acro Trainer: Quanto aggressivamente l'elicottero quando si inclina all'indietro per livellarsi quando si vola in modalita' Acro Trainer.",
        ["horizon_mode"] = "Horizon mode",
        ["gain"] = "Guadagno",
        ["help_p2"] = "Angle Mode: Quanto aggressivamente l'elicottero quando si inclina all'indietro per livellarsi quando si vola in modalita' Angle",
        ["help_p3"] = "Horizon Mode: Quanto aggressivamente l'elicottero quando si inclina all'indietro per livellarsi quando si vola in modalita' Horizon"
      },
      ["filters"] = {
        ["filter_type"] = "Tipo Filtro",
        ["help_p4"] = "Filtro Notch Dinamico: Crea automaticamente un filtro notch tra il min e il massimo del range di frequenza.",
        ["notch_c"] = "Conto Tacche/notch",
        ["rpm_preset"] = "Tipo",
        ["lowpass_1"] = "Passabasso 1",
        ["rpm_min_hz"] = "Min. Frequenza",
        ["help_p2"] = "Passabasso Gyro: i filtri passabasso per il segnale gyro. normalmente lasciati default.",
        ["cutoff"] = "Cutoff",
        ["notch_1"] = "Notch 1",
        ["max_cutoff"] = "Max cutoff",
        ["help_p3"] = "Filtro Notch Gyro: Utilizzare per filtrare specifiche gamme di frequenza. In genere non necessario nella maggior parte degli elicotteri.",
        ["lowpass_2"] = "Passabasso 2",
        ["rpm_filter"] = "Filtro RPM",
        ["help_p1"] = "In genere non si modificerebbe questa pagina senza controllare i registri della scatola nera!",
        ["dyn_notch"] = "Filtro Dinamico",
        ["notch_q"] = "Notch Q",
        ["lowpass_1_dyn"] = "Passabasso 1 dyn.",
        ["notch_min_hz"] = "Min",
        ["notch_max_hz"] = "Max",
        ["notch_2"] = "Notch 2",
        ["name"] = "Filtri",
        ["min_cutoff"] = "Min cutoff",
        ["center"] = "Centro"
      },
      ["status"] = {
        ["arming_disable_flag_3"] = "Recupero RX Fallata",
        ["arming_disable_flag_20"] = "Filtro RPM",
        ["arming_disable_flag_11"] = "Carica",
        ["arming_disable_flag_22"] = "DSHOT Bitbang",
        ["dataflash_free_space"] = "Spazio Libero Dataflash",
        ["arming_disable_flag_25"] = "Int. Armo",
        ["erasing"] = "Cancellazione",
        ["arming_disable_flag_9"] = "Boot Grace Time",
        ["megabyte"] = "MB",
        ["arming_disable_flag_17"] = "Paralizza",
        ["arming_disable_flag_5"] = "Governor",
        ["arming_disable_flag_8"] = "Angle",
        ["arming_disable_flag_1"] = "Fail Safe",
        ["cpu_load"] = "Carico CPU",
        ["arming_disable_flag_15"] = "BST",
        ["arming_disable_flag_12"] = "Calibrazione",
        ["arming_disable_flag_19"] = "Resc",
        ["arming_disable_flag_4"] = "Box Fail Safe",
        ["arming_disable_flag_24"] = "Protocollo Motore",
        ["real_time_load"] = "Caricamento in Tempo Reale",
        ["help_p2"] = "Per cancellare il dataflash e avere piu' spazio per i file di registro, premere il pulsante sul menu contrassegnato da un '*'.",
        ["arming_disable_flag_2"] = "RX Fail Safe",
        ["ok"] = "OK",
        ["arming_disable_flag_0"] = "No Gyro",
        ["arming_disable_flag_18"] = "GPS",
        ["help_p1"] = "Utilizza questa pagina per visualizzare lo stato attuale del tuo controller di volo. Questo puo' essere utile per determinare perché il tuo elicottero non si arma.",
        ["arming_flags"] = "Flags di Armo",
        ["unsupported"] = "Non supportato",
        ["erase_prompt"] = "Vuoi Cancellare la dataflash?",
        ["erase"] = "Cancella",
        ["arming_disable_flag_10"] = "No Pre Arm",
        ["arming_disable_flag_21"] = "Reboot Richiesto",
        ["name"] = "Stato",
        ["arming_disable_flag_13"] = "CLI",
        ["arming_disable_flag_14"] = "CMS Menu",
        ["arming_disable_flag_16"] = "MSP",
        ["arming_disable_flag_7"] = "Gas-Accelleratore",
        ["erasing_dataflash"] = "Cancello dataflash...",
        ["arming_disable_flag_23"] = "Calibrazione Acc"
      },
      ["profile_pidbandwidth"] = {
        ["help_p1"] = "Banda PID: Larghezza di banda complessiva in HZ utilizzata dal circuito PID",
        ["pitch"] = "P",
        ["yaw"] = "Y",
        ["name"] = "PID Bandwidth",
        ["bterm_cutoff"] = "B-term cut-off",
        ["help_p3"] = "B-term cutoff: Frequenza B-term cutoff in HZ.",
        ["dterm_cutoff"] = "D-term cut-off",
        ["help_p2"] = "D-term cutoff: Frequenza D-term cutoff in HZ.",
        ["roll"] = "R"
      }
    },
    ["navigation_save"] = "SALVA",
    ["menu_section_flight_tuning"] = "Tuning Volo",
    ["error_timed_out"] = "Errore: fuori tempo max",
    ["check_rf_module_on"] = "Prego accertarsi di aver acceso il modulo rf.",
    ["msg_saving"] = "Salva...",
    ["msg_save_not_commited"] = "Salva non commisionato alla EEPROM",
    ["menu_section_advanced"] = "Avanzato",
    ["msg_loading_from_fbl"] = "Carico dati dal flight controller...",
    ["msg_reload_settings"] = "Rciarico dati dal flight controller?",
    ["menu_section_tools"] = "Strumenti",
    ["msg_connecting"] = "Connessione in corso",
    ["msg_save_current_page"] = "Salva la pagina attuale nel flight controller?",
    ["btn_ok_long"] = "                OK                ",
    ["check_discovered_sensors"] = "Assicurati di aver trovato tutti i sensori.",
    ["msg_loading"] = "Carico...",
    ["check_heli_on"] = "Assicurati che la radiosia connessa e l'elicottero sia acceso.",
    ["check_bg_task"] = "Abilita i Task Background per favore.",
    ["navigation_tools"] = "*",
    ["check_supported_version"] = "Questa Versione del Lua Script \nnon puo' essere usata con il modello selezionato"
  },
  ["telemetry"] = {
    ["sensors"] = {
      ["attpitch"] = "P.angle",
      ["attroll"] = "R.angle",
      ["attyaw"] = "Y.angle",
      ["accx"] = "Accel X",
      ["accy"] = "Accel Z",
      ["accz"] = "Accel Z",
      ["groundspeed"] = "Velocità Suolo",
      ["esc_temp"] = "Temperatura ESC",
      ["rate_profile"] = "Valori Profilo",
      ["headspeed"] = "Velocita' Rotore",
      ["altitude"] = "Altitudine",
      ["voltage"] = "Voltaggio",
      ["bec_voltage"] = "Voltaggio Bec",
      ["cell_count"] = "Numero Celle",
      ["governor"] = "Stato Governor",
      ["adj_func"] = "Adj (Funzione)",
      ["fuel"] = "Carica Livello",
      ["smartfuel"] = "Smart Fuel",
      ["rssi"] = "RSSI",
      ["link"] = "Qualità Collegamento",
      ["adj_val"] = "Adj (Valore)",
      ["arming_flags"] = "Flags Armo",
      ["current"] = "Corrente",
      ["throttle_pct"] = "Acceleratore %",
      ["consumption"] = "Consumo",
      ["smartconsumption"] = "Consumo Intelligente",
      ["pid_profile"] = "Profilo PID",
      ["mcu_temp"] = "Temperatura MCU",
      ["armdisableflags"] = "Disattivazione Armo"
    }
  },
  ["widgets"] = {
    ["bbl"] = {
      ["erase_dataflash"] = "Cancella memoria flash",
      ["erasing"] = "Cancellazione in corso...",
      ["display"] = "Schermo",
      ["display_free"] = "Libero",
      ["display_used"] = "Usato",
      ["display_outof"] = "Usato/Totale"
    },
    ["craftimage"] = {
    },
    ["craftname"] = {
      ["txt_enter_craft_name"] = "Nome Velivolo",
      ["title"] = "NOME VELIVOLO",
      ["txt_cancel"] = "Cancella",
      ["txt_save"] = "Salva"
    },
    ["dashboard"] = {
      ["theme_load_error"] = "Il Tuo tema non si è caricato correttamente. Ritorno al tema di default.",
      ["validate_sensors"] = "SENSORI NECESSARI MANCANTI",
      ["unsupported_resolution"] = "TROPPO PICCOLO",
      ["loading"] = "ROTORFLIGHT",
      ["waiting_for_connection"] = "CONNECTING",
      ["check_bg_task"] = "BG TASK",
      ["check_rf_module_on"] = "MODULO RF",
      ["check_discovered_sensors"] = "SENSORI",
      ["no_link"] = "NO LINK",
      ["reset_flight"] = "Reset VOLO",
      ["reset_flight_ask_title"] = "Reset VOLO",
      ["reset_flight_ask_text"] = "Sei sicuro di resettare il Volo?",
      ["voltage"] = "Voltaggio",
      ["fuel"] = "Fuel",
      ["headspeed"] = "Headspeed",
      ["max"] = "Max",
      ["min"] = "Min",
      ["bec_voltage"] = "Voltaggio BEC",
      ["esc_temp"] = "ESC Temp",
      ["flight_duration"] = "Durata Volo",
      ["total_flight_duration"] = "Durata Totale Volo del modello",
      ["rpm_min"] = "RPM Min",
      ["rpm_max"] = "RPM Max",
      ["throttle_max"] = "Throttle Max",
      ["current_max"] = "Corrente Max",
      ["esc_max_temp"] = "ESC Temp Max",
      ["watts_max"] = "Max Watts",
      ["consumed_mah"] = "mAh Consumati",
      ["fuel_remaining"] = "Carburante Rimanente",
      ["min_volts_cell"] = "Min Volts per cella",
      ["link_min"] = "Link Min",
      ["governor"] = "Governor",
      ["profile"] = "Profilo",
      ["rates"] = "Escursioni",
      ["flights"] = "Voli",
      ["lq"] = "LQ",
      ["time"] = "Tempo",
      ["blackbox"] = "Blackbox",
      ["throttle"] = "Throttle",
      ["flight_time"] = "Tempo Volo",
      ["rssi_min"] = "RSSI Min",
      ["current"] = "Corrente",
      ["timer"] = "Timer",
      ["rpm"] = "RPM",
      ["min_voltage"] = "Min Voltaggio",
      ["max_voltage"] = "Max Voltaggio",
      ["min_current"] = "Min Corrente",
      ["max_current"] = "Max Corrente",
      ["max_tmcu"] = "Max T.MCU",
      ["max_emcu"] = "Max E.MCU",
      ["altitude"] = "Altitudine",
      ["altitude_max"] = "Altitudine Max",
      ["power"] = "Power",
      ["cell_voltage"] = "Voltaggio Cella",
      ["volts_per_cell"] = "Volts per cella",
      ["warning"] = "Attenzione",
      ["tx_batt"] = "TX Battery",
      ["link_max"] = "Link Max"
    },
    ["governor"] = {
      ["UNKNOWN"] = "SCONOSCIUTO",
      ["IDLE"] = "MINIMO",
      ["DISARMED"] = "DISARMATO",
      ["OFF"] = "OFF",
      ["SPOOLUP"] = "SPOOLUP",
      ["ACTIVE"] = "ATTIVO",
      ["RECOVERY"] = "RECUPERA",
      ["THROFF"] = "THR-OFF",
      ["LOSTHS"] = "HS-PERSO",
      ["AUTOROT"] = "AUTOROTAZIONE",
      ["DISABLED"] = "DISABILITA",
      ["BAILOUT"] = "SALVATAGGIO"
    }
  }
}
