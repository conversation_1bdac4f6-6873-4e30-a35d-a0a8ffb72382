--[[
 * Copyright (C) Rotorflight Project
 *
 * License GPLv3: https://www.gnu.org/licenses/gpl-3.0.en.html
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * Please do not edit these files directly. These files are generated
 * from JSON files contained within the folder:
 *
 *   bin/i18n/json/*.json
 *
 * Running the script:
 *
 *   lua54 json-to-lua.lua
 *
 * will create these files for you.
]]

return {
  ["reload"] = "reload",
  ["image"] = "image",
  ["error"] = "error",
  ["save"] = "save",
  ["ethos"] = "ethos",
  ["version"] = "version",
  ["bg_task_disabled"] = "bg task disabled",
  ["no_link"] = "no link",
  ["background_task_disabled"] = "background task disabled",
  ["no_sensor"] = "no sensor",
  ["api"] = {
    ["RC_TUNING"] = {
      ["setpoint_boost_cutoff_2"] = "Boost cutoff for the setpoint.",
      ["response_time_3"] = "Increase or decrease the response time of the rate to smooth heli movements.",
      ["accel_limit_4"] = "Maximum acceleration of the craft in response to a stick movement.",
      ["setpoint_boost_gain_4"] = "Boost gain for the setpoint.",
      ["yaw_dynamic_deadband_filter"] = "The maximum filter applied to the yaw dynamic deadband.",
      ["setpoint_boost_gain_3"] = "Boost gain for the setpoint.",
      ["response_time_2"] = "Increase or decrease the response time of the rate to smooth heli movements.",
      ["accel_limit_1"] = "Maximum acceleration of the craft in response to a stick movement.",
      ["setpoint_boost_cutoff_1"] = "Boost cutoff for the setpoint.",
      ["setpoint_boost_cutoff_4"] = "Boost cutoff for the setpoint.",
      ["setpoint_boost_gain_2"] = "Boost gain for the setpoint.",
      ["accel_limit_2"] = "Maximum acceleration of the craft in response to a stick movement.",
      ["yaw_dynamic_deadband_gain"] = "The maximum gain applied to the yaw dynamic deadband.",
      ["accel_limit_3"] = "Maximum acceleration of the craft in response to a stick movement.",
      ["response_time_4"] = "Increase or decrease the response time of the rate to smooth heli movements.",
      ["setpoint_boost_cutoff_3"] = "Boost cutoff for the setpoint.",
      ["setpoint_boost_gain_1"] = "Boost gain for the setpoint.",
      ["yaw_dynamic_ceiling_gain"] = "The maximum gain applied to the yaw dynamic ceiling.",
      ["response_time_1"] = "Increase or decrease the response time of the rate to smooth heli movements."
    },
    ["RESCUE_PROFILE"] = {
      ["rescue_flip_gain"] = "Determine how aggressively the heli flips during inverted rescue.",
      ["rescue_level_gain"] = "Determine how aggressively the heli levels during rescue.",
      ["tbl_off"] = "OFF",
      ["rescue_hover_collective"] = "Collective value for hover.",
      ["rescue_max_setpoint_rate"] = "Limit rescue roll/pitch rate. Larger helicopters may need slower rotation rates.",
      ["tbl_flip"] = "FLIP",
      ["rescue_flip_mode"] = "If rescue is activated while inverted, flip to upright - or remain inverted.",
      ["rescue_pull_up_time"] = "When rescue is activated, helicopter will apply pull-up collective for this time period before moving to flip or climb stage.",
      ["tbl_noflip"] = "NO FLIP",
      ["rescue_exit_time"] = "This limits rapid application of negative collective if the helicopter has rolled during rescue.",
      ["rescue_pull_up_collective"] = "Collective value for pull-up climb.",
      ["rescue_max_setpoint_accel"] = "Limit how fast the helicopter accelerates into a roll/pitch. Larger helicopters may need slower acceleration.",
      ["tbl_on"] = "ON",
      ["rescue_climb_collective"] = "Collective value for rescue climb.",
      ["rescue_flip_time"] = "If the helicopter is in rescue and is trying to flip to upright and does not within this time, rescue will be aborted.",
      ["rescue_climb_time"] = "Length of time the climb collective is applied before switching to hover."
    },
    ["ESC_SENSOR_CONFIG"] = {
      ["hw4_current_offset"] = "Hobbywing v4 current offset adjustment",
      ["tbl_off"] = "Off",
      ["update_hz"] = "ESC telemetry update rate",
      ["half_duplex"] = "Half duplex mode for ESC telemetry",
      ["consumption_correction"] = "Adjust the consumption correction",
      ["current_offset"] = "Current sensor offset adjustment",
      ["voltage_correction"] = "Adjust the voltage correction",
      ["hw4_voltage_gain"] = "Hobbywing v4 voltage gain adjustment",
      ["tbl_on"] = "On",
      ["hw4_current_gain"] = "Hobbywing v4 current gain adjustment",
      ["current_correction"] = "Adjust current correction",
      ["pin_swap"] = "Swap the TX and RX pins for the ESC telemetry"
    },
    ["ESC_PARAMETERS_FLYROTOR"] = {
      ["tbl_alwayson"] = "Always On",
      ["throttle_min"] = "Minimum throttle value",
      ["tbl_disabled"] = "Disabled",
      ["tbl_auto"] = "Auto",
      ["starting_torque"] = "Starting torque for the motor",
      ["cell_count"] = "Number of cells in the battery",
      ["motor_erpm_max"] = "Maximum RPM",
      ["throttle_max"] = "Maximum throttle value",
      ["tbl_ccw"] = "CCW",
      ["tbl_escgov"] = "Esc Governor",
      ["temperature_protection"] = "Temperature at which we cut power by 50%",
      ["tbl_automatic"] = "Automatic",
      ["low_voltage_protection"] = "Voltage at which we cut power by 50%",
      ["tbl_cw"] = "CW",
      ["soft_start"] = "Soft start value",
      ["gov_i"] = "Integral value for the governor",
      ["timing_angle"] = "Timing angle for the motor",
      ["response_speed"] = "Response speed for the motor",
      ["current_gain"] = "Gain value for the current sensor",
      ["tbl_extgov"] = "External Governor",
      ["buzzer_volume"] = "Buzzer volume",
      ["gov_d"] = "Derivative value for the governor",
      ["tbl_enabled"] = "Enabled",
      ["gov_p"] = "Proportional value for the governor"
    },
    ["ESC_PARAMETERS_YGE"] = {
      ["tbl_alwayson"] = "Always On",
      ["tbl_off"] = "Off",
      ["tbl_modestore"] = "Heli Governor Store",
      ["tbl_modefree"] = "Free (Attention!)",
      ["tbl_modeglider"] = "Aero Glider",
      ["tbl_modeext"] = "Heli Ext Governor",
      ["tbl_modeheli"] = "Heli Governor",
      ["tbl_medium"] = "Medium",
      ["tbl_autonorm"] = "Auto Normal",
      ["tbl_reverse"] = "Reverse",
      ["tbl_modef3a"] = "Aero F3A",
      ["tbl_auto"] = "Auto",
      ["tbl_slowdown"] = "Slowdown",
      ["tbl_slow"] = "Slow",
      ["tbl_modeair"] = "Aero Motor",
      ["tbl_normal"] = "Normal",
      ["tbl_on"] = "On",
      ["tbl_autoextreme"] = "Auto Extreme",
      ["tbl_autoefficient"] = "Auto Efficient",
      ["tbl_smooth"] = "Smooth",
      ["tbl_fast"] = "Fast",
      ["tbl_custom"] = "Custom (PC Defined)",
      ["tbl_cutoff"] = "Cutoff",
      ["tbl_autopower"] = "Auto Power",
      ["tbl_unused"] = "*Unused*"
    },
    ["GOVERNOR_PROFILE"] = {
      ["governor_tta_gain"] = "TTA gain applied to increase headspeed to control the tail in the negative direction (e.g. motorised tail less than idle speed).",
      ["governor_collective_ff_weight"] = "Collective precompensation weight - how much collective is mixed into the feedforward.",
      ["governor_i_gain"] = "PID loop I-term gain.",
      ["governor_cyclic_ff_weight"] = "Cyclic precompensation weight - how much cyclic is mixed into the feedforward.",
      ["governor_f_gain"] = "Feedforward gain.",
      ["governor_gain"] = "Master PID loop gain.",
      ["governor_headspeed"] = "Target headspeed for the current profile.",
      ["governor_min_throttle"] = "Minimum output throttle the governor is allowed to use.",
      ["governor_d_gain"] = "PID loop D-term gain.",
      ["governor_p_gain"] = "PID loop P-term gain.",
      ["governor_yaw_ff_weight"] = "Yaw precompensation weight - how much yaw is mixed into the feedforward.",
      ["governor_max_throttle"] = "Maximum output throttle the governor is allowed to use.",
      ["governor_tta_limit"] = "TTA max headspeed increase over full headspeed."
    },
    ["PID_PROFILE"] = {
      ["bterm_cutoff_2"] = "B-term cutoff in Hz.",
      ["dterm_cutoff_1"] = "D-term cutoff in Hz.",
      ["bterm_cutoff_1"] = "B-term cutoff in Hz.",
      ["gyro_cutoff_1"] = "PID loop overall bandwidth in Hz.",
      ["tbl_on"] = "ON",
      ["dterm_cutoff_2"] = "D-term cutoff in Hz.",
      ["yaw_inertia_precomp_cutoff"] = "Cutoff. Derivative cutoff frequency in 1/10Hz steps. Controls how sharp the precomp is. Higher value is sharper.",
      ["offset_limit_0"] = "Hard limit for the High Speed Integral offset angle in the PID loop. The O-term will never go over these limits.",
      ["cyclic_cross_coupling_ratio"] = "Amount of roll-to-pitch compensation needed, vs. pitch-to-roll.",
      ["yaw_precomp_cutoff"] = "Frequency limit for all yaw precompensation actions.",
      ["error_limit_0"] = "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.",
      ["trainer_gain"] = "Determines how aggressively the helicopter tilts back to the maximum angle (if exceeded) while in Acro Trainer Mode.",
      ["tbl_rpy"] = "RPY",
      ["gyro_cutoff_2"] = "PID loop overall bandwidth in Hz.",
      ["yaw_ccw_stop_gain"] = "Stop gain (PD) for counter-clockwise rotation.",
      ["trainer_angle_limit"] = "Limit the maximum angle the helicopter will pitch/roll to while in Acro Trainer Mode.",
      ["tbl_rp"] = "RP",
      ["yaw_cyclic_ff_gain"] = "Cyclic feedforward mixed into yaw (cyclic-to-yaw precomp).",
      ["error_decay_time_cyclic"] = "Time constant for bleeding off cyclic I-term. Higher will stabilize hover, lower will drift.",
      ["error_decay_limit_cyclic"] = "Maximum bleed-off speed for cyclic I-term.",
      ["cyclic_cross_coupling_gain"] = "Amount of compensation applied for pitch-to-roll decoupling.",
      ["yaw_collective_dynamic_decay"] = "Decay time for the extra yaw precomp on collective input.",
      ["pitch_collective_ff_gain"] = "Increasing will compensate for the pitching up motion caused by tail drag when climbing.",
      ["iterm_relax_type"] = "Choose the axes in which this is active. RP: Roll, Pitch. RPY: Roll, Pitch, Yaw.",
      ["offset_limit_1"] = "Hard limit for the High Speed Integral offset angle in the PID loop. The O-term will never go over these limits.",
      ["iterm_relax_cutoff_1"] = "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.",
      ["error_limit_1"] = "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.",
      ["horizon_level_strength"] = "Determines how aggressively the helicopter tilts back to level while in Horizon Mode.",
      ["error_limit_2"] = "Hard limit for the angle error in the PID loop. The absolute error and thus the I-term will never go above these limits.",
      ["iterm_relax_cutoff_2"] = "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.",
      ["tbl_off"] = "OFF",
      ["yaw_collective_ff_gain"] = "Collective feedforward mixed into yaw (collective-to-yaw precomp).",
      ["gyro_cutoff_0"] = "PID loop overall bandwidth in Hz.",
      ["yaw_collective_dynamic_gain"] = "An extra boost of yaw precomp on collective input.",
      ["cyclic_cross_coupling_cutoff"] = "Frequency limit for the compensation. Higher value will make the compensation action faster.",
      ["error_rotation"] = "Rotates the current roll and pitch error terms around yaw when the craft rotates. This is sometimes called Piro Compensation.",
      ["angle_level_limit"] = "Limit the maximum angle the helicopter will pitch/roll to while in Angle mode.",
      ["yaw_cw_stop_gain"] = "Stop gain (PD) for clockwise rotation.",
      ["iterm_relax_cutoff_0"] = "Helps reduce bounce back after fast stick movements. Can cause inconsistency in small stick movements if too low.",
      ["yaw_inertia_precomp_gain"] = "Scalar gain. The strength of the main rotor inertia. Higher value means more precomp is applied to yaw control.",
      ["dterm_cutoff_0"] = "D-term cutoff in Hz.",
      ["angle_level_strength"] = "Determines how aggressively the helicopter tilts back to level while in Angle Mode.",
      ["bterm_cutoff_0"] = "B-term cutoff in Hz.",
      ["error_decay_time_ground"] = "Bleeds off the current controller error when the craft is not airborne to stop the craft tipping over."
    },
    ["MIXER_CONFIG"] = {
      ["swash_trim_1"] = "Swash trim to level the swash plate when using fixed links.",
      ["tail_motor_idle"] = "Minimum throttle signal sent to the tail motor. This should be set just high enough that the motor does not stop.",
      ["tail_center_trim"] = "Sets tail rotor trim for 0 yaw for variable pitch, or tail motor throttle for 0 yaw for motorized.",
      ["tbl_cw"] = "CW",
      ["swash_tta_precomp"] = "Mixer precomp for 0 yaw.",
      ["swash_trim_2"] = "Swash trim to level the swash plate when using fixed links.",
      ["swash_geo_correction"] = "Adjust if there is too much negative collective or too much positive collective.",
      ["swash_trim_0"] = "Swash trim to level the swash plate when using fixed links.",
      ["swash_phase"] = "Phase offset for the swashplate controls.",
      ["collective_tilt_correction_pos"] = "Adjust the collective tilt correction scaling for positive collective pitch.",
      ["collective_tilt_correction_neg"] = "Adjust the collective tilt correction scaling for negative collective pitch.",
      ["tbl_ccw"] = "CCW",
      ["swash_pitch_limit"] = "Maximum amount of combined cyclic and collective blade pitch."
    },
    ["MOTOR_CONFIG"] = {
      ["minthrottle"] = "This PWM value is sent to the ESC/Servo at low throttle",
      ["motor_pwm_protocol"] = "The protocol used to communicate with the ESC",
      ["main_rotor_gear_ratio_0"] = "Motor Pinion Gear Tooth Count",
      ["maxthrottle"] = "This PWM value is sent to the ESC/Servo at full throttle",
      ["mincommand"] = "This PWM value is sent when the motor is stopped",
      ["main_rotor_gear_ratio_1"] = "Main Gear Tooth Count",
      ["tail_rotor_gear_ratio_1"] = "Autorotation Gear Tooth Count",
      ["motor_pwm_rate"] = "The frequency at which the ESC sends PWM signals to the motor",
      ["tail_rotor_gear_ratio_0"] = "Tail Gear Tooth Count",
      ["motor_pole_count_0"] = "The number of magnets on the motor bell."
    },
    ["ESC_PARAMETERS_HW5"] = {
      ["tbl_cw"] = "CW",
      ["tbl_fixedwing"] = "Fixed Wing",
      ["tbl_disabled"] = "Disabled",
      ["tbl_ccw"] = "CCW",
      ["tbl_heligov"] = "Heli Governor",
      ["tbl_softcutoff"] = "Soft Cutoff",
      ["tbl_proportional"] = "Proportional",
      ["tbl_heliext"] = "Heli Ext Governor",
      ["tbl_helistore"] = "Heli Governor Store",
      ["tbl_hardcutoff"] = "Hard Cutoff",
      ["tbl_normal"] = "Normal",
      ["tbl_autocalculate"] = "Auto Calculate",
      ["tbl_enabled"] = "Enabled",
      ["tbl_reverse"] = "Reverse"
    },
    ["PILOT_CONFIG"] = {
      ["model_param1_value"] = "Set this to the expected flight time in seconds.  The transmitter will beep when the flight time is reached."
    },
    ["BATTERY_INI"] = {
      ["calcfuel_local"] = "Calculate Fuel Using",
      ["tbl_off"] = "Current Sensor",
      ["tbl_on"] = "Voltage Sensor",
      ["sag_multiplier"] = "Raise or lower to adjust for the amount of voltage sag you see in flight.",
      ["kalman_multiplier"] = "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.",
      ["alert_off"] = "Off",
      ["alert_bec"] = "BEC",
      ["alert_rxbatt"] = "RxBatt"
    },
    ["BATTERY_CONFIG"] = {
      ["vbatmincellvoltage"] = "The minimum voltage per cell before the low voltage alarm is triggered.",
      ["vbatmaxcellvoltage"] = "The maximum voltage per cell before the high voltage alarm is triggered.",
      ["vbatwarningcellvoltage"] = "The voltage per cell at which the low voltage alarm will start to sound.",
      ["batteryCellCount"] = "The number of cells in your battery.",
      ["vbatfullcellvoltage"] = "The nominal voltage of a fully charged cell.",
      ["batteryCapacity"] = "The milliamp hour capacity of your battery."
    },
    ["ACC_TRIM"] = {
      ["pitch"] = "Use to trim if the heli drifts in one of the stabilized modes (angle, horizon, etc.).",
      ["roll"] = "Use to trim if the heli drifts in one of the stabilized modes (angle, horizon, etc.)."
    },
    ["PID_TUNING"] = {
      ["pid_1_I"] = "How tightly the system holds its position.",
      ["pid_2_P"] = "How tightly the system tracks the desired setpoint.",
      ["pid_2_I"] = "How tightly the system holds its position.",
      ["pid_1_O"] = "Used to prevent the craft from pitching when using high collective.",
      ["pid_1_F"] = "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.",
      ["pid_0_D"] = "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.",
      ["pid_1_P"] = "How tightly the system tracks the desired setpoint.",
      ["pid_0_I"] = "How tightly the system holds its position.",
      ["pid_2_B"] = "Additional boost on the feedforward to make the heli react more to quick stick movements.",
      ["pid_0_O"] = "Used to prevent the craft from rolling when using high collective.",
      ["pid_0_F"] = "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.",
      ["pid_2_F"] = "Helps push P-term based on stick input. Increasing will make response more sharp, but can cause overshoot.",
      ["pid_2_D"] = "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.",
      ["pid_0_P"] = "How tightly the system tracks the desired setpoint.",
      ["pid_1_D"] = "Strength of dampening to any motion on the system, including external influences. Also reduces overshoot.",
      ["pid_0_B"] = "Additional boost on the feedforward to make the heli react more to quick stick movements.",
      ["pid_1_B"] = "Additional boost on the feedforward to make the heli react more to quick stick movements."
    },
    ["GOVERNOR_CONFIG"] = {
      ["tbl_govmode_standard"] = "STANDARD",
      ["tbl_govmode_mode2"] = "MODE2",
      ["gov_tracking_time"] = "Time constant for headspeed changes, in seconds, measuring the time from zero to full headspeed.",
      ["tbl_govmode_passthrough"] = "PASSTHROUGH",
      ["tbl_govmode_mode1"] = "MODE1",
      ["gov_recovery_time"] = "Time constant for recovery spoolup, in seconds, measuring the time from zero to full headspeed.",
      ["gov_startup_time"] = "Time constant for slow startup, in seconds, measuring the time from zero to full headspeed.",
      ["gov_handover_throttle"] = "Governor activates above this %. Below this the input throttle is passed to the ESC.",
      ["gov_spoolup_time"] = "Time constant for slow spoolup, in seconds, measuring the time from zero to full headspeed.",
      ["gov_spoolup_min_throttle"] = "Minimum throttle to use for slow spoolup, in percent. For electric motors the default is 5%, for nitro this should be set so the clutch starts to engage for a smooth spoolup 10-15%.",
      ["tbl_govmode_off"] = "OFF"
    },
    ["RC_CONFIG"] = {
      ["rc_deflection"] = "Stick deflection from center in microseconds (us).",
      ["rc_min_throttle"] = "Minimum throttle (0% throttle output) expected from radio, in microseconds (us).",
      ["rc_max_throttle"] = "Maximum throttle (100% throttle output) expected from radio, in microseconds (us).",
      ["rc_arm_throttle"] = "Throttle must be at or below this value in microseconds (us) to allow arming. Must be at least 10us lower than minimum throttle.",
      ["rc_yaw_deadband"] = "Deadband for yaw control in microseconds (us).",
      ["rc_deadband"] = "Deadband for cyclic control in microseconds (us).",
      ["rc_center"] = "Stick center in microseconds (us)."
    },
    ["FILTER_CONFIG"] = {
      ["gyro_soft_notch_cutoff_2"] = "Width of the notch filter in Hz.",
      ["gyro_lpf1_static_hz"] = "Lowpass filter cutoff frequency in Hz.",
      ["tbl_none"] = "NONE",
      ["dyn_notch_max_hz"] = "Maximum frequency to which the notch is applied.",
      ["tbl_1st"] = "1ST",
      ["rpm_min_hz"] = "Minimum frequency for the RPM filter.",
      ["dyn_notch_min_hz"] = "Minimum frequency to which the notch is applied.",
      ["gyro_lpf1_dyn_max_hz"] = "Dynamic filter max cutoff in Hz.",
      ["gyro_soft_notch_hz_2"] = "Center frequency to which the notch is applied.",
      ["gyro_soft_notch_cutoff_1"] = "Width of the notch filter in Hz.",
      ["gyro_soft_notch_hz_1"] = "Center frequency to which the notch is applied.",
      ["dyn_notch_count"] = "Number of notches to apply.",
      ["dyn_notch_q"] = "Quality factor of the notch filter.",
      ["gyro_lpf2_static_hz"] = "Lowpass filter cutoff frequency in Hz.",
      ["gyro_lpf1_dyn_min_hz"] = "Dynamic filter min cutoff in Hz.",
      ["tbl_2nd"] = "2ND",
      ["tbl_custom"] = "CUSTOM",
      ["tbl_low"] = "LOW",
      ["tbl_medium"] = "MEDIUM",
      ["tbl_high"] = "HIGH"
    },
    ["ESC_PARAMETERS_XDFLY"] = {
      ["tbl_jadegreen"] = "JADE GREEN",
      ["tbl_off"] = "Off",
      ["tbl_low"] = "Low",
      ["tbl_orange"] = "ORANGE",
      ["tbl_fmfw"] = "Fixed Wing",
      ["tbl_ccw"] = "CCW",
      ["tbl_medium"] = "Medium",
      ["tbl_yellow"] = "YELLOW",
      ["tbl_reverse"] = "Reverse",
      ["tbl_red"] = "Red",
      ["tbl_high"] = "High",
      ["tbl_auto"] = "Auto",
      ["tbl_cw"] = "CW",
      ["tbl_fmheli"] = "Helicopter",
      ["tbl_purple"] = "PURPLE",
      ["tbl_green"] = "GREEN",
      ["tbl_blue"] = "BLUE",
      ["tbl_slow"] = "Slow",
      ["tbl_normal"] = "Normal",
      ["tbl_fast"] = "Fast",
      ["tbl_escgov"] = "ESC Governor",
      ["tbl_white"] = "WHITE",
      ["tbl_cyan"] = "CYAN",
      ["tbl_vslow"] = "Very Slow",
      ["tbl_extgov"] = "External Governor",
      ["tbl_pink"] = "PINK",
      ["tbl_fwgov"] = "Fixed Wing",
      ["tbl_on"] = "On"
    },
    ["ESC_PARAMETERS_SCORPION"] = {
      ["tbl_airplane"] = "Airplane mode",
      ["tbl_cw"] = "CW",
      ["tbl_off"] = "Off",
      ["tbl_quad"] = "Quad mode",
      ["tbl_heligov"] = "Heli Governor",
      ["tbl_exbus"] = "Jeti Exbus",
      ["tbl_boat"] = "Boat mode",
      ["tbl_unsolicited"] = "Unsolicited",
      ["tbl_futsbus"] = "Futaba SBUS",
      ["tbl_ccw"] = "CCW",
      ["tbl_helistore"] = "Heli Governor (stored)",
      ["tbl_standard"] = "Standard",
      ["tbl_on"] = "On",
      ["tbl_vbar"] = "VBar",
      ["tbl_vbargov"] = "VBar Governor",
      ["tbl_extgov"] = "External Governor"
    }
  },
  ["app"] = {
    ["btn_ok"] = "          OK           ",
    ["btn_close"] = "CLOSE",
    ["navigation_menu"] = "MENU",
    ["menu_section_hardware"] = "Hardware",
    ["msg_please_disarm_to_save_warning"] = "Settings will only be saved to eeprom on disarm",
    ["msg_saving_settings"] = "Saving settings...",
    ["msg_saving_to_fbl"] = "Saving data to flight controller...",
    ["navigation_reload"] = "RELOAD",
    ["menu_section_developer"] = "Developer",
    ["check_msp_version"] = "Unable to determine MSP version in use.",
    ["menu_section_about"] = "About",
    ["msg_please_disarm_to_save"] = "Please disarm to save",
    ["unit_hertz"] = "Hz",
    ["msg_rebooting"] = "Rebooting...",
    ["msg_save_settings"] = "Save settings",
    ["btn_cancel"] = "CANCEL",
    ["msg_connecting_to_fbl"] = "Connecting to flight controller...",
    ["navigation_help"] = "?",
    ["modules"] = {
      ["stats"] = {
        ["name"] = "Stats",
        ["totalflighttime"] = "Total Flight Time",
        ["flightcount"] = "Flight Count",
        ["lastflighttime"] = "Last Flight Time",
        ["help_p1"] = "Use this module to update the recorded flight statistics on the flight controller."
      },
      ["settings"] = {
        ["name"] = "Settings",
        ["no_themes_available_to_configure"] = "No configurable themes installed on this device",
        ["txt_audio_timer"] = "Timer",
        ["txt_audio_events"] = "Events",
        ["txt_audio_switches"] = "Switches",
        ["txt_iconsize"] = "Icon Size",
        ["txt_general"] = "General",
        ["txt_text"] = "TEXT",
        ["txt_small"] = "SMALL",
        ["txt_large"] = "LARGE",
        ["txt_syncname"] = "Sync model name",
        ["txt_devtools"] = "Developer Tools",
        ["txt_apiversion"] = "API Version",
        ["txt_logging"] = "Logging",
        ["txt_compilation"] = "Compilation",
        ["txt_loglocation"] = "Log location",
        ["txt_console"] = "CONSOLE",
        ["txt_consolefile"] = "CONSOLE & FILE",
        ["txt_loglevel"] = "Log level",
        ["txt_off"] = "OFF",
        ["txt_info"] = "INFO",
        ["txt_debug"] = "DEBUG",
        ["txt_mspdata"] = "Log msp data",
        ["txt_queuesize"] = "Log MSP queue size",
        ["txt_memusage"] = "Log memory usage",
        ["txt_batttype"] = "Tx Battery Options",
        ["txt_battdef"] = "Default",
        ["txt_batttext"] = "Text",
        ["txt_battdig"] = "Digital",
        ["dashboard"] = "Dashboard",
        ["dashboard_theme"] = "Theme",
        ["dashboard_theme_panel_global"] = "Default theme for all models",
        ["dashboard_theme_panel_model"] = "Optional theme for this model",
        ["dashboard_theme_panel_model_disabled"] = "Disabled",
        ["dashboard_settings"] = "Settings",
        ["dashboard_theme_preflight"] = "Preflight Theme",
        ["dashboard_theme_inflight"] = "Inflight Theme",
        ["dashboard_theme_postflight"] = "Postflight Theme",
        ["audio"] = "Audio",
        ["localizations"] = "Localization",
        ["txt_development"] = "Development",
        ["temperature_unit"] = "Temperature Unit",
        ["altitude_unit"] = "Altitude Unit",
        ["celcius"] = "Celsius",
        ["fahrenheit"] = "Fahrenheit",
        ["meters"] = "Meters",
        ["feet"] = "Feet",
        ["warning"] = "Warning",
        ["governor_state"] = "Governor State",
        ["arming_flags"] = "Arming Flags",
        ["voltage"] = "Voltage",
        ["pid_rates_profile"] = "PID/Rates Profile",
        ["pid_profile"] = "PID Profile",
        ["rate_profile"] = "Rate Profile",
        ["esc_temperature"] = "ESC Temperature",
        ["esc_threshold"] = "Threshold (°)",
        ["bec_voltage"] = "BEC Voltage",
        ["bec_threshold"] = "Threshold (V)",
        ["fuel"] = "Fuel",
        ["fuel_callout_default"] = "Default (Only at 10%)",
        ["fuel_callout_10"] = "Every 10%",
        ["fuel_callout_20"] = "Every 20%",
        ["fuel_callout_25"] = "Every 25%",
        ["fuel_callout_50"] = "Every 50%",
        ["fuel_callout_percent"] = "Callout %",
        ["fuel_repeats_below"] = "Repeats below 0%",
        ["fuel_haptic_below"] = "Haptic below 0%",
        ["timer_alerting"] = "Timer Alerting",
        ["timer_elapsed_alert_mode"] = "Timer Elapsed Alert",
        ["timer_prealert_options"] = "Pre-timer Alert Options",
        ["timer_prealert"] = "Pre-timer Alert",
        ["timer_alert_period"] = "Alert Period",
        ["timer_postalert_options"] = "Post-timer Alert Options",
        ["timer_postalert"] = "Post-timer Alert",
        ["timer_postalert_period"] = "Alert Period",
        ["timer_postalert_interval"] = "Alert Interval"
      },
      ["validate_sensors"] = {
        ["help_p1"] = "This tool attempts to list all the sensors that you are not receiving in a concise list.",
        ["invalid"] = "INVALID",
        ["name"] = "Sensors",
        ["msg_repair"] = "Enable required sensors on flight controller?",
        ["msg_repair_fin"] = "The flight controller has been configured? You may need to perform a discover sensors to see the changes.",
        ["ok"] = "OK",
        ["help_p2"] = "Use this tool to ensure you are sending the correct sensors."
      },
      ["msp_exp"] = {
        ["help_p1"] = "This tool provides the ability to send a custom byte string to the flight controller. It is useful for developers when debugging values.",
        ["name"] = "MSP Expermental",
        ["help_p2"] = "If you do not understand what you are doing, do not use it as bad things can happen."
      },
      ["esc_tools"] = {
        ["unknown"] = "UNKNOWN",
        ["name"] = "ESC Tools",
        ["please_powercycle"] = "Please power cycle the ESC...",
        ["mfg"] = {
          ["hw5"] = {
            ["esc"] = "ESC",
            ["brake_force"] = "Brake Force%",
            ["rotation"] = "Rotation",
            ["soft_start"] = "Soft Start",
            ["name"] = "Hobbywing V5",
            ["limits"] = "Limits",
            ["bec_voltage"] = "BEC Voltage",
            ["gov_i_gain"] = "I-Gain",
            ["startup_time"] = "Startup Time",
            ["lipo_cell_count"] = "LiPo Cell Count",
            ["restart_time"] = "Restart Time",
            ["volt_cutoff_type"] = "Volt Cutoff Type",
            ["motor"] = "Motor",
            ["brake_type"] = "Brake Type",
            ["brake"] = "Brake",
            ["governor"] = "Governor",
            ["advanced"] = "Advanced",
            ["basic"] = "Basic",
            ["flight_mode"] = "Flight Mode",
            ["auto_restart"] = "Auto Restart",
            ["active_freewheel"] = "Active Freewheel",
            ["cutoff_voltage"] = "Cutoff Voltage",
            ["startup_power"] = "Startup Power",
            ["other"] = "Other",
            ["timing"] = "Timing",
            ["gov_p_gain"] = "P-Gain"
          },
          ["xdfly"] = {
            ["hv_bec_voltage"] = "HV BEC Voltage",
            ["gov"] = "Governor",
            ["brake_force"] = "Brake Force",
            ["sr_function"] = "SR Function",
            ["name"] = "XDFLY",
            ["lv_bec_voltage"] = "LV BEC Voltage",
            ["auto_restart_time"] = "Auto Restart Time",
            ["acceleration"] = "Acceleration",
            ["motor_direction"] = "Motor Direction",
            ["smart_fan"] = "Smart Fan",
            ["governor"] = "Governor",
            ["advanced"] = "Advanced",
            ["gov_i"] = "Gov-I",
            ["cell_cutoff"] = "Cell Cutoff",
            ["led_color"] = "LED Color",
            ["basic"] = "Basic",
            ["startup_power"] = "Startup Power",
            ["motor_poles"] = "Motor Poles",
            ["capacity_correction"] = "Capacity Correction",
            ["timing"] = "Timing",
            ["gov_p"] = "Gov-P"
          },
          ["flrtr"] = {
            ["gov"] = "Governor",
            ["motor_temp_sensor"] = "Motor temp sensor",
            ["starting_torque"] = "Starting torque",
            ["cell_count"] = "Cell count",
            ["gov_p"] = "Gov-P",
            ["motor_erpm_max"] = "Motor ERPM max",
            ["name"] = "FLYROTOR",
            ["low_voltage_protection"] = "Low voltage protection",
            ["gov_d"] = "Gov-D",
            ["telemetry_protocol"] = "Telemetry protocol",
            ["motor_direction"] = "Motor direction",
            ["throttle_protocol"] = "Throttle protocol",
            ["soft_start"] = "Soft start",
            ["other"] = "Other",
            ["temperature_protection"] = "Temperature protection",
            ["buzzer_volume"] = "Buzzer volume",
            ["timing_angle"] = "Timing angle",
            ["governor"] = "Governor",
            ["advanced"] = "Advanced",
            ["gov_i"] = "Gov-I",
            ["bec_voltage"] = "BEC voltage",
            ["fan_control"] = "Fan control",
            ["basic"] = "Basic",
            ["current_gain"] = "Current gain",
            ["led_color"] = "LED color",
            ["motor_temp"] = "Motor temperture",
            ["response_speed"] = "Response speed",
            ["battery_capacity"] = "Battery capacity"
          },
          ["scorp"] = {
            ["esc_mode"] = "ESC Mode",
            ["min_voltage"] = "Min Voltage",
            ["rotation"] = "Rotation",
            ["telemetry_protocol"] = "Telemetry Protocol",
            ["name"] = "Scorpion",
            ["runup_time"] = "Runup Time",
            ["motor_startup_sound"] = "Motor Startup Sound",
            ["gov_integral"] = "Gov Integral",
            ["gov_proportional"] = "Gov Proportional",
            ["cutoff_handling"] = "Cutoff Handling",
            ["bailout"] = "Bailout",
            ["limits"] = "Limits",
            ["soft_start_time"] = "Soft Start Time",
            ["advanced"] = "Advanced",
            ["bec_voltage"] = "BEC Voltage",
            ["extra_msg_save"] = "Please reboot the ESC to apply the changes",
            ["basic"] = "Basic",
            ["max_current"] = "Max Current",
            ["max_temperature"] = "Max Temperature",
            ["protection_delay"] = "Protection Delay",
            ["max_used"] = "Max Used"
          },
          ["yge"] = {
            ["esc_mode"] = "ESC Mode",
            ["esc"] = "ESC",
            ["current_limit"] = "Current Limit",
            ["f3c_auto"] = "F3C Autorotation",
            ["name"] = "YGE",
            ["max_start_power"] = "Max Start Power",
            ["lv_bec_voltage"] = "BEC",
            ["pinion_teeth"] = "Pinion Teeth",
            ["auto_restart_time"] = "Auto Restart Time",
            ["main_teeth"] = "Main Teeth",
            ["other"] = "Other",
            ["limits"] = "Limits",
            ["cell_cutoff"] = "Cell Cutoff",
            ["throttle_response"] = "Throttle Response",
            ["stick_zero_us"] = "Stick Zero",
            ["advanced"] = "Advanced",
            ["gov_i"] = "Gov-I",
            ["motor_pole_pairs"] = "Motor Pole Pairs",
            ["stick_range_us"] = "Stick Range",
            ["basic"] = "Basic",
            ["min_start_power"] = "Min Start Power",
            ["active_freewheel"] = "Active Freewheel",
            ["direction"] = "Direction",
            ["timing"] = "Motor Timing",
            ["gov_p"] = "Gov-P"
          }
        },
        ["searching"] = "Searching"
      },
      ["pids"] = {
        ["help_p1"] = "FeedForward (Roll/Pitch): Start at 70, increase until stops are sharp with no drift. Keep roll and pitch equal.",
        ["o"] = "O",
        ["pitch"] = "Pitch",
        ["i"] = "I",
        ["yaw"] = "Yaw",
        ["roll"] = "Roll",
        ["help_p5"] = "Test & Adjust: Fly, observe, and fine-tune for best performance in real conditions.",
        ["p"] = "P",
        ["f"] = "F",
        ["name"] = "PIDs",
        ["help_p2"] = "I Gain (Roll/Pitch): Raise gradually for stable piro pitch pumps. Too high causes wobbles; match roll/pitch values.",
        ["d"] = "D",
        ["b"] = "B",
        ["help_p4"] = "Tail Stop Gain (CW/CCW): Adjust separately for clean, bounce-free stops in both directions.",
        ["help_p3"] = "Tail P/I/D Gains: Increase P until slight wobble in funnels, then back off slightly. Raise I until tail holds firm in hard moves (too high causes slow wag). Adjust D for smooth stops—higher for slow servos, lower for fast ones."
      },
      ["msp_speed"] = {
        ["seconds_600"] = "  600S  ",
        ["avg_query_time"] = "Average query time",
        ["seconds_30"] = "  30S  ",
        ["name"] = "MSP Speed",
        ["max_query_time"] = "Maximum query time",
        ["help_p1"] = "This tool attempts to determine the quality of your MSP data link by performing as many large MSP queries within 30 seconds as possible.",
        ["retries"] = "Retries",
        ["checksum_errors"] = "Checksum errors",
        ["test_length"] = "Test length",
        ["start"] = "Start",
        ["memory_free"] = "Memory free",
        ["start_prompt"] = "Would you like to start the test? Choose the test run time below.",
        ["rf_protocol"] = "RF protocol",
        ["min_query_time"] = "Minimum query time",
        ["seconds_120"] = "  120S  ",
        ["seconds_300"] = "  300S  ",
        ["testing"] = "Testing",
        ["successful_queries"] = "Successful queries",
        ["timeouts"] = "Timeouts",
        ["testing_performance"] = "Testing MSP performance...",
        ["total_queries"] = "Total queries"
      },
      ["copyprofiles"] = {
        ["profile_type"] = "Profile Type",
        ["profile_type_pid"] = "PID",
        ["profile_type_rate"] = "Rate",
        ["msgbox_save"] = "Save settings",
        ["name"] = "Copy Profiles",
        ["help_p1"] = "Copy PID profile or Rate profile from Source to Destination.",
        ["dest_profile"] = "Dest. Profile",
        ["source_profile"] = "Source Profile",
        ["msgbox_msg"] = "Save current page to flight controller?",
        ["help_p2"] = "Choose the source and destinations and save to copy the profile."
      },
      ["esc_motors"] = {
        ["min_throttle"] = "0% Throttle PWM Value",
        ["tail_motor_ratio"] = "Tail Motor Ratio",
        ["max_throttle"] = "100% Throttle PWM value",
        ["main_motor_ratio"] = "Main Motor Ratio",
        ["pinion"] = "Pinion",
        ["main"] = "Main",
        ["help_p1"] = "Configure the motor and speed controller features.",
        ["rear"] = "Rear",
        ["front"] = "Front",
        ["voltage_correction"] = "Voltage Correction",
        ["mincommand"] = "Motor Stop PWM Value",
        ["name"] = "ESC/Motors",
        ["motor_pole_count"] = "Motor Pole Count",
        ["current_correction"] = "Current Correction",
        ["consumption_correction"] = "Consumption Correction"
      },
      ["radio_config"] = {
        ["deflection"] = "Deflection",
        ["max_throttle"] = "Max",
        ["stick"] = "Stick",
        ["arming"] = "Arming",
        ["yaw_deadband"] = "Yaw",
        ["cyclic"] = "Cyclic",
        ["name"] = "Radio Config",
        ["help_p1"] = "Configure your radio settings. Stick center, arm, throttle hold, and throttle cut.",
        ["min_throttle"] = "Min",
        ["throttle"] = "Throttle",
        ["deadband"] = "Deadband",
        ["center"] = "Center"
      },
      ["profile_select"] = {
        ["help_p1"] = "Set the current flight profile or rate profile you would like to use.",
        ["rate_profile"] = "Rate Profile",
        ["pid_profile"] = "PID profile",
        ["save_prompt"] = "Save current page to flight controller?",
        ["save_prompt_local"] = "Save current page to radio?",
        ["cancel"] = "CANCEL",
        ["name"] = "Select Profile",
        ["save_settings"] = "Save settings",
        ["ok"] = "OK",
        ["help_p2"] = "If you use a switch on your radio to change flight or rate modes, this will override this choice as soon as you toggle the switch."
      },
      ["profile_governor"] = {
        ["tail_torque_assist"] = "Tail Torque Assist",
        ["p"] = "P",
        ["i"] = "I",
        ["yaw"] = "Yaw",
        ["cyc"] = "Cyc",
        ["f"] = "F",
        ["name"] = "Governor",
        ["d"] = "D",
        ["help_p1"] = "Full headspeed: Headspeed target when at 100% throttle input.",
        ["help_p6"] = "Tail Torque Assist: For motorized tails. Gain and limit of headspeed increase when using main rotor torque for yaw assist.",
        ["help_p4"] = "Precomp: Governor precomp gain for yaw, cyclic, and collective inputs.",
        ["max_throttle"] = "Max throttle",
        ["full_headspeed"] = "Full headspeed",
        ["precomp"] = "Precomp",
        ["gain"] = "PID master gain",
        ["disabled_message"] = "Rotorflight governor is not enabled",
        ["help_p3"] = "Gains: Fine tuning of the governor.",
        ["col"] = "Col",
        ["min_throttle"] = "Min throttle",
        ["tta_limit"] = "Limit",
        ["help_p2"] = "PID master gain: How hard the governor works to hold the RPM.",
        ["gains"] = "Gains",
        ["help_p5"] = "Max throttle: The maximum throttle % the governor is allowed to use.",
        ["tta_gain"] = "Gain"
      },
      ["profile_tailrotor"] = {
        ["gain"] = "Gain",
        ["help_p4"] = "Collective FF Gain: Tail precompensation for collective inputs.",
        ["collective_impulse_ff"] = "Collective Impulse FF",
        ["help_p2"] = "Precomp Cutoff: Frequency limit for all yaw precompensation actions.",
        ["cutoff"] = "Cutoff",
        ["help_p3"] = "Cyclic FF Gain: Tail precompensation for cyclic inputs.",
        ["help_p1"] = "Yaw Stop Gain: Higher stop gain will make the tail stop more aggressively but may cause oscillations if too high. Adjust CW or CCW to make the yaw stops even.",
        ["inertia_precomp"] = "Inertia Precomp",
        ["cyclic_ff_gain"] = "Cyclic FF gain",
        ["help_p5"] = "Collective Impulse FF: Impulse tail precompensation for collective inputs. If you need extra tail precompensation at the beginning of collective input.",
        ["cw"] = "CW",
        ["ccw"] = "CCW",
        ["yaw_stop_gain"] = "Yaw stop gain",
        ["precomp_cutoff"] = "Precomp Cutoff",
        ["collective_ff_gain"] = "Collective FF gain",
        ["name"] = "Tail Rotor",
        ["decay"] = "Decay"
      },
      ["profile_pidcontroller"] = {
        ["help_p4"] = "Error rotation: Allow errors to be shared between all axes.",
        ["ground_error_decay"] = "Ground Error Decay",
        ["yaw"] = "Y",
        ["inflight_error_decay"] = "Inflight Error Decay",
        ["help_p2"] = "Error limit: Angle limit for I-term.",
        ["error_limit"] = "Error limit",
        ["help_p3"] = "Offset limit: Angle limit for High Speed Integral (O-term).",
        ["cutoff_point"] = "Cut-off point",
        ["limit"] = "Limit",
        ["iterm_relax"] = "I-term relax",
        ["hsi_offset_limit"] = "HSI Offset limit",
        ["pitch"] = "P",
        ["name"] = "PID Controller",
        ["error_rotation"] = "Error rotation",
        ["roll"] = "R",
        ["help_p5"] = "I-term relax: Limit accumulation of I-term during fast movements - helps reduce bounce back after fast stick movements. Generally needs to be lower for large helis and can be higher for small helis. Best to only reduce as much as is needed for your flying style.",
        ["time"] = "Time",
        ["help_p1"] = "Error decay ground: PID decay to help prevent heli from tipping over when on the ground."
      },
      ["logs"] = {
        ["help_logs_p2"] = "Note. To enable logging it is essential for you to have the following sensors enabled.",
        ["name"] = "Logs",
        ["help_logs_p1"] = "Please select a log file from the list below.",
        ["msg_no_logs_found"] = "NO LOG FILES FOUND",
        ["help_logs_tool_p1"] = "Please use the slider to navigate the graph.",
        ["help_logs_p3"] = "- arm status, voltage, headspeed, current, esc temperature"
      },
      ["battery"] = {
        ["calcfuel_local"] = "Calculate Fuel Using",
        ["max_cell_voltage"] = "Max Cell Voltage",
        ["full_cell_voltage"] = "Full Cell Voltage",
        ["name"] = "Battery",
        ["min_cell_voltage"] = "Min Cell Voltage",
        ["help_p1"] = "The battery settings are used to configure the flight controller to monitor the battery voltage and provide warnings when the voltage drops below a certain level.",
        ["battery_capacity"] = "Battery Capacity",
        ["warn_cell_voltage"] = "Warn Cell Voltage",
        ["cell_count"] = "Cell Count",
        ["consumption_warning_percentage"] = "Consumption Warning %",
        ["timer"] = "Flight Time Alarm",
        ["voltage_multiplier"] = "Sag Compensation",
        ["kalman_multiplier"] = "Filter Compensation",
        ["alert_type"] = "Rx Voltage Alert",
        ["bec_voltage_alert"] = "BEC Alert Value",
        ["rx_voltage_alert"] = "RxBatt Alert Value"
      },
      ["profile_mainrotor"] = {
        ["gain"] = "Gain",
        ["help_p4"] = "Cross Coupling Freq. Limit: Frequency limit for the compensation, higher value will make the compensation action faster.",
        ["collective_pitch_comp_short"] = "Col. Pitch Compensation",
        ["cyclic_cross_coupling"] = "Cyclic Cross coupling",
        ["collective_pitch_comp"] = "Collective Pitch Compensation",
        ["name"] = "Main Rotor",
        ["cutoff"] = "Cutoff",
        ["ratio"] = "Ratio",
        ["help_p1"] = "Collective Pitch Compensation: Increasing will compensate for the pitching motion caused by tail drag when climbing.",
        ["help_p2"] = "Cross Coupling Gain: Removes roll coupling when only elevator is applied.",
        ["help_p3"] = "Cross Coupling Ratio: Amount of compensation (pitch vs roll) to apply."
      },
      ["sbusout"] = {
        ["title"] = "SBUS Output",
        ["help_fields_source"] = "Source id for the mix, counting from 0-15.",
        ["help_default_p4"] = "- For motors, use 0, 1000.",
        ["ch_prefix"] = "CH",
        ["channel_prefix"] = "CHANNEL ",
        ["saving"] = "Saving",
        ["name"] = "SBUS Out",
        ["channel_page"] = "Sbus out / CH",
        ["receiver"] = "Receiver",
        ["servo"] = "Servo",
        ["type"] = "Type",
        ["saving_data"] = "Saving data...",
        ["help_fields_max"] = "The maximum pwm value to send",
        ["motor"] = "Motor",
        ["help_default_p5"] = "- Or you can customize your own mapping.",
        ["help_default_p1"] = "Configure advanced mixing and channel mapping if you have SBUS Out enabled on a serial port.",
        ["max"] = "Max",
        ["save_prompt"] = "Save current page to flight controller?",
        ["help_fields_min"] = "The minimum pwm value to send.",
        ["mixer"] = "Mixer",
        ["ok"] = "OK",
        ["cancel"] = "CANCEL",
        ["help_default_p2"] = "- For RX channels or servos (wideband), use 1000, 2000 or 500,1000 for narrow band servos.",
        ["save_settings"] = "Save settings",
        ["min"] = "Min",
        ["help_default_p3"] = "- For mixer rules, use -1000, 1000.",
        ["source"] = "Source"
      },
      ["profile_rescue"] = {
        ["help_p4"] = "Hover: How much collective to maintain a steady hover.",
        ["hover"] = "Hover",
        ["collective"] = "Collective",
        ["help_p2"] = "Pull-up: How much collective and for how long to arrest the fall.",
        ["climb"] = "Climb",
        ["mode_enable"] = "Rescue mode enable",
        ["help_p3"] = "Climb: How much collective to maintain a steady climb - and how long.",
        ["help_p1"] = "Flip to upright: Flip the heli upright when rescue is activated.",
        ["flip_upright"] = "Flip to upright",
        ["flip"] = "Flip",
        ["level_gain"] = "Level",
        ["name"] = "Rescue",
        ["exit_time"] = "Exit time",
        ["help_p5"] = "Flip: How long to wait before aborting because the flip did not work.",
        ["help_p6"] = "Gains: How hard to fight to keep heli level when engaging rescue mode.",
        ["fail_time"] = "Fail time",
        ["pull_up"] = "Pull-up",
        ["rate"] = "Rate",
        ["help_p7"] = "Rate and Accel: Max rotation and acceleration rates when leveling during rescue.",
        ["gains"] = "Gains",
        ["time"] = "Time",
        ["accel"] = "Accel"
      },
      ["trim"] = {
        ["disable_mixer_message"] = "Return control of the servos to the flight controller.",
        ["tail_motor_idle"] = "Tail Motor idle  %",
        ["disable_mixer_override"] = "Disable mixer override",
        ["yaw_trim"] = "Yaw. trim %",
        ["enable_mixer_message"] = "Set all servos to their configured center position. \r\n\r\nThis will result in all values on this page being saved when adjusting the servo trim.",
        ["mixer_override_disabling"] = "Disabling mixer override...",
        ["roll_trim"] = "Roll trim %",
        ["pitch_trim"] = "Pitch trim %",
        ["name"] = "Trim",
        ["help_p2"] = "Motorised tail: If using a motorised tail, use this to set the minimum idle speed and zero yaw.",
        ["mixer_override"] = "Mixer Override",
        ["mixer_override_enabling"] = "Enabling mixer override...",
        ["enable_mixer_override"] = "Enable mixer override",
        ["collective_trim"] = "Col. trim %",
        ["help_p1"] = "Link trims: Use to trim out small leveling issues in your swash plate. Typically only used if the swash links are non-adjustable."
      },
      ["governor"] = {
        ["help_p1"] = "These parameters apply globally to the governor regardless of the profile in use.",
        ["handover_throttle"] = "Handover throttle%",
        ["spoolup_min_throttle"] = "Spoolup min throttle%",
        ["recovery_time"] = "Recovery time",
        ["mode"] = "Mode",
        ["help_p2"] = "Each parameter is simply a time value in seconds for each governor action.",
        ["tracking_time"] = "Tracking time",
        ["name"] = "Governor",
        ["startup_time"] = "Startup time",
        ["spoolup_time"] = "Spoolup time"
      },
      ["accelerometer"] = {
        ["help_p1"] = "The accelerometer is used to measure the angle of the flight controller in relation to the horizon. This data is used to stabilize the aircraft and provide self-leveling functionality.",
        ["name"] = "Accelerometer",
        ["pitch"] = "Pitch",
        ["msg_calibrate"] = "Calibrate the accelerometer?",
        ["roll"] = "Roll"
      },
      ["gyro_alignment"] = {
        ["name"] = "Board Alignment",
        ["board_alignment"] = "Board Alignment",
        ["roll"] = "Roll",
        ["pitch"] = "Pitch",
        ["yaw"] = "Yaw",
        ["msg_calibrate"] = "Calibrate accelerometer? This will reset the accelerometer and apply current board alignment settings.",
        ["help_p1"] = "The board alignment tool allows you to configure the board alignment angles to compensate for flight controller mounting orientation.",
        ["help_p2"] = "Board Alignment: Adjust roll, pitch, and yaw angles (in degrees) to match your flight controller's physical orientation relative to the helicopter frame.",
        ["help_p3"] = "These settings compensate for cases where the flight controller is not mounted perfectly aligned with the helicopter's frame axes.",
        ["help_p4"] = "Use the Tool button to calibrate the accelerometer after making alignment changes. Save settings to EEPROM when complete."
      },
      ["rates"] = {
        ["help_table_5_p2"] = "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.",
        ["actual"] = "ACTUAL",
        ["max_rate"] = "Max Rate",
        ["help_table_4_p3"] = "Expo: Reduces sensitivity near the stick's center where fine controls are needed.",
        ["rate"] = "Rate",
        ["help_table_5_p1"] = "RC Rate: Use to reduce sensitivity around center stick. RC Rate set to one half of the Max Rate is linear. A lower number will reduce sensitivity around center stick. Higher than one half of the Max Rate will also increase the Max Rate.",
        ["help_table_4_p2"] = "Max Rate: Maximum rotation rate at full stick deflection in degrees per second.",
        ["center_sensitivity"] = "Cntr. Sens.",
        ["rc_curve"] = "RC Curve",
        ["roll"] = "Roll",
        ["none"] = "NONE",
        ["betaflight"] = "BETAFLIGHT",
        ["kiss"] = "KISS",
        ["help_table_1_p3"] = "Expo: Reduces sensitivity near the stick's center where fine controls are needed.",
        ["help_table_3_p2"] = "Rate: Increases maximum rotation rate while reducing sensitivity around half stick.",
        ["help_table_2_p2"] = "Acro+: Increases the maximum rotation rate while reducing sensitivity around half stick.",
        ["superrate"] = "SuperRate",
        ["help_table_2_p3"] = "Expo: Reduces sensitivity near the stick's center where fine controls are needed.",
        ["raceflight"] = "RACEFLIGHT",
        ["yaw"] = "Yaw",
        ["collective"] = "Col",
        ["name"] = "Rates",
        ["help_table_5_p3"] = "Expo: Reduces sensitivity near the stick's center where fine controls are needed.",
        ["help_table_3_p3"] = "RC Curve: Reduces sensitivity near the stick's center where fine controls are needed.",
        ["expo"] = "Expo",
        ["help_table_1_p2"] = "SuperRate: Increases maximum rotation rate while reducing sensitivity around half stick.",
        ["help_default_p2"] = "We will use the sub keys below.",
        ["help_default_p1"] = "Default: We keep this to make button appear for rates.",
        ["quick"] = "QUICK",
        ["pitch"] = "Pitch",
        ["acroplus"] = "Acro+",
        ["help_table_1_p1"] = "RC Rate: Maximum rotation rate at full stick deflection.",
        ["rc_rate"] = "RC Rate",
        ["help_table_2_p1"] = "Rate: Maximum rotation rate at full stick deflection in degrees per second.",
        ["help_table_4_p1"] = "Center Sensitivity: Use to reduce sensitivity around center stick. Set Center Sensitivity to the same as Max Rate for a linear response. A lower number than Max Rate will reduce sensitivity around center stick. Note that higher than Max Rate will increase the Max Rate - not recommended as it causes issues in the Blackbox log.",
        ["help_table_0_p1"] = "All values are set to zero because no RATE TABLE is in use.",
        ["help_table_3_p1"] = "RC Rate: Maximum rotation rate at full stick deflection."
      },
      ["mixer"] = {
        ["help_p1"] = "Adust swash plate geometry, phase angles, and limits.",
        ["collective_tilt_correction_pos"] = "Positive",
        ["geo_correction"] = "Geo Correction",
        ["swash_tta_precomp"] = "TTA Precomp",
        ["name"] = "Mixer",
        ["collective_tilt_correction_neg"] = "Negative",
        ["tail_motor_idle"] = "Tail Idle Thr%",
        ["swash_phase"] = "Phase Angle",
        ["collective_tilt_correction"] = "Collective Tilt Correction",
        ["swash_pitch_limit"] = "Total Pitch Limit"
      },
      ["about"] = {
        ["help_p1"] = "This page provides some useful information that you may be asked for when requesting support.",
        ["msgbox_credits"] = "Credits",
        ["ethos_version"] = "Ethos Version",
        ["rf_version"] = "Rotorflight Version",
        ["fc_version"] = "FC Version",
        ["name"] = "About",
        ["supported_versions"] = "Supported MSP Versions",
        ["license"] = "You may copy, distribute, and modify the software as long as you track changes/dates in source files. Any modifications to or software including (via compiler) GPL-licensed code must also be made available under the GPL along with build & install instructions.",
        ["simulation"] = "Simulation",
        ["help_p2"] = "For support, please first read the help pages on www.rotorflight.org",
        ["opener"] = "Rotorflight is an open source project. Contribution from other like minded people, keen to assist in making this software even better, is welcomed and encouraged. You do not have to be a hardcore programmer to help.",
        ["version"] = "Version",
        ["msp_version"] = "MSP Version",
        ["credits"] = "Notable contributors to both the Rotorflight firmware and this software are: Petri Mattila, Egon Lubbers, Rob Thomson, Rob Gayle, Phil Kaighin, Robert Burrow, Keith Williams, Bertrand Songis, Venbs Zhou... and many more who have spent hours testing and providing feedback!",
        ["msp_transport"] = "MSP Transport"
      },
      ["rates_advanced"] = {
        ["dyn_ceiling_gain"] = "Dynamic ceiling gain",
        ["acc_limit"] = "Accelerometer Limit",
        ["roll"] = "Roll",
        ["yaw_dynamics"] = "Yaw dynamics",
        ["pitch"] = "Pitch",
        ["col"] = "Col",
        ["setpoint_boost_cutoff"] = "Setpoint boost cutoff",
        ["yaw_dynamic_deadband_gain"] = "D. Band",
        ["rates_type"] = "Rates Type",
        ["setpoint_boost_gain"] = "Setpoint boost gain",
        ["msg_reset_to_defaults"] = "Rate type changed. Values will be reset to defaults.",
        ["yaw_dynamic_ceiling_gain"] = "Ceiling",
        ["yaw_boost"] = "Yaw boost",
        ["gain"] = "Gain",
        ["rate_table"] = "Rate Table",
        ["dynamics"] = "Dynamics",
        ["yaw"] = "Yaw",
        ["yaw_dynamic_deadband_filter"] = "Filter",
        ["name"] = "Rates",
        ["cutoff"] = "Cutoff",
        ["help_rate_table"] = "Please select the rate you would like to use. Saving will apply the choice to the active profile.",
        ["help_p1"] = "Rates type: Choose the rate type you prefer flying with. Raceflight and Actual are the most straightforward.",
        ["pitch_boost"] = "Pitch boost",
        ["help_p2"] = "Dynamics: Applied regardless of rates type. Typically left on defaults but can be adjusted to smooth heli movements, like with scale helis.",
        ["accel_limit"] = "Accel",
        ["dyn_deadband_filter"] = "Dynamic deadband filter",
        ["roll_boost"] = "Roll boost",
        ["dyn_deadband_gain"] = "Dynamic deadband gain",
        ["collective_dynamics"] = "Collective dynamics",
        ["roll_dynamics"] = "Roll dynamics",
        ["collective_boost"] = "Collective boost",
        ["pitch_dynamics"] = "Pitch dynamics",
        ["response_time"] = "Response Time"
      },
      ["servos"] = {
        ["tbl_yes"] = "YES",
        ["enable_servo_override"] = "Enable servo override",
        ["disabling_servo_override"] = "Disabling servo override...",
        ["help_tool_p3"] = "Minimum/Maximum: Adjust the end points of the selected servo.",
        ["tail"] = "TAIL",
        ["scale_negative"] = "Scale Negative",
        ["help_tool_p1"] = "Override: [*] Enable override to allow real-time updates of servo center point.",
        ["tbl_no"] = "NO",
        ["maximum"] = "Maximum",
        ["help_tool_p6"] = "Speed: The speed the servo moves. Generally only used for the cyclic servos to help the swash move evenly. Optional - leave all at 0 if unsure.",
        ["help_fields_rate"] = "Servo PWM rate.",
        ["cyc_pitch"] = "CYC.PITCH",
        ["center"] = "Center",
        ["minimum"] = "Minimum",
        ["speed"] = "Speed",
        ["help_fields_speed"] = "Servo motion speed in milliseconds.",
        ["disable_servo_override"] = "Disable servo override",
        ["help_fields_scale_pos"] = "Servo positive scaling.",
        ["saving_data"] = "Saving data...",
        ["cyc_left"] = "CYC.LEFT",
        ["saving"] = "Saving",
        ["name"] = "Servos",
        ["help_tool_p5"] = "Rate: The frequency the servo runs best at - check with manufacturer.",
        ["help_tool_p2"] = "Center: Adjust the center position of the servo.",
        ["enabling_servo_override"] = "Enabling servo override...",
        ["servo_prefix"] = "SERVO ",
        ["reverse"] = "Reverse",
        ["enable_servo_override_msg"] = "Servo override allows you to 'trim' your servo center point in real time.",
        ["cyc_right"] = "CYC.RIGHT",
        ["help_default_p2"] = "Primary flight controls that use the rotorflight mixer will display in the section called 'mixer'.",
        ["scale_positive"] = "Scale Positive",
        ["help_default_p1"] = "Please select the servo you would like to configure from the list below.",
        ["servo_override"] = "Servo Override",
        ["disable_servo_override_msg"] = "Return control of the servos to the flight controller.",
        ["help_fields_min"] = "Servo negative travel limit.",
        ["help_default_p3"] = "Any other servos that are not controlled by the primary flight mixer will be displayed in the section called 'Other servos'.",
        ["help_fields_mid"] = "Servo center position pulse width.",
        ["help_fields_scale_neg"] = "Servo negative scaling.",
        ["rate"] = "Rate",
        ["help_tool_p4"] = "Scale: Adjust the amount the servo moves for a given input.",
        ["help_fields_flags"] = "0 = Default, 1=Reverse, 2 = Geo Correction, 3 = Reverse + Geo Correction",
        ["geometry"] = "Geometry",
        ["help_fields_max"] = "Servo positive travel limit."
      },
      ["profile_autolevel"] = {
        ["acro_trainer"] = "Acro trainer",
        ["angle_mode"] = "Angle mode",
        ["max"] = "Max",
        ["name"] = "Autolevel",
        ["help_p1"] = "Acro Trainer: How aggressively the heli tilts back to level when flying in Acro Trainer Mode.",
        ["horizon_mode"] = "Horizon mode",
        ["gain"] = "Gain",
        ["help_p2"] = "Angle Mode: How aggressively the heli tilts back to level when flying in Angle Mode.",
        ["help_p3"] = "Horizon Mode: How aggressively the heli tilts back to level when flying in Horizon Mode."
      },
      ["filters"] = {
        ["filter_type"] = "Filter type",
        ["help_p4"] = "Dynamic Notch Filters: Automatically creates notch filters within the min and max frequency range.",
        ["notch_c"] = "Notch Count",
        ["rpm_preset"] = "Type",
        ["lowpass_1"] = "Lowpass 1",
        ["rpm_min_hz"] = "Min. Frequency",
        ["help_p2"] = "Gyro lowpass: Lowpass filters for the gyro signal. Typically left at default.",
        ["cutoff"] = "Cutoff",
        ["notch_1"] = "Notch 1",
        ["max_cutoff"] = "Max cutoff",
        ["help_p3"] = "Gyro notch filters: Use for filtering specific frequency ranges. Typically not needed in most helis.",
        ["lowpass_2"] = "Lowpass 2",
        ["rpm_filter"] = "RPM filter",
        ["help_p1"] = "Typically you would not edit this page without checking your Blackbox logs!",
        ["dyn_notch"] = "Dynamic Filters",
        ["notch_q"] = "Notch Q",
        ["lowpass_1_dyn"] = "Lowpass 1 dyn.",
        ["notch_min_hz"] = "Min",
        ["notch_max_hz"] = "Max",
        ["notch_2"] = "Notch 2",
        ["name"] = "Filters",
        ["min_cutoff"] = "Min cutoff",
        ["center"] = "Center"
      },
      ["status"] = {
        ["arming_disable_flag_3"] = "Bad RX Recovery",
        ["arming_disable_flag_20"] = "RPM Filter",
        ["arming_disable_flag_11"] = "Load",
        ["arming_disable_flag_22"] = "DSHOT Bitbang",
        ["dataflash_free_space"] = "Dataflash Free Space",
        ["arming_disable_flag_25"] = "Arm Switch",
        ["erasing"] = "Erasing",
        ["arming_disable_flag_9"] = "Boot Grace Time",
        ["megabyte"] = "MB",
        ["arming_disable_flag_17"] = "Paralyze",
        ["arming_disable_flag_5"] = "Governor",
        ["arming_disable_flag_8"] = "Angle",
        ["arming_disable_flag_1"] = "Fail Safe",
        ["cpu_load"] = "CPU Load",
        ["arming_disable_flag_15"] = "BST",
        ["arming_disable_flag_12"] = "Calibrating",
        ["arming_disable_flag_19"] = "Resc",
        ["arming_disable_flag_4"] = "Box Fail Safe",
        ["arming_disable_flag_24"] = "Motor Protocol",
        ["real_time_load"] = "Real-time Load",
        ["help_p2"] = "To erase the dataflash for more log file storage, press the button on the menu denoted by a '*'.",
        ["arming_disable_flag_2"] = "RX Fail Safe",
        ["ok"] = "OK",
        ["arming_disable_flag_0"] = "No Gyro",
        ["arming_disable_flag_18"] = "GPS",
        ["help_p1"] = "Use this page to view your current flight controller status. This can be useful when determining why your heli will not arm.",
        ["arming_flags"] = "Arming Flags",
        ["unsupported"] = "Unsupported",
        ["erase_prompt"] = "Would you like to erase the dataflash?",
        ["erase"] = "Erase",
        ["arming_disable_flag_10"] = "No Pre Arm",
        ["arming_disable_flag_21"] = "Reboot Required",
        ["name"] = "Status",
        ["arming_disable_flag_13"] = "CLI",
        ["arming_disable_flag_14"] = "CMS Menu",
        ["arming_disable_flag_16"] = "MSP",
        ["arming_disable_flag_7"] = "Throttle",
        ["erasing_dataflash"] = "Erasing dataflash...",
        ["arming_disable_flag_23"] = "Acc Calibration"
      },
      ["profile_pidbandwidth"] = {
        ["help_p1"] = "PID Bandwidth: Overall bandwidth in HZ used by the PID loop.",
        ["pitch"] = "P",
        ["yaw"] = "Y",
        ["name"] = "PID Bandwidth",
        ["bterm_cutoff"] = "B-term cut-off",
        ["help_p3"] = "B-term cutoff: B-term cutoff frequency in HZ.",
        ["dterm_cutoff"] = "D-term cut-off",
        ["help_p2"] = "D-term cutoff: D-term cutoff frequency in HZ.",
        ["roll"] = "R"
      }
    },
    ["navigation_save"] = "SAVE",
    ["menu_section_flight_tuning"] = "Flight Tuning",
    ["error_timed_out"] = "Error: timed out",
    ["check_rf_module_on"] = "Please check your rf module is turned on.",
    ["msg_saving"] = "Saving...",
    ["msg_save_not_commited"] = "Save not committed to EEPROM",
    ["menu_section_advanced"] = "Advanced",
    ["msg_loading_from_fbl"] = "Loading data from flight controller...",
    ["msg_reload_settings"] = "Reload data from flight controller?",
    ["menu_section_tools"] = "Tools",
    ["msg_connecting"] = "Connecting",
    ["msg_save_current_page"] = "Save current page to flight controller?",
    ["btn_ok_long"] = "                OK                ",
    ["check_discovered_sensors"] = "Please check you have discovered all sensors.",
    ["msg_loading"] = "Loading...",
    ["check_heli_on"] = "Please check your heli is powered up and radio connected.",
    ["check_bg_task"] = "Please enable the background task.",
    ["navigation_tools"] = "*",
    ["check_supported_version"] = "This version of the Lua script \ncan't be used with the selected model"
  },
  ["telemetry"] = {
    ["sensors"] = {
      ["attpitch"] = "P.angle",
      ["attroll"] = "R.angle",
      ["attyaw"] = "Y.angle",
      ["accx"] = "Accel X",
      ["accy"] = "Accel Y",
      ["accz"] = "Accel Z",
      ["groundspeed"] = "Ground Speed",
      ["esc_temp"] = "ESC Temperature",
      ["rate_profile"] = "Rate Profile",
      ["headspeed"] = "Headspeed",
      ["altitude"] = "Altitude",
      ["voltage"] = "Voltage",
      ["bec_voltage"] = "Bec voltage",
      ["cell_count"] = "Cell count",
      ["governor"] = "Governor State",
      ["adj_func"] = "Adj (Function)",
      ["fuel"] = "Fuel",
      ["smartfuel"] = "Smart Fuel",
      ["rssi"] = "RSSI",
      ["link"] = "Link Quality",
      ["adj_val"] = "Adj (Value)",
      ["arming_flags"] = "Arming Flags",
      ["current"] = "Current",
      ["throttle_pct"] = "Throttle %",
      ["consumption"] = "Consumption",
      ["smartconsumption"] = "Smart Consumption",
      ["pid_profile"] = "PID Profile",
      ["mcu_temp"] = "MCU Temperature",
      ["armdisableflags"] = "Arming Disable"
    }
  },
  ["widgets"] = {
    ["bbl"] = {
      ["erase_dataflash"] = "Erase dataflash",
      ["erasing"] = "Erasing...",
      ["display"] = "Display",
      ["display_free"] = "Free",
      ["display_used"] = "Used",
      ["display_outof"] = "Used/Total"
    },
    ["craftimage"] = {
    },
    ["craftname"] = {
      ["txt_enter_craft_name"] = "Enter Craft Name",
      ["title"] = "CRAFT NAME",
      ["txt_cancel"] = "Cancel",
      ["txt_save"] = "Save"
    },
    ["dashboard"] = {
      ["theme_load_error"] = "Your theme did not load correctly. Falling back to default theme.",
      ["validate_sensors"] = "PLEASE CHECK SENSORS",
      ["unsupported_resolution"] = "TO SMALL",
      ["loading"] = "ROTORFLIGHT",
      ["waiting_for_connection"] = "CONNECTING",
      ["check_bg_task"] = "BG TASK",
      ["check_rf_module_on"] = "RF MODULE",
      ["check_discovered_sensors"] = "SENSORS",
      ["no_link"] = "NO LINK",
      ["reset_flight"] = "Reset flight",
      ["reset_flight_ask_title"] = "Reset flight",
      ["reset_flight_ask_text"] = "Are you sure you want to reset the flight?",
      ["voltage"] = "Voltage",
      ["fuel"] = "Fuel",
      ["headspeed"] = "Headspeed",
      ["max"] = "Max",
      ["min"] = "Min",
      ["bec_voltage"] = "BEC Voltage",
      ["esc_temp"] = "ESC Temp",
      ["flight_duration"] = "Flight Duration",
      ["total_flight_duration"] = "Total Model Flight Duration",
      ["rpm_min"] = "RPM Min",
      ["rpm_max"] = "RPM Max",
      ["throttle_max"] = "Throttle Max",
      ["current_max"] = "Current Max",
      ["esc_max_temp"] = "ESC Temp Max",
      ["watts_max"] = "Max Watts",
      ["consumed_mah"] = "Consumed mAh",
      ["fuel_remaining"] = "Fuel Remaining",
      ["min_volts_cell"] = "Min Volts per cell",
      ["link_min"] = "Link Min",
      ["governor"] = "Governor",
      ["profile"] = "Profile",
      ["rates"] = "Rates",
      ["flights"] = "Flights",
      ["lq"] = "LQ",
      ["time"] = "Time",
      ["blackbox"] = "Blackbox",
      ["throttle"] = "Throttle",
      ["flight_time"] = "Flight Time",
      ["rssi_min"] = "RSSI Min",
      ["current"] = "Current",
      ["timer"] = "Timer",
      ["rpm"] = "RPM",
      ["min_voltage"] = "Min Voltage",
      ["max_voltage"] = "Max Voltage",
      ["min_current"] = "Min Current",
      ["max_current"] = "Max Current",
      ["max_tmcu"] = "Max T.MCU",
      ["max_emcu"] = "Max E.MCU",
      ["altitude"] = "Altitude",
      ["altitude_max"] = "Altitude Max",
      ["power"] = "Power",
      ["cell_voltage"] = "Cell Voltage",
      ["volts_per_cell"] = "Volts per cell",
      ["warning"] = "Warning",
      ["tx_batt"] = "TX Battery",
      ["link_max"] = "Link Max"
    },
    ["governor"] = {
      ["UNKNOWN"] = "UNKNOWN",
      ["IDLE"] = "IDLE",
      ["DISARMED"] = "DISARMED",
      ["OFF"] = "OFF",
      ["SPOOLUP"] = "SPOOLUP",
      ["ACTIVE"] = "ACTIVE",
      ["RECOVERY"] = "RECOVERY",
      ["THROFF"] = "THR-OFF",
      ["LOSTHS"] = "LOST-HS",
      ["AUTOROT"] = "AUTOROT",
      ["DISABLED"] = "DISABLED",
      ["BAILOUT"] = "BAILOUT"
    }
  }
}
