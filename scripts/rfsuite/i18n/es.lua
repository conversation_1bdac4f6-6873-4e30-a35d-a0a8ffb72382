--[[
 * Copyright (C) Rotorflight Project
 *
 * License GPLv3: https://www.gnu.org/licenses/gpl-3.0.en.html
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 3 as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * Please do not edit these files directly. These files are generated
 * from JSON files contained within the folder:
 *
 *   bin/i18n/json/*.json
 *
 * Running the script:
 *
 *   lua54 json-to-lua.lua
 *
 * will create these files for you.
]]

return {
  ["reload"] = "recargar",
  ["image"] = "imagen",
  ["error"] = "error",
  ["save"] = "guardar",
  ["ethos"] = "ethos",
  ["version"] = "versión",
  ["bg_task_disabled"] = "tarea en segundo plano deshabilitada",
  ["no_link"] = "sin conexión",
  ["background_task_disabled"] = "tarea en segundo plano deshabilitada",
  ["no_sensor"] = "sin sensor",
  ["api"] = {
    ["RC_TUNING"] = {
      ["setpoint_boost_cutoff_2"] = "Limite de impulso para el punto seleccionado.",
      ["response_time_3"] = "Incrementar o decrementar el tiempo de respuesta de la tasa para suavizar los movimientos del heli.",
      ["accel_limit_4"] = "Aceleracion maxima de la aeronave en respuesta a un movimiento del joystick.",
      ["setpoint_boost_gain_4"] = "Ganancia de impulso para el punto seleccionado.",
      ["yaw_dynamic_deadband_filter"] = "El filtro maximo aplicado a la zona muerta dinamica de la direccion.",
      ["setpoint_boost_gain_3"] = "Ganancia de impulso para el punto seleccionado.",
      ["response_time_2"] = "Incrementar o decrementar el tiempo de respuesta de la tasa para suavizar los movimientos del heli.",
      ["accel_limit_1"] = "Aceleracion maxima de la aeronave en respuesta a un movimiento del joystick.",
      ["setpoint_boost_cutoff_1"] = "Limite de impulso para el punto seleccionado.",
      ["setpoint_boost_cutoff_4"] = "Limite de impulso para el punto seleccionado.",
      ["setpoint_boost_gain_2"] = "Ganancia de impulso para el punto seleccionado.",
      ["accel_limit_2"] = "Aceleracion maxima de la aeronave en respuesta a un movimiento del joystick.",
      ["yaw_dynamic_deadband_gain"] = "La ganancia maxima aplicada a la zona muerta dinamica de la direccion.",
      ["accel_limit_3"] = "Aceleracion maxima de la aeronave en respuesta a un movimiento del joystick.",
      ["response_time_4"] = "Incrementar o decrementar el tiempo de respuesta de la tasa para suavizar los movimientos del heli.",
      ["setpoint_boost_cutoff_3"] = "Limite de impulso para el punto deseado.",
      ["setpoint_boost_gain_1"] = "Ganancia de impulso para el punto deseado.",
      ["yaw_dynamic_ceiling_gain"] = "Ganancia maxima aplicada al limite dinamico de la direccion.",
      ["response_time_1"] = "Incrementar o decrementar el tiempo de respuesta de la tasa para suavizar los movimientos del heli."
    },
    ["RESCUE_PROFILE"] = {
      ["rescue_flip_gain"] = "Determina que tan agresivamente se da vuelta el heli durante un rescate de invertido.",
      ["rescue_level_gain"] = "Determina que tan agresivamente se nivela el heli durante un rescate.",
      ["tbl_off"] = "APAGADO",
      ["rescue_hover_collective"] = "Valor de colectivo para sobrevuelo.",
      ["rescue_max_setpoint_rate"] = "Limite de tasa de alabeo/cabeceo en rescate. Helicopteros mas grandes pueden necesitar menores tasas de rotacion.",
      ["tbl_flip"] = "VOLTEAR",
      ["rescue_flip_mode"] = "Si se activa la funcion rescate mientras esta invertido, dar vuelta a vertical - o permanecer invertido.",
      ["rescue_pull_up_time"] = "Cuando se activa rescate, el helicoptero aplicara colectivo arriba durante este tiempo antes de continuar con inversion o nivelacion.",
      ["tbl_noflip"] = "NO VOLTEAR",
      ["rescue_exit_time"] = "Esto limita la aplicacion rapida de colectivo negativo si el helicoptero roto durante el rescate.",
      ["rescue_pull_up_collective"] = "Valor de colectivo para trepada (pull-up).",
      ["rescue_max_setpoint_accel"] = "Limite de que tan rapido el helicoptero acelera entrando a un alabeo/cabeceo. Helicopteros mas grandes pueden necesitar menores tasas de aceleracion.",
      ["tbl_on"] = "ENCENDIDO",
      ["rescue_climb_collective"] = "Valor de colectivo para trepada de rescate.",
      ["rescue_flip_time"] = "Si el helicoptero esta en rescate e intentando darse vuelta a nivelado y no lo logra en este intervalo de tiempo, se abortara el rescate.",
      ["rescue_climb_time"] = "Plazo de tiempo que se aplica colectivo arriba antes de continuar con sobrevuelo."
    },
    ["ESC_SENSOR_CONFIG"] = {
      ["hw4_current_offset"] = "Ajuste de corriente del Hobbywing v4",
      ["tbl_off"] = "Apagado",
      ["update_hz"] = "Tasa de refresco de telemetria del ESC",
      ["half_duplex"] = "Modo half-duplex para telemetria del ESC",
      ["consumption_correction"] = "Ajuste de la correccion de consumo",
      ["current_offset"] = "Ajuste del sensor de corriente",
      ["voltage_correction"] = "Ajuste de la correccion de voltaje",
      ["hw4_voltage_gain"] = "Ajuste de ganancia de voltaje del Hobbywing v4",
      ["tbl_on"] = "Encendido",
      ["hw4_current_gain"] = "Ajuste de ganancia de corriente del Hobbywing v4",
      ["current_correction"] = "Ajuste de la correccion de corriente",
      ["pin_swap"] = "Intercambio de pines TX y RX para telemetria del ESC"
    },
    ["ESC_PARAMETERS_FLYROTOR"] = {
      ["tbl_alwayson"] = "Siempre Encendido",
      ["throttle_min"] = "Valor minimo del acelerador",
      ["tbl_disabled"] = "Deshabilitado",
      ["tbl_auto"] = "Auto",
      ["starting_torque"] = "Torque de inicio para el motor",
      ["cell_count"] = "Numero de celdas en la bateria",
      ["motor_erpm_max"] = "RPM maxima",
      ["throttle_max"] = "Valor maximo del acelerador",
      ["tbl_ccw"] = "AntiH.",
      ["tbl_escgov"] = "Governor del ESC",
      ["temperature_protection"] = "Temperatura a la cual se disminuye la potencia en un 50%",
      ["tbl_automatic"] = "Automatico",
      ["low_voltage_protection"] = "Voltaje al cual se disminuye la potencia en un 50%",
      ["tbl_cw"] = "Horario",
      ["soft_start"] = "Valor de inicio suave",
      ["gov_i"] = "Valor de integral para el governor",
      ["timing_angle"] = "Angulo de timing para el motor",
      ["response_speed"] = "Velocidad de respuesta para el motor",
      ["current_gain"] = "Valor de ganancia para el sensor actual",
      ["tbl_extgov"] = "Governor Externo",
      ["buzzer_volume"] = "Volumen del Buzzer",
      ["gov_d"] = "Valor de derivada para el governor",
      ["tbl_enabled"] = "Habilitado",
      ["gov_p"] = "Valor proporcional para el governor"
    },
    ["ESC_PARAMETERS_YGE"] = {
      ["tbl_alwayson"] = "Siempre encendido",
      ["tbl_off"] = "Apagado",
      ["tbl_modestore"] = "Governor Heli guardado",
      ["tbl_modefree"] = "Libre (Atencion!)",
      ["tbl_modeglider"] = "Aero Planeador",
      ["tbl_modeext"] = "Governor Ext Heli",
      ["tbl_modeheli"] = "Governor Heli",
      ["tbl_medium"] = "Medio",
      ["tbl_autonorm"] = "Auto Normal",
      ["tbl_reverse"] = "Reversa",
      ["tbl_modef3a"] = "Aero F3A",
      ["tbl_auto"] = "Auto",
      ["tbl_slowdown"] = "Desacelerar",
      ["tbl_slow"] = "Lento",
      ["tbl_modeair"] = "Aero Motor",
      ["tbl_normal"] = "Normal",
      ["tbl_on"] = "Encendido",
      ["tbl_autoextreme"] = "Auto Extremo",
      ["tbl_autoefficient"] = "Auto Eficiente",
      ["tbl_smooth"] = "Suave",
      ["tbl_fast"] = "Rapido",
      ["tbl_custom"] = "Personalizado (Definido por PC)",
      ["tbl_cutoff"] = "Corte",
      ["tbl_autopower"] = "Auto Potencia",
      ["tbl_unused"] = "*no se usa*"
    },
    ["GOVERNOR_PROFILE"] = {
      ["governor_tta_gain"] = "Ganancia TTA aplicada para incrementar la velocidad del rotor para controlar la cola en direccion negativa (ej: la cola motorizada a velocidad menor que ralenti).",
      ["governor_collective_ff_weight"] = "Precompensacion de colectivo - cuanto colectivo se mezcla con el avance (feedforward).",
      ["governor_i_gain"] = "Ganancia I-term del loop PID.",
      ["governor_cyclic_ff_weight"] = "Precompensacion de ciclico - cuanto ciclico se mezcla con el avance (feedforward).",
      ["governor_f_gain"] = "Ganancia de avance (FF).",
      ["governor_gain"] = "Ganancia del loop PID maestro.",
      ["governor_headspeed"] = "Velocidad del rotor deseada para el perfil en uso.",
      ["governor_min_throttle"] = "Salida minima de acelerador que se le permite usar al governor.",
      ["governor_d_gain"] = "Ganancia D-term del loop PID.",
      ["governor_p_gain"] = "Ganancia P-term del loop PID.",
      ["governor_yaw_ff_weight"] = "Precompensacion de direccion - cuanto comando de direccion se mezcla con el avance (feedforward).",
      ["governor_max_throttle"] = "Salida maxima de acelerador que se le permite usar al governor.",
      ["governor_tta_limit"] = "Maximo incremento TTA por encima de la velocidad maxima del rotor."
    },
    ["PID_PROFILE"] = {
      ["bterm_cutoff_2"] = "Corte de B-term en Hz.",
      ["dterm_cutoff_1"] = "Corte D-term en Hz.",
      ["bterm_cutoff_1"] = "Corte de B-term en Hz.",
      ["gyro_cutoff_1"] = "Ancho de banda general para el loop PID en Hz.",
      ["tbl_on"] = "ENCENDIDO",
      ["dterm_cutoff_2"] = "Corte D-term en Hz.",
      ["yaw_inertia_precomp_cutoff"] = "Corte. Frecuencia de corte derivada, en pasos de 1/10Hz. Controla que tan agresivamente se precompensa. Un valor mayor es mas fuerte.",
      ["offset_limit_0"] = "Limite duro del ajuste de angulo para la Integral de Alta Velocidad (HSI) en el loop PID. El O-term nunca superara estos limites.",
      ["cyclic_cross_coupling_ratio"] = "Cantidad de compensacion alabeo-a-cabeceo necesaria, vs. cabeceo-a-alabeo.",
      ["yaw_precomp_cutoff"] = "Limite de frecuencia para todas las acciones de precompensacion de direccion.",
      ["error_limit_0"] = "Limite duro del error de angulo en el loop PID. Es el error absoluto y por lo tanto el I-term nunca superara estos limites.",
      ["trainer_gain"] = "Determina que tan agresivamente el helicoptero vuelve al angulo maximo (si se excedio) mientras esta en modo Entrenador Acro.",
      ["tbl_rpy"] = "ACD (RPY)",
      ["gyro_cutoff_2"] = "Ancho de banda general para el loop PID en Hz.",
      ["yaw_ccw_stop_gain"] = "Ganancia de detencion (PD) para rotacion antihoraria.",
      ["trainer_angle_limit"] = "Limita el angulo maximo de cabeceo/alabeo que alcanzara el helicoptero en modo Entrenador Acro.",
      ["tbl_rp"] = "RP",
      ["yaw_cyclic_ff_gain"] = "Avance de ciclico mezclado con direccion (precomp. ciclico-a-direccion).",
      ["error_decay_time_cyclic"] = "Constante de tiempo para el alivio del I-term del ciclico. Valores mas altos estabilizan el sobrevuelo, valores menores producen deriva.",
      ["error_decay_limit_cyclic"] = "Velocidad maxima de alivio del I-term para ciclico.",
      ["cyclic_cross_coupling_gain"] = "Cantidad de compensacion aplicada al desacople cabeceo-a-alabeo.",
      ["yaw_collective_dynamic_decay"] = "Tiempo de decadencia para precompensacion extra para comandos de colectivo.",
      ["pitch_collective_ff_gain"] = "Incrementarlo compensara el movimiento de cabeceo superior causado por el arrastre de cola al trepar.",
      ["iterm_relax_type"] = "Seleccione los ejes en los que esta activado. AC (RP): Alabeo, Cabeceo. ACD (RPY): Alabeo, Cabeceo, Direccion.",
      ["offset_limit_1"] = "Limite duro del ajuste de angulo para la Integral de Alta Velocidad (HSI) en el loop PID. El O-term nunca superara estos limites.",
      ["iterm_relax_cutoff_1"] = "Ayuda a reducir el rebote luego de movimientos rapidos del joystick. Puede causar inconsistencias en movimientos chicos si esta demasiado bajo.",
      ["error_limit_1"] = "Limite duro del error de angulo en el loop PID. Es el error absoluto y por lo tanto el I-term nunca superara estos limites.",
      ["horizon_level_strength"] = "Determina que tan agresivamente el helicoptero vuelve a horizontal en modo horizonte.",
      ["error_limit_2"] = "Limite duro del error de angulo en el loop PID. Es el error absoluto y por lo tanto el I-term nunca superara estos limites.",
      ["iterm_relax_cutoff_2"] = "Ayuda a reducir el rebote luego de movimientos rapidos del joystick. Puede causar inconsistencias en movimientos chicos si esta demasiado bajo.",
      ["tbl_off"] = "APAGADO",
      ["yaw_collective_ff_gain"] = "Avance de colectivo mezclado con direccion (precomp colectivo-a-direccion).",
      ["gyro_cutoff_0"] = "Ancho de banda general para el loop PID en Hz.",
      ["yaw_collective_dynamic_gain"] = "Aumento de precompensacion extra de direccion para comandos de colectivo.",
      ["cyclic_cross_coupling_cutoff"] = "Limite de frecuenca de la compensacion. Un valor mayor acelerara la accion de compensacion.",
      ["error_rotation"] = "Rota los valores actuales de alabeo y cabeceo alrededor del eje de rotacion vertical mientras el heli esta girando. Este termino se suele llamar Compensacion Piro (de pirueta).",
      ["angle_level_limit"] = "Limita el angulo maximo de inclinacion de cabeceo/alabeo en modo angulo.",
      ["yaw_cw_stop_gain"] = "Ganancia de detencion (PD) para rotacion horaria.",
      ["iterm_relax_cutoff_0"] = "Ayuda a reducir el rebote luego de movimientos rapidos del joystick. Puede causar inconsistencias en movimientos chicos si esta demasiado bajo.",
      ["yaw_inertia_precomp_gain"] = "Ganacia escalar. La fuerza de inercia del rotor principal. Un valor mayor implica mayor precompensacion aplicada al control de direccion.",
      ["dterm_cutoff_0"] = "Corte D-term en Hz.",
      ["angle_level_strength"] = "Determina que tan agresivamente el helicoptero vuelve a horizontal en modo angulo.",
      ["bterm_cutoff_0"] = "Corte de B-term en Hz.",
      ["error_decay_time_ground"] = "Alivio del error actual del controlador cuando la aeronave esta en tierra, para evitar que se vuelque."
    },
    ["MIXER_CONFIG"] = {
      ["swash_trim_1"] = "Ajuste para nivelar el swashplate cuando se utilizan brazos no extensibles.",
      ["tail_motor_idle"] = "Senial minima de acelerador que se envia al motor de cola. Deberia configurarse lo suficientemente alta para que no se detenga el motor.",
      ["tail_center_trim"] = "Define el ajuste de cola para direccion 0 en paso variable, o acelerador del motor para direccion 0 en cola motorizada.",
      ["tbl_cw"] = "Horario",
      ["swash_tta_precomp"] = "Precompensacion de mezclador para direccion 0.",
      ["swash_trim_2"] = "Ajuste para nivelar el swashplate cuando se utilizan brazos no extensibles.",
      ["swash_geo_correction"] = "Ajuste si hay demasiado colectivo negativo o positivo.",
      ["swash_trim_0"] = "Ajuste para nivelar el swashplate cuando se utilizan brazos no extensibles.",
      ["swash_phase"] = "Compensacion de fase para controles del swashplate.",
      ["collective_tilt_correction_pos"] = "Ajuste la escala de correccion de inclinacion del colectivo para paso posivo.",
      ["collective_tilt_correction_neg"] = "Ajuste la escala de correccion de inclinacion del colectivo para paso negativo",
      ["tbl_ccw"] = "AntiH.",
      ["swash_pitch_limit"] = "Valor maximo del angulo combinado de ciclico y colectivo en las palas."
    },
    ["MOTOR_CONFIG"] = {
      ["minthrottle"] = "Este valor PWM se envia al ESC/Servo cuando el acelerador esta bajo",
      ["motor_pwm_protocol"] = "El protcolo utilizado para comunicarse con el ESC",
      ["main_rotor_gear_ratio_0"] = "Numero de dientes del pinion del motor",
      ["maxthrottle"] = "Este valor PWM se envia al ESC/Servo cuando el acelerador esta a fondo",
      ["mincommand"] = "Este valor PWM se envia cuando el motor esta detenido",
      ["main_rotor_gear_ratio_1"] = "Numero de dientes del engranaje principal",
      ["tail_rotor_gear_ratio_1"] = "Numero de dientes del engranaje de autorotacion",
      ["motor_pwm_rate"] = "La frecuencia de refresco en Hz de la senial PWM del ESC",
      ["tail_rotor_gear_ratio_0"] = "Numero de dientes del engranaje de cola",
      ["motor_pole_count_0"] = "El numero de imanes en la campana del motor."
    },
    ["ESC_PARAMETERS_HW5"] = {
      ["tbl_cw"] = "Horario",
      ["tbl_fixedwing"] = "Ala Fija",
      ["tbl_disabled"] = "Deshabilitado",
      ["tbl_ccw"] = "AntiH.",
      ["tbl_heligov"] = "Governor Heli",
      ["tbl_softcutoff"] = "Corte Suave",
      ["tbl_proportional"] = "Proporcional",
      ["tbl_heliext"] = "Governor Ext Heli",
      ["tbl_helistore"] = "Guardar Governor Heli",
      ["tbl_hardcutoff"] = "Corte Rapido",
      ["tbl_normal"] = "Normal",
      ["tbl_autocalculate"] = "Calculo Auto",
      ["tbl_enabled"] = "Habilitado",
      ["tbl_reverse"] = "Reversa"
    },
    ["PILOT_CONFIG"] = {
      ["model_param1_value"] = "Establezca el tiempo esperado de vuelo en segundos. El transmisor emitirá beeps cuando el tiempo establecido se haya alcanzado."
    },
    ["BATTERY_INI"] = {
      ["calcfuel_local"] = "Calculate Fuel Using",
      ["tbl_off"] = "Current Sensor",
      ["tbl_on"] = "Voltage Sensor",
      ["sag_multiplier"] = "Raise or lower to adjust for the amount of voltage sag you see in flight.",
      ["kalman_multiplier"] = "Stabilise the input voltage sensor reading to prevent the fuel sensor bouncing due to sudden voltage dips. Raise or lower to adjust.",
      ["alert_off"] = "Off",
      ["alert_bec"] = "BEC",
      ["alert_rxbatt"] = "Rx Batt"
    },
    ["BATTERY_CONFIG"] = {
      ["vbatmincellvoltage"] = "El voltaje minimo por celda al cual se dispara la alarma de bajo voltaje.",
      ["vbatmaxcellvoltage"] = "El voltaje maximo por celda al cual se dispara la alarma de voltaje alto.",
      ["vbatwarningcellvoltage"] = "El voltaje por celda a partir del cual empieza a sonar la alarma de bajo voltaje.",
      ["batteryCellCount"] = "El numero de celdas de su bateria.",
      ["vbatfullcellvoltage"] = "El voltaje nominal de una celda totalmente cargada.",
      ["batteryCapacity"] = "La capacidad de su bateria en miliamper-hora."
    },
    ["ACC_TRIM"] = {
      ["pitch"] = "Utilice para corregir si el heli deriva en uno de los modos estabilizados (angulo, horizonte, etc.).",
      ["roll"] = "Utilice para corregir si el heli deriva en uno de los modos estabilizados (angulo, horizonte, etc.)."
    },
    ["PID_TUNING"] = {
      ["pid_1_I"] = "Que tan estrechamente el sistema sostiene su posicion.",
      ["pid_2_P"] = "Que tan estrechamente el sistema persigue el punto deseado.",
      ["pid_2_I"] = "Que tan estrechamente el sistema sostiene su posicion.",
      ["pid_1_O"] = "Se utiliza para evitar que la aeronave cabecee cuando se utiliza colectivo alto.",
      ["pid_1_F"] = "Ayuda a empujar el P-term en funcion de la posicion del joystick. Aumentarlo hara la respuesta mas agresiva, pero puede causar que se pase de largo.",
      ["pid_0_D"] = "Fuerza de amortiguacion de cualquier movimiento del sistema, incluyendo influencias externas. Tambien reduce el exceso de movimiento (pasarse de largo).",
      ["pid_1_P"] = "Que tan estrechamente el sistema persigue el punto deseado.",
      ["pid_0_I"] = "Que tan estrechamente el sistema sostiene su posicion.",
      ["pid_2_B"] = "Aumento adicional de avance para hacer que el heli reaccione mas rapidamente ante los movimientos del joystick.",
      ["pid_0_O"] = "Se utiliza para evitar que la aeronave se alabee cuando se utiliza colectivo alto.",
      ["pid_0_F"] = "Ayuda a empujar el P-term en funcion de la posicion del joystick. Aumentarlo hara la respuesta mas fuerte, pero puede causar que se pase de largo.",
      ["pid_2_F"] = "Ayuda a empujar el P-term en funcion de la posicion del joystick. Aumentarlo hara la respuesta mas fuerte, pero puede causar que se pase de largo.",
      ["pid_2_D"] = "Fuerza de amortiguacion de cualquier movimiento del sistema, incluyendo influencias externas. Tambien reduce el exceso de movimiento (pasarse de largo).",
      ["pid_0_P"] = "Que tan estrechamente el sistema persigue el punto deseado.",
      ["pid_1_D"] = "Fuerza de amortiguacion de cualquier movimiento del sistema, incluyendo influencias externas. Tambien reduce el exceso de movimiento (pasarse de largo).",
      ["pid_0_B"] = "Aumento adicional de avance para hacer que el heli reaccione mas rapidamente ante los movimientos del joystick.",
      ["pid_1_B"] = "Aumento adicional de avance para hacer que el heli reaccione mas rapidamente ante los movimientos del joystick."
    },
    ["GOVERNOR_CONFIG"] = {
      ["tbl_govmode_standard"] = "ESTANDAR",
      ["tbl_govmode_mode2"] = "MODO2",
      ["gov_tracking_time"] = "Constante de tiempo para cambios de velocidad del rotor, en segundos, midiendo desde cero hasta la velocidad maxima del rotor.",
      ["tbl_govmode_passthrough"] = "SALTEAR",
      ["tbl_govmode_mode1"] = "MODO1",
      ["gov_recovery_time"] = "Constante de tiempo para aceleracion de recuperacion, en segundos, midiendo desde cero hasta la velocidad maxima del rotor.",
      ["gov_startup_time"] = "Constante de tiempo para inicio lento, en segundos, midiendo desde cero hasta la velocidad maxima del rotor.",
      ["gov_handover_throttle"] = "El governor se activa por encima de este %. Por debajo de este valor la posicion del acelerador se transfiere directamente al ESC.",
      ["gov_spoolup_time"] = "Constante de tiempo para arranque lento, en segundos, midiendo desde cero hasta la velocidad maxima del rotor.",
      ["gov_spoolup_min_throttle"] = "Posicion minima del acelerador para arranque lento, en porcentaje. Para motores electricos el valor por defecto es 5%, para nitro este valor deberia hacer que el embrague empiece a acoplarse para un arranque suave 10-15%.",
      ["tbl_govmode_off"] = "APAGADO"
    },
    ["RC_CONFIG"] = {
      ["rc_deflection"] = "Defleccion desde el centro del joystick en microsegundos (uS).",
      ["rc_min_throttle"] = "Acelerador minimo que se espera del radio (salida del acelerador 0%), en microsegundos (uS).",
      ["rc_max_throttle"] = "Acelerador maximo que se espera del radio (salida del acelerador 100%), en microsegundos (uS).",
      ["rc_arm_throttle"] = "El acelerador debe estar por debajo de este valor en microsegundos (uS) para permitir el armado. Debe ser al menos 10uS menor que el minimo del acelerador.",
      ["rc_yaw_deadband"] = "Zona muerta para control de direccion en microsegundos (uS).",
      ["rc_deadband"] = "Zona muerta para control ciclico en microsegundos (uS).",
      ["rc_center"] = "Centro del joystick en microsegundos (uS)."
    },
    ["FILTER_CONFIG"] = {
      ["gyro_soft_notch_cutoff_2"] = "Ancho del filtro para el Punto en Hz.",
      ["gyro_lpf1_static_hz"] = "Frecuencia de corte del filtro pasabajos en Hz.",
      ["tbl_none"] = "NINGUNO",
      ["dyn_notch_max_hz"] = "Frecuencia Maxima a la que el Punto es aplicado.",
      ["tbl_1st"] = "1RO",
      ["rpm_min_hz"] = "Frecuencia Minima para el filtro de RPM.",
      ["dyn_notch_min_hz"] = "Frecuencia Minima a la que el Punto es aplicado.",
      ["gyro_lpf1_dyn_max_hz"] = "Corte max del filtro dinamico en Hz.",
      ["gyro_soft_notch_hz_2"] = "Frecuencia central aplicada al punto.",
      ["gyro_soft_notch_cutoff_1"] = "Ancho del filtro para el Punto en Hz.",
      ["gyro_soft_notch_hz_1"] = "Frecuencia central aplicada al Punto.",
      ["dyn_notch_count"] = "Numero de Puntos a aplicar.",
      ["dyn_notch_q"] = "Factor de Calidad del Filtro de Puntos.",
      ["gyro_lpf2_static_hz"] = "Frecuencia de corte del filtro pasabajos en Hz.",
      ["gyro_lpf1_dyn_min_hz"] = "Corte min del filtro dinamico en Hz.",
      ["tbl_2nd"] = "2DO",
      ["tbl_custom"] = "PERSONALIZADA",
      ["tbl_low"] = "BAJA",
      ["tbl_medium"] = "MEDIA",
      ["tbl_high"] = "ALTA"
    },
    ["ESC_PARAMETERS_XDFLY"] = {
      ["tbl_jadegreen"] = "VERDE JADE",
      ["tbl_off"] = "Apagado",
      ["tbl_low"] = "Bajo",
      ["tbl_orange"] = "NARANJA",
      ["tbl_fmfw"] = "Ala Fija",
      ["tbl_ccw"] = "AntiH.",
      ["tbl_medium"] = "Medio",
      ["tbl_yellow"] = "AMARILLO",
      ["tbl_reverse"] = "Reversa",
      ["tbl_red"] = "ROJO",
      ["tbl_high"] = "Alto",
      ["tbl_auto"] = "Auto",
      ["tbl_cw"] = "Horario",
      ["tbl_fmheli"] = "Helicoptero",
      ["tbl_purple"] = "PURPURA",
      ["tbl_green"] = "VERDE",
      ["tbl_blue"] = "AZUL",
      ["tbl_slow"] = "Lento",
      ["tbl_normal"] = "Normal",
      ["tbl_fast"] = "Rapido",
      ["tbl_escgov"] = "Governor ESC",
      ["tbl_white"] = "BLANCO",
      ["tbl_cyan"] = "CYAN",
      ["tbl_vslow"] = "Muy Lento",
      ["tbl_extgov"] = "Governor Externo",
      ["tbl_pink"] = "ROSA",
      ["tbl_fwgov"] = "Ala Fija",
      ["tbl_on"] = "Encendido"
    },
    ["ESC_PARAMETERS_SCORPION"] = {
      ["tbl_airplane"] = "Modo Avion",
      ["tbl_cw"] = "Horario",
      ["tbl_off"] = "Apagado",
      ["tbl_quad"] = "Modo Quad",
      ["tbl_heligov"] = "Governor Heli",
      ["tbl_exbus"] = "Jeti Exbus",
      ["tbl_boat"] = "Modo Barco",
      ["tbl_unsolicited"] = "No Solicitado",
      ["tbl_futsbus"] = "SBUS Futaba",
      ["tbl_ccw"] = "AntiH.",
      ["tbl_helistore"] = "Governor Heli (guardado)",
      ["tbl_standard"] = "Estandar",
      ["tbl_on"] = "Encendido",
      ["tbl_vbar"] = "VBar",
      ["tbl_vbargov"] = "Governor VBar",
      ["tbl_extgov"] = "Governor Externo"
    }
  },
  ["app"] = {
    ["btn_ok"] = "          OK           ",
    ["btn_close"] = "CERRAR",
    ["navigation_menu"] = "Menú",
    ["menu_section_hardware"] = "Hardware",
    ["msg_please_disarm_to_save_warning"] = "La configuración solo se guardara en EEPROM al desarmar",
    ["msg_saving_settings"] = "Guardando configuración...",
    ["msg_saving_to_fbl"] = "Guardando datos en el controlador de vuelo...",
    ["navigation_reload"] = "Recarga",
    ["menu_section_developer"] = "Desarrollador",
    ["check_msp_version"] = "Imposible determinar la versión MSP en uso.",
    ["menu_section_about"] = "Acerca de",
    ["msg_please_disarm_to_save"] = "Desarme para guardar a fin de asegurar la integridad de los datos.",
    ["unit_hertz"] = "Hz",
    ["msg_rebooting"] = "Reiniciando...",
    ["msg_save_settings"] = "Guardar configuración",
    ["btn_cancel"] = "CANCELAR",
    ["msg_connecting_to_fbl"] = "Conectando al controlador de vuelo...",
    ["navigation_help"] = "?",
    ["modules"] = {
      ["stats"] = {
        ["name"] = "Estadísticas",
        ["totalflighttime"] = "Tiempo Total de Vuelo",
        ["flightcount"] = "Nro de Vuelos",
        ["lastflighttime"] = "Tiempo Ultimo Vuelo",
        ["help_p1"] = "Utilice este módulo para actualizar las estadísticas guardadas en el controlador de vuelo."
      },
      ["settings"] = {
        ["name"] = "Configuración",
        ["no_themes_available_to_configure"] = "No hay temas configurables instalados en este dispositivo",
        ["txt_audio_timer"] = "Timer",
        ["txt_audio_events"] = "Eventos",
        ["txt_audio_switches"] = "Botones",
        ["txt_iconsize"] = "Tamaño Icon",
        ["txt_general"] = "General",
        ["txt_text"] = "TEXTO",
        ["txt_small"] = "PEQUEÑO",
        ["txt_large"] = "GRANDE",
        ["txt_syncname"] = "Sinc nombre modelo",
        ["txt_devtools"] = "Herramientas Desarrollo",
        ["txt_apiversion"] = "Versión API",
        ["txt_logging"] = "Loggeo",
        ["txt_compilation"] = "Compilación",
        ["txt_loglocation"] = "Posición Log",
        ["txt_console"] = "CONSOLA",
        ["txt_consolefile"] = "CONSOLA y ARCHIVO",
        ["txt_loglevel"] = "Nivel Log",
        ["txt_off"] = "APAGADO",
        ["txt_info"] = "INFO",
        ["txt_debug"] = "DEBUG",
        ["txt_mspdata"] = "Log datos msp",
        ["txt_queuesize"] = "Tamaño cola Log MSP",
        ["txt_memusage"] = "Uso de memoria Log",
        ["txt_batttype"] = "Tx Battery Options",
        ["txt_battdef"] = "Default",
        ["txt_batttext"] = "Text",
        ["txt_battdig"] = "Digital",
        ["dashboard"] = "Panel",
        ["dashboard_theme"] = "Tema",
        ["dashboard_theme_panel_global"] = "Tema por defecto Global",
        ["dashboard_theme_panel_model"] = "Tema opcional para este modelo",
        ["dashboard_theme_panel_model_disabled"] = "Deshabilitado",
        ["dashboard_settings"] = "Ajustes",
        ["dashboard_theme_preflight"] = "Tema Prevuelo",
        ["dashboard_theme_inflight"] = "Tema En-Vuelo",
        ["dashboard_theme_postflight"] = "Tema Postvuelo",
        ["audio"] = "Audio",
        ["localizations"] = "Localización",
        ["txt_development"] = "Desarrollo",
        ["temperature_unit"] = "Unidad de Temperatura",
        ["altitude_unit"] = "Unidad de Altitud",
        ["celcius"] = "Celsius",
        ["fahrenheit"] = "Fahrenheit",
        ["meters"] = "Metros",
        ["feet"] = "Pies",
        ["warning"] = "Advertencia",
        ["governor_state"] = "Estado del Governor",
        ["arming_flags"] = "Banderas de Armado",
        ["voltage"] = "Voltaje",
        ["pid_rates_profile"] = "Perfil PID/Tasas",
        ["pid_profile"] = "Perfil PID",
        ["rate_profile"] = "Perfil Tasas",
        ["esc_temperature"] = "Temperatura del ESC",
        ["esc_threshold"] = "Umbral (°)",
        ["bec_voltage"] = "Voltaje BEC",
        ["bec_threshold"] = "Umbral (V)",
        ["fuel"] = "Combustible",
        ["fuel_callout_default"] = "Por defecto (solo al 10%)",
        ["fuel_callout_10"] = "Cada 10%",
        ["fuel_callout_20"] = "Cada 20%",
        ["fuel_callout_25"] = "Cada 25%",
        ["fuel_callout_50"] = "Cada 50%",
        ["fuel_callout_percent"] = "Anuncio %",
        ["fuel_repeats_below"] = "Repite debajo de 0%",
        ["fuel_haptic_below"] = "Vibrar debajo de 0%",
        ["timer_alerting"] = "Alerta Timer",
        ["timer_elapsed_alert_mode"] = "Transcurrido Alerta Timer",
        ["timer_prealert_options"] = "Opciones de Alerta Pre-timer",
        ["timer_prealert"] = "Alerta Pre-timer",
        ["timer_alert_period"] = "Período de Alerta",
        ["timer_postalert_options"] = "Opciones Alerta Post-timer",
        ["timer_postalert"] = "Alerta Post-timer",
        ["timer_postalert_period"] = "Período Alerta",
        ["timer_postalert_interval"] = "Intervalo Alerta"
      },
      ["validate_sensors"] = {
        ["help_p1"] = "Esta herramienta intenta listar todos los sensores necesarios que faltan.",
        ["invalid"] = "INVALIDOS",
        ["name"] = "Sensores",
        ["msg_repair"] = "Activa los sensores necesarios en el controlador de vuelo?",
        ["msg_repair_fin"] = "El controlador de vuelo está configurado? Puede que necesite ejecutar 'Descubrir Sensores' para ver los cambios.",
        ["ok"] = "OK",
        ["help_p2"] = "Utilice esta herramienta para asegurar que está enviando los sensores que corresponde."
      },
      ["msp_exp"] = {
        ["help_p1"] = "Esta herramienta provee la capacidad de enviar un string de bytes al controlador de vuelo. Lo utilizan los desarrolladores durante el debugging.",
        ["name"] = "MSP Experimental",
        ["help_p2"] = "Si no sabe lo que está haciendo no la use, podrian suceder cosas peligrosas."
      },
      ["esc_tools"] = {
        ["unknown"] = "DESCONOCIDO",
        ["name"] = "Herramientas ESC",
        ["please_powercycle"] = "Apague y reencienda el ESC...",
        ["mfg"] = {
          ["hw5"] = {
            ["esc"] = "ESC",
            ["brake_force"] = "Fuerza frenado %",
            ["rotation"] = "Rotación",
            ["soft_start"] = "Arranque Suave",
            ["name"] = "Hobbywing V5",
            ["limits"] = "Límites",
            ["bec_voltage"] = "Voltaje BEC",
            ["gov_i_gain"] = "Ganancia-I",
            ["startup_time"] = "T. Arranque",
            ["lipo_cell_count"] = "Nro. Celdas LiPo",
            ["restart_time"] = "Tiempo Reinicio",
            ["volt_cutoff_type"] = "Tipo Corte Voltaje",
            ["motor"] = "Motor",
            ["brake_type"] = "Tipo de Freno",
            ["brake"] = "Freno",
            ["governor"] = "Governor",
            ["advanced"] = "Advanzado",
            ["basic"] = "Básico",
            ["flight_mode"] = "Modo de Vuelo",
            ["auto_restart"] = "Reinicio Auto",
            ["active_freewheel"] = "Corona Liberada",
            ["cutoff_voltage"] = "Voltaje de Corte",
            ["startup_power"] = "Potencia de Inicio",
            ["other"] = "Otro",
            ["timing"] = "Timing",
            ["gov_p_gain"] = "Ganancia-P"
          },
          ["xdfly"] = {
            ["hv_bec_voltage"] = "Voltaje HV BEC",
            ["gov"] = "Governor",
            ["brake_force"] = "Fuerza de Frenado",
            ["sr_function"] = "Función SR",
            ["name"] = "XDFLY",
            ["lv_bec_voltage"] = "Voltaje LV BEC",
            ["auto_restart_time"] = "Tiempo Auto Reinicio",
            ["acceleration"] = "Aceleración",
            ["motor_direction"] = "Dirección Motor",
            ["smart_fan"] = "Ventilador Inteligente",
            ["governor"] = "Governor",
            ["advanced"] = "Avanzado",
            ["gov_i"] = "Gov-I",
            ["cell_cutoff"] = "Corte Celdas",
            ["led_color"] = "Color LED",
            ["basic"] = "Básico",
            ["startup_power"] = "Potencia de Inicio",
            ["motor_poles"] = "Polos del Motor",
            ["capacity_correction"] = "Corrección Capacidad",
            ["timing"] = "Timing",
            ["gov_p"] = "Gov-P"
          },
          ["flrtr"] = {
            ["gov"] = "Governor",
            ["motor_temp_sensor"] = "Sensor temp. motor",
            ["starting_torque"] = "Torque de Inicio",
            ["cell_count"] = "Nro. de Celdas",
            ["gov_p"] = "Gov-P",
            ["motor_erpm_max"] = "Max ERPM Motor",
            ["name"] = "FLYROTOR",
            ["low_voltage_protection"] = "Proteccion bajo voltaje",
            ["gov_d"] = "Gov-D",
            ["telemetry_protocol"] = "Protocolo de telemetria",
            ["motor_direction"] = "Direccion del Motor",
            ["throttle_protocol"] = "Protocolo de acelerador",
            ["soft_start"] = "Arranque Suave",
            ["other"] = "Otro",
            ["temperature_protection"] = "Proteccion Temperatura",
            ["buzzer_volume"] = "Volumen del Buzzer",
            ["timing_angle"] = "Angulo de Timing",
            ["governor"] = "Governor",
            ["advanced"] = "Avanzado",
            ["gov_i"] = "Gov-I",
            ["bec_voltage"] = "Voltaje BEC",
            ["fan_control"] = "Control de Ventilador",
            ["basic"] = "Basico",
            ["current_gain"] = "Ganancia Corriente",
            ["led_color"] = "Color LED",
            ["motor_temp"] = "Temperatura del motor",
            ["response_speed"] = "Velocidad de Respuesta",
            ["battery_capacity"] = "Capacidad de la bateria"
          },
          ["scorp"] = {
            ["esc_mode"] = "Modo ESC",
            ["min_voltage"] = "Voltaje Mín",
            ["rotation"] = "Rotación",
            ["telemetry_protocol"] = "Protocolo Telemetría",
            ["name"] = "Scorpion",
            ["runup_time"] = "Tiempo Previo",
            ["motor_startup_sound"] = "Sonido Inicio Motor",
            ["gov_integral"] = "Gov Integral",
            ["gov_proportional"] = "Gov Proporcional",
            ["cutoff_handling"] = "Manejo de Corte",
            ["bailout"] = "Rescate",
            ["limits"] = "Límites",
            ["soft_start_time"] = "Tiempo Inicio Suave",
            ["advanced"] = "Advanzado",
            ["bec_voltage"] = "Voltaje BEC",
            ["extra_msg_save"] = "Renicie el ESC para aplicar los cambios",
            ["basic"] = "Básico",
            ["max_current"] = "Corriente Máx",
            ["max_temperature"] = "Temperatura Máx",
            ["protection_delay"] = "Retardo Protección",
            ["max_used"] = "Máx Usado"
          },
          ["yge"] = {
            ["esc_mode"] = "Modo ESC",
            ["esc"] = "ESC",
            ["current_limit"] = "Límite de Corriente",
            ["f3c_auto"] = "Autorrotación F3C",
            ["name"] = "YGE",
            ["max_start_power"] = "Potencia Máx Inicio",
            ["lv_bec_voltage"] = "BEC",
            ["pinion_teeth"] = "Dientes Piñón",
            ["auto_restart_time"] = "Tiempo Auto Reinicio",
            ["main_teeth"] = "Dientes Corona",
            ["other"] = "Otro",
            ["limits"] = "Límites",
            ["cell_cutoff"] = "Corte Celdas",
            ["throttle_response"] = "Respuesta Acelerador",
            ["stick_zero_us"] = "Cero Joystick",
            ["advanced"] = "Avanzado",
            ["gov_i"] = "Gov-I",
            ["motor_pole_pairs"] = "Pares Polos Motor",
            ["stick_range_us"] = "Rango Joystick",
            ["basic"] = "Básico",
            ["min_start_power"] = "Potencia Mín Inicio",
            ["active_freewheel"] = "Corona Liberada",
            ["direction"] = "Dirección",
            ["timing"] = "Timing del Motor",
            ["gov_p"] = "Gov-P"
          }
        },
        ["searching"] = "Buscando"
      },
      ["pids"] = {
        ["help_p1"] = "Ganancia F (Alabeo/Cabeceo): Comience en 70, aumente hasta que las paradas sean en seco sin derivas. Mantenga alabeo y cabeceo en rangos similares.",
        ["o"] = "O",
        ["pitch"] = "Cabeceo",
        ["i"] = "I",
        ["yaw"] = "Dirección",
        ["roll"] = "Alabeo",
        ["help_p5"] = "Prueba & Ajuste: Vuele, observe, y afine hasta la mejor performance en condiciones reales.",
        ["p"] = "P",
        ["f"] = "F",
        ["name"] = "PIDs",
        ["help_p2"] = "Ganancia I (Alabeo/Cabeceo): Aumente gradualmente para bombeo de cabeceo estable en piruetas. Demasiado alto causa tambaleo; mantega alabeo/cabeceo en rangos similares.",
        ["d"] = "D",
        ["b"] = "B",
        ["help_p4"] = "Ganancia Detención Cola (Horaria CW/Antihoraria CCW): Ajuste separadamente para detenciones limpias, sin rebotes, en ambos sentidos.",
        ["help_p3"] = "Ganacia P/I/D de cola: Incremente P hasta un leve tambaleo durante embudos, luego disminuya levemente. Aumente I hasta que la cola se mantenga firme durante movimientos rápidos (demasiado alto causa un meneo lento de la cola). Ajuste D para paradas suaves — más alto para servos lentos, más bajo para rápidos."
      },
      ["msp_speed"] = {
        ["seconds_600"] = "  600S  ",
        ["avg_query_time"] = "Tiempo Promedio Consulta",
        ["seconds_30"] = "  30S  ",
        ["name"] = "Velocidad MSP",
        ["max_query_time"] = "Tiempo Máx Consulta",
        ["help_p1"] = "Esta herramienta intenta determinar la calidad de la conexión de datos MSP llevando a cabo tantas consultas MSP grandes como sea posible en 30 segundos.",
        ["retries"] = "Reintentos",
        ["checksum_errors"] = "Errores de checksum",
        ["test_length"] = "Longitud del Test",
        ["start"] = "Inicio",
        ["memory_free"] = "Memoria libre",
        ["start_prompt"] = "Desea comenzar la prueba? A continuacion seleccione el tiempo de ejecución.",
        ["rf_protocol"] = "Protocolo RF",
        ["min_query_time"] = "Tiempo Mín Consulta",
        ["seconds_120"] = "  120S  ",
        ["seconds_300"] = "  300S  ",
        ["testing"] = "Probando",
        ["successful_queries"] = "Consultas exitosas",
        ["timeouts"] = "Tiempo agotado",
        ["testing_performance"] = "Probando performance MSP...",
        ["total_queries"] = "Total de consultas"
      },
      ["copyprofiles"] = {
        ["profile_type"] = "Tipo de Perfil",
        ["profile_type_pid"] = "PID",
        ["profile_type_rate"] = "Tasa",
        ["msgbox_save"] = "Guardar Configuración",
        ["name"] = "Copiar Perfiles",
        ["help_p1"] = "Copiar perfil PID o perfil de tasas de Fuente a Destino.",
        ["dest_profile"] = "Perfil Destino",
        ["source_profile"] = "Perfil Fuente",
        ["msgbox_msg"] = "Guarda esta página en el controlador de vuelo?",
        ["help_p2"] = "Seleccione fuente y destino y oprima Guardar para copiar el perfil."
      },
      ["esc_motors"] = {
        ["min_throttle"] = "Valor PWM Acelerador 0%",
        ["tail_motor_ratio"] = "Relación Motor ",
        ["max_throttle"] = "Valor PWM Acelerador 100%",
        ["main_motor_ratio"] = "Relación Motor ",
        ["pinion"] = "Piñón",
        ["main"] = "Principal",
        ["help_p1"] = "Configure características del motor y ESC.",
        ["rear"] = "de Cola",
        ["front"] = "Frontal",
        ["voltage_correction"] = "Corrección de Voltaje",
        ["mincommand"] = "Valor PWM Motor Detenido",
        ["name"] = "ESC/Motores",
        ["motor_pole_count"] = "Nro. de Polos del Motor",
        ["current_correction"] = "Corrección de Corriente",
        ["consumption_correction"] = "Corrección de Consumo"
      },
      ["radio_config"] = {
        ["deflection"] = "Deflección",
        ["max_throttle"] = "Máx",
        ["stick"] = "Joystick",
        ["arming"] = "Armado",
        ["yaw_deadband"] = "Dirección",
        ["cyclic"] = "Cíclico",
        ["name"] = "Config Radio",
        ["help_p1"] = "Configure su radio. Centro del joystick, armado, retención de acelerador, y corte de acelerador.",
        ["min_throttle"] = "Mín",
        ["throttle"] = "Acelerador",
        ["deadband"] = "Zona muerta",
        ["center"] = "Centro"
      },
      ["profile_select"] = {
        ["help_p1"] = "Indique el perfil de vuelo o perfil de tasa que desee utilizar.",
        ["rate_profile"] = "Perfil Tasa",
        ["pid_profile"] = "Perfil PID",
        ["save_prompt"] = "Guarda esta página en el controlador de vuelo?",
        ["save_prompt_local"] = "Guardar página actual?",
        ["cancel"] = "CANCELAR",
        ["name"] = "Selección Perfil",
        ["save_settings"] = "Guardar config",
        ["ok"] = "OK",
        ["help_p2"] = "Si utiliza un botón en su radio para cambiar los modos de vuelo o tasas, esta función anula la elección tan pronto como cambie la posición del botón."
      },
      ["profile_governor"] = {
        ["tail_torque_assist"] = "Asist. Torque Cola",
        ["p"] = "P",
        ["i"] = "I",
        ["yaw"] = "Dirección",
        ["cyc"] = "Cícl.",
        ["f"] = "F",
        ["name"] = "Governor",
        ["d"] = "D",
        ["help_p1"] = "Velocidad Rotor Máx: Velocidad deseada del rotor con el acelerador al 100%.",
        ["help_p6"] = "Asistencia de Torque de Cola: Para colas motorizadas. Ganancia y límite de aceleracion del rotor cuando se utiliza el torque del rotor principal para asistir al giro sobre el eje vertical.",
        ["help_p4"] = "Precomp: Precompensación del Governor para comandos de cíclico, colectivo, y dirección (cola).",
        ["max_throttle"] = "Acelerador Máx",
        ["full_headspeed"] = "Velocidad Máx Rotor",
        ["precomp"] = "Precomp",
        ["gain"] = "Ganacia PID maestra",
        ["disabled_message"] = "Governor Rotorflight está deshabilitado",
        ["help_p3"] = "Ganancias: Ajuste fino del governor.",
        ["col"] = "Colec.",
        ["min_throttle"] = "Acelerador Mín",
        ["tta_limit"] = "Limite",
        ["help_p2"] = "Ganacia PID maestra: Que tanto trabaja el governor para sostener las RPM.",
        ["gains"] = "Ganancias",
        ["help_p5"] = "Acelerador Máx: El porcentaje máximo de acelerador que se le permite utilizar al governor.",
        ["tta_gain"] = "Ganancia"
      },
      ["profile_tailrotor"] = {
        ["gain"] = "Ganancia",
        ["help_p4"] = "Ganancia FF Colectivo: Precompensación de cola para comandos de colectivo.",
        ["collective_impulse_ff"] = "Impulso Colectivo FF",
        ["help_p2"] = "Corte Precomp.: Límite de frecuencia para todas las acciones de precompensación de dirección.",
        ["cutoff"] = "Corte",
        ["help_p3"] = "Ganancia FF Cíclico: Precompensación de cola para comandos de cíclico.",
        ["help_p1"] = "Ganancia de parada de dirección: Un valor más alto hará que la cola se detenga más agresivamente, pero puede causar oscilaciones si el valor es demasiado alto. Ajuste horario (CW) y antihorario (CCW) para hacer que las paradas de dirección sean parejas para ambos lados.",
        ["inertia_precomp"] = "Precomp. Inercia",
        ["cyclic_ff_gain"] = "Ganancia FF Cíclico",
        ["help_p5"] = "Impulso Colectivo FF: Precompensación de impulso de cola para comandos de colectivo. Si se necesita precompensación de la cola al principio de un comando de colectivo.",
        ["cw"] = "Horario",
        ["ccw"] = "AntiH.",
        ["yaw_stop_gain"] = "Ganacia parada dir.",
        ["precomp_cutoff"] = "Corte de Precomp.",
        ["collective_ff_gain"] = "Ganancia FF Colectivo",
        ["name"] = "Rotor de Cola",
        ["decay"] = "Distensión"
      },
      ["profile_pidcontroller"] = {
        ["help_p4"] = "Error rotación: Permitir que se compartan los errores entre todos los ejes.",
        ["ground_error_decay"] = "Distensión Error Tierra",
        ["yaw"] = "Y",
        ["inflight_error_decay"] = "Distens. Err en Vuelo",
        ["help_p2"] = "Límite Error: Angulo límite para I-term.",
        ["error_limit"] = "Límite Error",
        ["help_p3"] = "Límite de compensación: Angulo límite para Integral de Alta Velocidad (HSI O-term).",
        ["cutoff_point"] = "Punto de Corte",
        ["limit"] = "Límite",
        ["iterm_relax"] = "Distensión I-term",
        ["hsi_offset_limit"] = "Límite Compensación HSI",
        ["pitch"] = "P",
        ["name"] = "Controlador PID",
        ["error_rotation"] = "Error rotación",
        ["roll"] = "R",
        ["help_p5"] = "Distensión I-term: Limita la acumulación de I-term durante movimientos rápidos - ayuda a reducir el rebote luego de movimientos rápidos del joystick. En general debe ser menor para helis grandes y puede ser mayor en helis pequeños. Lo mejor es reducir tanto como sea necesario para su estilo de vuelo.",
        ["time"] = "Tiempo",
        ["help_p1"] = "Distensión de Error en Tierra: Distensión de PID para ayudar a prevenir que el heli se vuelque estando en tierra."
      },
      ["logs"] = {
        ["help_logs_p2"] = "Nota: Para activar los LOGs es escencial que estén activados los siguientes sensores..",
        ["name"] = "Logs",
        ["help_logs_p1"] = "Seleccione un archivo de la lista que sigue.",
        ["msg_no_logs_found"] = "NO HAY LOGS",
        ["help_logs_tool_p1"] = "Utilice el control deslizante para navegar en el gráfico.",
        ["help_logs_p3"] = "- estado de armado, voltaje, velocidad del rotor, corriente, temperatura del ESC"
      },
      ["battery"] = {
        ["calcfuel_local"] = "Calculate fuel using",
        ["max_cell_voltage"] = "Voltaje Máx Celda",
        ["full_cell_voltage"] = "Voltaje de Celda llena",
        ["name"] = "Batería",
        ["min_cell_voltage"] = "Voltaje Mín Celda",
        ["help_p1"] = "La configuración de celdas se utiliza para que el controlador de vuelo pueda monitorear el voltaje de la batería y generar advertencias cuando esté por debajo de cierto nivel.",
        ["battery_capacity"] = "Capacidad Batería",
        ["warn_cell_voltage"] = "Advertencia Voltaje de Celda",
        ["cell_count"] = "Número de Celdas",
        ["consumption_warning_percentage"] = "Advertencia Consumo %",
        ["timer"] = "Alarma Tiempo Vuelo",
        ["voltage_multiplier"] = "Compensación de caída",
        ["kalman_multiplier"] = "Filter compensation",
        ["alert_type"] = "BEC or Rx Batt Voltage Alert",
        ["bec_voltage_alert"] = "BEC Alert Value",
        ["rx_voltage_alert"] = "RX Batt Alert Value"
      },
      ["profile_mainrotor"] = {
        ["gain"] = "Ganancia",
        ["help_p4"] = "Límite de Frecuencia de Acople Cruzado: Límite de Frecuencia para la compensación; un valor mayor aumentará la velocidad de la compensación.",
        ["collective_pitch_comp_short"] = "Compens. Ang. Colectivo.",
        ["cyclic_cross_coupling"] = "Acople inter-Ciclico",
        ["collective_pitch_comp"] = "Compensación de Angulo de Colectivo",
        ["name"] = "Rotor Principal",
        ["cutoff"] = "Corte",
        ["ratio"] = "Relación",
        ["help_p1"] = "Compensación de Angulo de Colectivo: Incrementar este valor compensa el movimiento de cabeceo causado por el arrastre de la cola durante la trepada.",
        ["help_p2"] = "Ganancia de Acople Cruzado: Cancela el acople de alabeo cuando se aplica solo elevador.",
        ["help_p3"] = "Relación de Acople Cruzado: Cantidad de compensación (cabeceo vs alabeo) a aplicar."
      },
      ["sbusout"] = {
        ["title"] = "Salida SBUS",
        ["help_fields_source"] = "ID Fuente para la mezcla , contando desde 0-15.",
        ["help_default_p4"] = "- Para motores, use 0, 1000.",
        ["ch_prefix"] = "CH",
        ["channel_prefix"] = "CANAL ",
        ["saving"] = "Guardando",
        ["name"] = "Salida SBUS",
        ["channel_page"] = "Salida Sbus/CH",
        ["receiver"] = "Receptor",
        ["servo"] = "Servo",
        ["type"] = "Tipo",
        ["saving_data"] = "Guardando datos...",
        ["help_fields_max"] = "El valor máximo PWM a enviar",
        ["motor"] = "Motor",
        ["help_default_p5"] = "- O bien puede personalizar su propio mapeo.",
        ["help_default_p1"] = "Configure las mezclas avanzadas y el mapeo de canales si utiliza la salida SBUS en un puerto serial.",
        ["max"] = "Máx",
        ["save_prompt"] = "Guarda esta página en el controlador de vuelo?",
        ["help_fields_min"] = "El valor mínimo PWM a enviar.",
        ["mixer"] = "Mezclador",
        ["ok"] = "OK",
        ["cancel"] = "CANCELAR",
        ["help_default_p2"] = "- Para canales RX o servos comunes use 1000, 2000; para servos de banda angosta use 500, 1000.",
        ["save_settings"] = "Guardar Config.",
        ["min"] = "Mín",
        ["help_default_p3"] = "- Para reglas de mezclador, use -1000, 1000.",
        ["source"] = "Fuente"
      },
      ["profile_rescue"] = {
        ["help_p4"] = "Sobrevuelo: Cuánto colectivo se necesita para mantener el heli sobrevolando el lugar.",
        ["hover"] = "Sobrevuelo",
        ["collective"] = "Colectivo",
        ["help_p2"] = "Levantado: Cantidad de colectivo y tiempo que se aplica para detener la caída.",
        ["climb"] = "Trepada",
        ["mode_enable"] = "Habilitar Modo Rescate",
        ["help_p3"] = "Trepada: Cantidad y tiempo de colectivo para mantener una trepada constante.",
        ["help_p1"] = "Invertir a Vertical: Dar vuelta el heli a posición vertical cuando se activa la función rescate.",
        ["flip_upright"] = "Invertir a Vertical",
        ["flip"] = "Invers.",
        ["level_gain"] = "Nivel",
        ["name"] = "Rescate",
        ["exit_time"] = "T. Salida",
        ["help_p5"] = "Inversión: Cuánto tiempo esperar antes de abortar una inversión que no funcionó.",
        ["help_p6"] = "Ganancias: Que tan duro debe luchar para mantener el helicóptero horizontal en modo rescate.",
        ["fail_time"] = "Tiempo Falla",
        ["pull_up"] = "Levantado",
        ["rate"] = "Tasa",
        ["help_p7"] = "Tasa y Acel.: Máxima tasa de rotación y aceleración durante el nivelado de rescate.",
        ["gains"] = "Ganancias",
        ["time"] = "Tiempo",
        ["accel"] = "Acel."
      },
      ["trim"] = {
        ["disable_mixer_message"] = "Devolver el control de los servos al controlador de vuelo.",
        ["tail_motor_idle"] = "% Motor cola ralentí",
        ["disable_mixer_override"] = "No permitir toma de mezclador",
        ["yaw_trim"] = "% ajuste Direccion",
        ["enable_mixer_message"] = "Ponga todos los servos en su posición central configurada.\r\n\r\nEsto permitirá que se guarden todos los valores de ajuste de los servos.",
        ["mixer_override_disabling"] = "Desactivando toma de mezclador...",
        ["roll_trim"] = "% ajuste Alabeo",
        ["pitch_trim"] = "% ajuste Cabeceo",
        ["name"] = "Ajuste",
        ["help_p2"] = "Cola motorizada: Si tiene una cola motorizada, utilice este parámetro para configurar la velocidad mínima de ralentí a la que el heli no gira.",
        ["mixer_override"] = "Toma de Mezclador",
        ["mixer_override_enabling"] = "Activando toma de mezclador...",
        ["enable_mixer_override"] = "Permitir toma de mezclador",
        ["collective_trim"] = "% ajuste Colectivo",
        ["help_p1"] = "Ajuste de enlaces: Se utiliza para corregir pequeños problemas de nivelación en el swashplate. Normalmente se utiliza cuando los brazos del swashplate no son ajustables."
      },
      ["governor"] = {
        ["help_p1"] = "Estos parámetros se aplican globalmente al governor independientemente del perfil en uso.",
        ["handover_throttle"] = "% Acelerador Transferencia",
        ["spoolup_min_throttle"] = "% Acelerador Mín Arranque",
        ["recovery_time"] = "Tiempo de Recuperación",
        ["mode"] = "Modo",
        ["help_p2"] = "Todos los parámetros son tiempos en segundos para cada acción del governor.",
        ["tracking_time"] = "Tiempo de Tracking",
        ["name"] = "Governor",
        ["startup_time"] = "Tiempo de Inicio",
        ["spoolup_time"] = "Tiempo de Arranque"
      },
      ["accelerometer"] = {
        ["help_p1"] = "El acelerómetro se utiliza para medir el ángulo del controlador de vuelo respecto del horizonte. Los datos obtenidos son utilizados para estabilizar la aeronave y porveer funcionalidad de nivelado automático.",
        ["name"] = "Acelerómetro",
        ["pitch"] = "Cabeceo",
        ["msg_calibrate"] = "Calibrar el acelerómetro?",
        ["roll"] = "Alabeo"
      },
      ["gyro_alignment"] = {
        ["name"] = "Alineación de la Placa",
        ["board_alignment"] = "Alineación de la Placa",
        ["roll"] = "Alabeo",
        ["pitch"] = "Cabeceo",
        ["yaw"] = "Guiñada",
        ["msg_calibrate"] = "¿Calibrar el acelerómetro? Esto reiniciará el acelerómetro y aplicará la configuración actual de alineación de la placa.",
        ["help_p1"] = "La herramienta de alineación de la placa permite configurar los ángulos de alineación de la placa para compensar la orientación de montaje del controlador de vuelo.",
        ["help_p2"] = "Alineación de la Placa: Ajuste los ángulos de alabeo, cabeceo y guiñada (en grados) para que coincidan con la orientación física de su controlador de vuelo en relación con el marco del helicóptero.",
        ["help_p3"] = "Estas configuraciones compensan los casos donde el controlador de vuelo no está montado perfectamente alineado con los ejes del marco del helicóptero.",
        ["help_p4"] = "Use el botón Herramienta para calibrar el acelerómetro después de realizar cambios de alineación. Guarde la configuración en EEPROM cuando termine."
      },
      ["rates"] = {
        ["help_table_5_p2"] = "Tasa Máx.: Tasa de rotación máxima durante el desplazamiento máximo del joystick en grados por segundo.",
        ["actual"] = "ACTUAL",
        ["max_rate"] = "Tasa Máx",
        ["help_table_4_p3"] = "Expo: Reduce la sensibilidad cerca del centro del joystick donde hacen falta controles finos.",
        ["rate"] = "Tasa",
        ["help_table_5_p1"] = "Tasa RC: Se usa para reducir le sensibilidad cerca del centro del joystick. Configurar la Tasa RC a la mitad de la Tasa Máx da por resultado una función lineal. Un valor menor reduce la sesibilidad cerca del centro del joystick y un valor mayor incrementará la Tasa Máx.",
        ["help_table_4_p2"] = "Tasa Máx: Tasa máxima de rotación durante el desplazamiento máximo del joystick en grados por segundo.",
        ["center_sensitivity"] = "Sens.Cntro.",
        ["rc_curve"] = "Curva RC",
        ["roll"] = "Alabeo",
        ["none"] = "NINGUNA",
        ["betaflight"] = "BETAFLIGHT",
        ["kiss"] = "KISS",
        ["help_table_1_p3"] = "Expo: Reduce la sensibilidad cerca del centro del joystick donde hacen falta controles finos.",
        ["help_table_3_p2"] = "Tasa: Incrementa la tasa de rotación máxima al tiempo que reduce la sensibilidad cerca del desplazamiento medio del joystick.",
        ["help_table_2_p2"] = "Acro+: Incrementa la tasa de rotación al tiempo que reduce la sensibilidad cerca del desplazamiento medio del joystick.",
        ["superrate"] = "SuperTasa",
        ["help_table_2_p3"] = "Expo: Reduce la sensibilidad cerca del centro del joystick donde hacen falta controles finos.",
        ["raceflight"] = "RACEFLIGHT",
        ["yaw"] = "Dirección",
        ["collective"] = "Colectivo",
        ["name"] = "Tasas",
        ["help_table_5_p3"] = "Expo: Reduce la sensibilidad cerca del centro del joystick donde hacen falta controles finos.",
        ["help_table_3_p3"] = "Curva RC: Reduce la sensibilidad cerca del centro del joystick donde hacen falta controles finos.",
        ["expo"] = "Expo",
        ["help_table_1_p2"] = "SuperTasa: Aumenta la tasa máxima de rotación al tiempo que reduce la sensibilidad cerca del desplazamiento medio del joystick.",
        ["help_default_p2"] = "Usaremos las teclas que siguen abajo.",
        ["help_default_p1"] = "Por Defecto: Mantenemos esto para hacer que el botón esté disponible para tasas.",
        ["quick"] = "QUICK",
        ["pitch"] = "Cabeceo",
        ["acroplus"] = "Acro+",
        ["help_table_1_p1"] = "Tasa RC: Tasa de rotación máxima durante el desplazamiento máximo del joystick.",
        ["rc_rate"] = "Tasa RC",
        ["help_table_2_p1"] = "Tasa: Tasa de rotación máxima durante el desplazamiento máximo del joystick en grados por segundo.",
        ["help_table_4_p1"] = "Sensibilidad Central: Para reducir sensibilidad cerca del centro del joystick. Configure la Sensibilidad Central igual que Tasa Máx para una respuesta lineal. Un valor menor que Tasa Máx reducirá la sensibilidad cerca del centro del joystick. Si es mayor que Tasa Máx se incrementa la Tasa Máx - No se recomienda porque causa problemas en el log de la Caja Negra.",
        ["help_table_0_p1"] = "Todos los valores en cero porque no hay TABLA DE TASAS en uso.",
        ["help_table_3_p1"] = "Tasa RC: Tasa de rotación máxima durante el desplazamiento máximo del joystick."
      },
      ["mixer"] = {
        ["help_p1"] = "Ajuste de geometría del swashplate, ángulos de fase, y límites.",
        ["collective_tilt_correction_pos"] = "Positivo",
        ["geo_correction"] = "Geo-Corrección",
        ["swash_tta_precomp"] = "Precomp TTA",
        ["name"] = "Mezclador",
        ["collective_tilt_correction_neg"] = "Negativo",
        ["tail_motor_idle"] = "% Acel Ralentí Cola",
        ["swash_phase"] = "Angulo de Fase",
        ["collective_tilt_correction"] = "Corrección Inclinación Colectivo",
        ["swash_pitch_limit"] = "Límite Total Cabeceo"
      },
      ["about"] = {
        ["help_p1"] = "Esta página provee información que le puede ser requerida cuando solicite soporte técnico.",
        ["msgbox_credits"] = "Créditos",
        ["ethos_version"] = "Versión de Ethos",
        ["rf_version"] = "Versión de Rotorflight",
        ["fc_version"] = "Versión de FC",
        ["name"] = "Acerca de",
        ["supported_versions"] = "Ver. MSP Soportadas",
        ["license"] = "Puede copiar, distribuir, y modificar este software siempre y cuando se comprometa a marcar/fechar los cambios en el código fuente. Todas las modificaciones a nuestro software incluyendo código licenciado mediante GPL (via compilador) deben ser distribuidas bajo licencia GPL junto con instrucciones de compilación e instalación.",
        ["simulation"] = "Simulación",
        ["help_p2"] = "Para obtener soporte por favor lea primero las páginas de ayuda en www.rotorflight.org",
        ["opener"] = "Rotorflight es un proyecto de código abierto. Contribuciones de gente que piensa como nosotros, entusiasta en ayudar a mejorar este software, es bienvenida y motivada. No hace falta que que sea un programador de élite para ayudar.",
        ["version"] = "Versión",
        ["msp_version"] = "Versión de MSP",
        ["credits"] = "Contribuyentes notables tanto para el firmware de Rotorflight como a este software son: Petri Mattila, Egon Lubbers, Rob Thomson, Rob Gayle, Phil Kaighin, Robert Burrow, Keith Williams, Bertrand Songis, Venbs Zhou... y muchos más que han dedicado horas probando y proveyendo feedback! Traducción al Español: Pablo Montoreano",
        ["msp_transport"] = "Transporte MSP"
      },
      ["rates_advanced"] = {
        ["dyn_ceiling_gain"] = "Tope Ganancia",
        ["acc_limit"] = "Límite Acel.",
        ["roll"] = "Alabeo",
        ["yaw_dynamics"] = "Dinám. de Dirección",
        ["pitch"] = "Cabeceo",
        ["col"] = "Colec.",
        ["setpoint_boost_cutoff"] = "Corte Ref.",
        ["yaw_dynamic_deadband_gain"] = "Banda Muerta Rot.",
        ["rates_type"] = "Tipo de Tasas",
        ["setpoint_boost_gain"] = "Ganancia Ref.",
        ["msg_reset_to_defaults"] = "Cambió el tipo de tasa. Los valores se cambiaron a los por defecto.",
        ["yaw_dynamic_ceiling_gain"] = "Tope Dirección",
        ["yaw_boost"] = "Impulso de Dirección",
        ["gain"] = "Ganancia",
        ["rate_table"] = "Tabla de Tasas",
        ["dynamics"] = "Dinámica",
        ["yaw"] = "Dirección",
        ["yaw_dynamic_deadband_filter"] = "Filtro Rot.",
        ["name"] = "Tasas",
        ["cutoff"] = "Límite",
        ["help_rate_table"] = "Seleccione la tasa que desea utilizar. Al guardar se aplicará la elección al perfil activo.",
        ["help_p1"] = "Tipo de Tasas: Seleccione el tipo de tasa con la que prefiere volar. Raceflight y Actual son las mas directas.",
        ["pitch_boost"] = "Impulso de cabeceo",
        ["help_p2"] = "Dinámicas: Se aplican independientemente de los tipos de tasas. Normalmente se dejan los valores por defecto, pero pueden ser ajustados para suavizar los movimientos del heli, como es el caso de los modelos a escala.",
        ["accel_limit"] = "Acel.",
        ["dyn_deadband_filter"] = "Filtro Zona Muerta",
        ["roll_boost"] = "Impulso de alabeo",
        ["dyn_deadband_gain"] = "Gan. Zona Muerta",
        ["collective_dynamics"] = "Dinám. de Colectivo",
        ["roll_dynamics"] = "Dinámica de Alabeo",
        ["collective_boost"] = "Impulso colectivo",
        ["pitch_dynamics"] = "Dinám. de Cabeceo",
        ["response_time"] = "T. Respuesta"
      },
      ["servos"] = {
        ["tbl_yes"] = "SI",
        ["enable_servo_override"] = "Activar toma de servo",
        ["disabling_servo_override"] = "Desactivando toma de servo...",
        ["help_tool_p3"] = "Mínimo/Máximo: Ajuste de los puntos límites del servo seleccionado.",
        ["tail"] = "COLA",
        ["scale_negative"] = "Escala Negativa",
        ["help_tool_p1"] = "Toma de servo: [*] Active la toma de control del servo para permitir la actualización en tiempo real del punto central del servo.",
        ["tbl_no"] = "NO",
        ["maximum"] = "Máximo",
        ["help_tool_p6"] = "Velocidad: La velocidad a la que se mueve el servo. Generalmente se utiliza solamente para los servos del cíclico para ayudar a que el swashplate se mueva en forma pareja. Opcional - deje todo en 0 si no está seguro.",
        ["help_fields_rate"] = "Tasa PWM del servo.",
        ["cyc_pitch"] = "PASO CICL.",
        ["center"] = "Centro",
        ["minimum"] = "Mínimo",
        ["speed"] = "Velocidad",
        ["help_fields_speed"] = "Velocidad de movimiento del servo en milisegundos.",
        ["disable_servo_override"] = "Desactivar toma de servo",
        ["help_fields_scale_pos"] = "Escala positiva del servo.",
        ["saving_data"] = "Guardando datos...",
        ["cyc_left"] = "CICL. IZQ",
        ["saving"] = "Guardando",
        ["name"] = "Servos",
        ["help_tool_p5"] = "Tasa: La frecuencia a la que el servo trabaja mejor. - consulte al fabricante.",
        ["help_tool_p2"] = "Centro: Ajuste de la posición central del servo.",
        ["enabling_servo_override"] = "Activando toma de servo...",
        ["servo_prefix"] = "SERVO ",
        ["reverse"] = "Invertir sentido",
        ["enable_servo_override_msg"] = "La toma de control del servo permite ajustar en tiempo real el punto central de su servo.",
        ["cyc_right"] = "CICL. DER",
        ["help_default_p2"] = "Los controles primarios que utilizan el mezclador de Rotorflight aparecen en la sección llamada 'mezclador'.",
        ["scale_positive"] = "Escala Positiva",
        ["help_default_p1"] = "Seleccione el servo que desea configurar en la lista que sigue.",
        ["servo_override"] = "Toma de Servo",
        ["disable_servo_override_msg"] = "Devolver el control de los servos al controlador de vuelo.",
        ["help_fields_min"] = "Límite de desplazamiento negativo del servo.",
        ["help_default_p3"] = "Aquellos servos que no están controlados por el controlador de vuelo primario aparecen en la sección llamada 'Otros servos'.",
        ["help_fields_mid"] = "Ancho del pulso en la posición central del servo.",
        ["help_fields_scale_neg"] = "Escala negativa del servo.",
        ["rate"] = "Tasa",
        ["help_tool_p4"] = "Escala: Ajuste de la cantidad de movimiento del servo en función del movimiento del comamdo.",
        ["help_fields_flags"] = "0 = Por defecto, 1 = Reversa, 2 = Corrección-Geo, 3 = Reversa + Corrección-Geo",
        ["geometry"] = "Corrección Geometría",
        ["help_fields_max"] = "Límite de desplazamiento positivo del servo."
      },
      ["profile_autolevel"] = {
        ["acro_trainer"] = "Entrenador Acro",
        ["angle_mode"] = "Modo Angulo",
        ["max"] = "Máx",
        ["name"] = "Autonivelado",
        ["help_p1"] = "Entrenador Acro: Que tan agresivamente el helicótero vuelve a nivel en el modo Entrenador Acro.",
        ["horizon_mode"] = "Modo Horizonte",
        ["gain"] = "Ganancia",
        ["help_p2"] = "Modo Angulo: Que tan agresivamente el helicótero vuelve a nivel en el modo Angulo.",
        ["help_p3"] = "Modo Horizonte: Que tan agresivamente el helicótero vuelve a nivel en el modo Horizonte."
      },
      ["filters"] = {
        ["filter_type"] = "Tipo de Filtro",
        ["help_p4"] = "Puntos de Filtros Dinámicos: Crea automáticamente los puntos de filtro dentro del rango de frecuencias mín y máx",
        ["notch_c"] = "Nro. de Puntos",
        ["rpm_preset"] = "Tipo",
        ["lowpass_1"] = "Pasabajos 1",
        ["rpm_min_hz"] = "Frecuencia Mín.",
        ["help_p2"] = "Pasabajos Gyro: Filtros pasabajos para la señal del giroscopio. Normalmente se dejan los valores por defecto.",
        ["cutoff"] = "Corte",
        ["notch_1"] = "Punto 1",
        ["max_cutoff"] = "Corte Máx",
        ["help_p3"] = "Puntos de filtro de Gyro: Utilizado para filtrar rangos específicos de frecuencia. Generalmente no se necesitan en la mayoría de los helicópteros.",
        ["lowpass_2"] = "Pasabajos 2",
        ["rpm_filter"] = "Filtro RPM",
        ["help_p1"] = "Normalente no se edita esta página sin verificar los logs de la Caja Negra!",
        ["dyn_notch"] = "Filtros Dinámicos",
        ["notch_q"] = "Punto Q",
        ["lowpass_1_dyn"] = "Pasabajos 1 din.",
        ["notch_min_hz"] = "Mín",
        ["notch_max_hz"] = "Max",
        ["notch_2"] = "Punto 2",
        ["name"] = "Filtros",
        ["min_cutoff"] = "Corte Mín",
        ["center"] = "Centro"
      },
      ["status"] = {
        ["arming_disable_flag_3"] = "Falla Recup. RX",
        ["arming_disable_flag_20"] = "Filtro RPM",
        ["arming_disable_flag_11"] = "Carga",
        ["arming_disable_flag_22"] = "DSHOT Bitbang",
        ["dataflash_free_space"] = "Espacio libre en flash",
        ["arming_disable_flag_25"] = "Botón Armado",
        ["erasing"] = "Borrando",
        ["arming_disable_flag_9"] = "Tiempo gracia inicio",
        ["megabyte"] = "MB",
        ["arming_disable_flag_17"] = "Paralizar",
        ["arming_disable_flag_5"] = "Governor",
        ["arming_disable_flag_8"] = "Angulo",
        ["arming_disable_flag_1"] = "A prueba de fallos",
        ["cpu_load"] = "Carga de CPU",
        ["arming_disable_flag_15"] = "BST",
        ["arming_disable_flag_12"] = "Calibrando",
        ["arming_disable_flag_19"] = "Resc",
        ["arming_disable_flag_4"] = "Pérdida Box",
        ["arming_disable_flag_24"] = "Protocolo Motor",
        ["real_time_load"] = "Carga Tiempo-Real",
        ["help_p2"] = "Para borrar la flash de datos y guardar nuevos LOGs, oprima el botón del menú marcado con '*'.",
        ["arming_disable_flag_2"] = "Pérdida RX",
        ["ok"] = "OK",
        ["arming_disable_flag_0"] = "No hay Gyro",
        ["arming_disable_flag_18"] = "GPS",
        ["help_p1"] = "Utilice esta página para ver el estado del controlador de vuelo. Puede ser útil para determinar la razón por la que el heli no se puede armar.",
        ["arming_flags"] = "Flags de Armado",
        ["unsupported"] = "No soportado",
        ["erase_prompt"] = "Quiere borrar la flash de datos?",
        ["erase"] = "Borrar",
        ["arming_disable_flag_10"] = "No Pre Armado",
        ["arming_disable_flag_21"] = "Reinicio requerido",
        ["name"] = "Estado",
        ["arming_disable_flag_13"] = "CLI",
        ["arming_disable_flag_14"] = "Menú CMS",
        ["arming_disable_flag_16"] = "MSP",
        ["arming_disable_flag_7"] = "Acelerador",
        ["erasing_dataflash"] = "Borrando flash de datos...",
        ["arming_disable_flag_23"] = "Calibración Acc."
      },
      ["profile_pidbandwidth"] = {
        ["help_p1"] = "Ancho Banda PID: Es el ancho de banda en Hertz utilizado por el loop PID.",
        ["pitch"] = "P",
        ["yaw"] = "Y",
        ["name"] = "Ancho Banda PID",
        ["bterm_cutoff"] = "Corte B-term",
        ["help_p3"] = "Corte B-term: Frecuencia en Hz del corte B-term.",
        ["dterm_cutoff"] = "Corte D-term",
        ["help_p2"] = "Corte D-term: Frecuencia en Hz del corte D-term.",
        ["roll"] = "R"
      }
    },
    ["navigation_save"] = "Guarda",
    ["menu_section_flight_tuning"] = "Ajustes de Vuelo",
    ["error_timed_out"] = "Error: tiempo agotado",
    ["check_rf_module_on"] = "Verifique que el módulo RF esté encendido.",
    ["msg_saving"] = "Guardando...",
    ["msg_save_not_commited"] = "No se copió a EEPROM",
    ["menu_section_advanced"] = "Advanzado",
    ["msg_loading_from_fbl"] = "Cargando datos del controlador de vuelo...",
    ["msg_reload_settings"] = "Recargar datos del controlador de vuelo?",
    ["menu_section_tools"] = "Herramientas",
    ["msg_connecting"] = "Conectando",
    ["msg_save_current_page"] = "Guarda esta página en el controlador de vuelo?",
    ["btn_ok_long"] = "                OK                ",
    ["check_discovered_sensors"] = "Verifique que ha descubierto todos los sensores.",
    ["msg_loading"] = "Cargando...",
    ["check_heli_on"] = "Verifique que el helicóptero esté encendido y el radio conectado.",
    ["check_bg_task"] = "Habilite tarea en segundo plano (en LUA del modelo).",
    ["navigation_tools"] = "*",
    ["check_supported_version"] = "Esta versión del script Lua \nno se puede utilizar con el modelo seleccionado"
  },
  ["telemetry"] = {
    ["sensors"] = {
      ["attpitch"] = "Angulo P.",
      ["attroll"] = "Angulo R.",
      ["attyaw"] = "Angulo Y.",
      ["accx"] = "Acel. X",
      ["accy"] = "Acel. Y",
      ["accz"] = "Acel. Z",
      ["groundspeed"] = "Velocidad Avance",
      ["esc_temp"] = "Temperatura ESC",
      ["rate_profile"] = "Perfil Tasas",
      ["headspeed"] = "Veloc. Rotor",
      ["altitude"] = "Altitud",
      ["voltage"] = "Voltaje",
      ["bec_voltage"] = "Voltaje BEC",
      ["cell_count"] = "Nro de Celdas",
      ["governor"] = "Estado Governor",
      ["adj_func"] = "Ajus.(Función)",
      ["fuel"] = "Nivel de Carga",
      ["smartfuel"] = "Smart Fuel",
      ["rssi"] = "RSSI",
      ["link"] = "Calidad de Enlace",
      ["adj_val"] = "Ajus.(Valor)",
      ["arming_flags"] = "Banderas Armado",
      ["current"] = "Corriente",
      ["throttle_pct"] = "% Acelerador",
      ["consumption"] = "Consumo",
      ["smartconsumption"] = "Consumo Inteligente",
      ["pid_profile"] = "Perfil PID",
      ["mcu_temp"] = "Temperatura MCU",
      ["armdisableflags"] = "Desactivar Armado"
    }
  },
  ["widgets"] = {
    ["bbl"] = {
      ["erase_dataflash"] = "Borrar memoria flash",
      ["erasing"] = "Borrando...",
      ["display"] = "Pantalla",
      ["display_free"] = "Libre",
      ["display_used"] = "Usado",
      ["display_outof"] = "Usado/Total"
    },
    ["craftimage"] = {
    },
    ["craftname"] = {
      ["txt_enter_craft_name"] = "Ingrese el Nombre de la Aeronave",
      ["title"] = "NOMBRE DE LA AERONAVE",
      ["txt_cancel"] = "Cancelar",
      ["txt_save"] = "Guardar"
    },
    ["dashboard"] = {
      ["theme_load_error"] = "El Tema no se cargó correctamente. Cargando Tema por defecto.",
      ["validate_sensors"] = "COMPROBAR SENSORES",
      ["unsupported_resolution"] = "DEMASIADO BAJA",
      ["loading"] = "ROTORFLIGHT",
      ["waiting_for_connection"] = "CONECTANDO",
      ["check_bg_task"] = "TAREA SP",
      ["check_rf_module_on"] = "MODULO RF",
      ["check_discovered_sensors"] = "SENSORES",
      ["no_link"] = "SIN CONEXION",
      ["reset_flight"] = "Reiniciar vuelo",
      ["reset_flight_ask_title"] = "Reiniciar vuelo",
      ["reset_flight_ask_text"] = "Seguro que quiere reiniciar el vuelo?",
      ["voltage"] = "Voltaje",
      ["fuel"] = "Combustible",
      ["headspeed"] = "Velocidad Rotor",
      ["max"] = "Máx",
      ["min"] = "Mín",
      ["bec_voltage"] = "Voltaje BEC",
      ["esc_temp"] = "Temp ESC",
      ["flight_duration"] = "Duración Vuelo",
      ["total_flight_duration"] = "Duración Total Vuelo Modelo",
      ["rpm_min"] = "Mín RPM",
      ["rpm_max"] = "Máx RPM",
      ["throttle_max"] = "Acelerador Máx",
      ["current_max"] = "Corriente Máx",
      ["esc_max_temp"] = "Temp Máx ESC",
      ["watts_max"] = "Watts Máx",
      ["consumed_mah"] = "mAh consumidos",
      ["fuel_remaining"] = "Combustible Restante",
      ["min_volts_cell"] = "Volts Mín por celda",
      ["link_min"] = "Enlace Mín",
      ["governor"] = "Governor",
      ["profile"] = "Perfil",
      ["rates"] = "Tasas",
      ["flights"] = "Vuelos",
      ["lq"] = "LQ",
      ["time"] = "Tiempo",
      ["blackbox"] = "Caja Negra",
      ["throttle"] = "Acelerador",
      ["flight_time"] = "Tiempo Vuelo",
      ["rssi_min"] = "Mín RSSI",
      ["current"] = "Corriente",
      ["timer"] = "Timer",
      ["rpm"] = "RPM",
      ["min_voltage"] = "Voltaje Mín",
      ["max_voltage"] = "Voltaje Máx",
      ["min_current"] = "Corriente Mín",
      ["max_current"] = "Corriente Máx",
      ["max_tmcu"] = "Máx T.MCU",
      ["max_emcu"] = "Máx E.MCU",
      ["altitude"] = "Altitud",
      ["altitude_max"] = "Altitud Máx",
      ["power"] = "Potencia",
      ["cell_voltage"] = "Voltaje Celda",
      ["volts_per_cell"] = "Volts por celda",
      ["warning"] = "Advertencia",
      ["tx_batt"] = "TX Battery",
      ["link_max"] = "Link Max"
    },
    ["governor"] = {
      ["UNKNOWN"] = "DESCONOCIDO",
      ["IDLE"] = "RALENTI",
      ["DISARMED"] = "DESARMADO",
      ["OFF"] = "APAGADO",
      ["SPOOLUP"] = "ARRANQUE",
      ["ACTIVE"] = "ACTIVO",
      ["RECOVERY"] = "RECUPERACION",
      ["THROFF"] = "ACEL-APAG",
      ["LOSTHS"] = "PERDIDA-HS",
      ["AUTOROT"] = "AUTOROT",
      ["DISABLED"] = "DESHABIL.",
      ["BAILOUT"] = "SALVATAJE"
    }
  }
}
